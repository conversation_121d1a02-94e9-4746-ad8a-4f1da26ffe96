import React from 'react';
import { useTranslation } from 'react-i18next';
import { FaBolt, FaMoneyBillWave } from 'react-icons/fa';

const MONTHS = [
  'January', 'February', 'March', 'April', 'May', 'June',
  'July', 'August', 'September', 'October', 'November', 'December'
];

function EnergyBillingInputs({ billing, setBilling }) {
  const { t } = useTranslation();
  // billing: { mode, consumptionType, fixedValue, customTariff, monthlyValues }
  // setBilling: function to update billing state

  const handleModeChange = (e) => {
    setBilling(b => ({ ...b, mode: e.target.value }));
  };
  const handleConsumptionTypeChange = (e) => {
    setBilling(b => ({ ...b, consumptionType: e.target.value }));
  };
  const handleFixedValueChange = (e) => {
    setBilling(b => ({ ...b, fixedValue: e.target.value }));
  };
  const handleCustomTariffChange = (e) => {
    setBilling(b => ({ ...b, customTariff: e.target.value }));
  };
  const handleMonthlyValueChange = (idx, value) => {
    setBilling(b => {
      const monthlyValues = [...b.monthlyValues];
      monthlyValues[idx] = value;
      return { ...b, monthlyValues };
    });
  };

  return (
    <div style={{
      background: 'var(--color-surface)',
      borderRadius: '12px',
      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
      padding: '24px',
      marginTop: '24px'
    }}>
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        marginBottom: '20px',
        borderBottom: '2px solid var(--color-border)',
        paddingBottom: '12px'
      }}>
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          width: '40px',
          height: '40px',
          borderRadius: '50%',
          background: 'linear-gradient(135deg, #eab308, #f59e0b)',
          marginRight: '12px',
          boxShadow: '0 2px 6px rgba(234, 179, 8, 0.3)'
        }}>
          <FaBolt size={20} color="var(--color-text)" />
        </div>
        <h2 style={{ 
          margin: '0', 
          color: 'var(--color-text)', 
          fontSize: '1.5rem', 
          fontWeight: '700'
        }}>{t('Energy Consumption & Billing')}</h2>
      </div>
      
      <div style={{ display: 'flex', flexWrap: 'wrap', gap: '20px' }}>
        <div style={{ flex: '1 1 250px' }}>
          <label style={{ fontWeight: '600', fontSize: '0.9rem', color: '#4b5563', display: 'block', marginBottom: '8px' }}>
            {t('Input Method')}
          </label>
          <select 
            value={billing.mode} 
            onChange={handleModeChange}
            style={{
              padding: '10px 12px',
              borderRadius: '8px',
              border: '1px solid #d1d5db',
              fontSize: '1rem',
              width: '100%',
              boxSizing: 'border-box',
              appearance: 'none',
              backgroundImage: 'url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23131313%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E")',
              backgroundRepeat: 'no-repeat',
              backgroundPosition: 'right 12px top 50%',
              backgroundSize: '12px auto',
              cursor: 'pointer'
            }}
          >
            <option value="average-bill">{t('Average Monthly Bill')}</option>
            <option value="average-consumption">{t('Average Monthly Consumption')}</option>
            <option value="variable-bill">{t('Variable Monthly Bills')}</option>
            <option value="variable-consumption">{t('Variable Monthly Consumption')}</option>
          </select>
        </div>
        
        <div style={{ flex: '1 1 250px' }}>
          <label style={{ fontWeight: '600', fontSize: '0.9rem', color: '#4b5563', display: 'block', marginBottom: '8px' }}>
            {t('Consumption Type')}
          </label>
          <select 
            value={billing.consumptionType} 
            onChange={handleConsumptionTypeChange}
            style={{
              padding: '10px 12px',
              borderRadius: '8px',
              border: '1px solid #d1d5db',
              fontSize: '1rem',
              width: '100%',
              boxSizing: 'border-box',
              appearance: 'none',
              backgroundImage: 'url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23131313%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E")',
              backgroundRepeat: 'no-repeat',
              backgroundPosition: 'right 12px top 50%',
              backgroundSize: '12px auto',
              cursor: 'pointer'
            }}
          >
            <option value="residential">{t('Residential')}</option>
            <option value="commercial">{t('Commercial')}</option>
            <option value="low_voltage">{t('Low Voltage Use')}</option>
            <option value="medium_voltage">{t('Medium Voltage Use')}</option>
            <option value="irrigation_low_voltage">{t('Irrigation (Low Voltage)')}</option>
            <option value="custom_tariff">{t('Custom Tariff')}</option>
          </select>
        </div>
      </div>
      
      {billing.consumptionType === 'custom_tariff' && (
        <div style={{ marginTop: '20px' }}>
          <label style={{ fontWeight: '600', fontSize: '0.9rem', color: '#4b5563', display: 'block', marginBottom: '8px' }}>
            {t('Custom Tariff Rate (EGP per kWh)')}
          </label>
          <input
            type="number"
            min="0"
            step="0.01"
            value={billing.customTariff}
            onChange={handleCustomTariffChange}
            style={{
              padding: '10px 12px',
              borderRadius: '8px',
              border: '1px solid #d1d5db',
              fontSize: '1rem',
              width: '100%',
              maxWidth: '250px',
              boxSizing: 'border-box'
            }}
          />
        </div>
      )}
      
      {(billing.mode === 'average-bill' || billing.mode === 'average-consumption') && (
        <div style={{ 
          marginTop: '20px',
          padding: '16px',
          background: 'var(--color-surface-dark)',
          borderRadius: '8px',
          border: '1px solid var(--color-border)'
        }}>
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            marginBottom: '12px'
          }}>
            {billing.mode === 'average-bill' ? (
              <FaMoneyBillWave size={18} color="#10b981" style={{ marginRight: '8px' }} />
            ) : (
              <FaBolt size={18} color="#eab308" style={{ marginRight: '8px' }} />
            )}
            <label style={{ fontWeight: '600', fontSize: '1rem', color: 'var(--color-text)' }}>
              {billing.mode === 'average-bill' ? t('Average Monthly Bill (EGP)') : t('Average Monthly Consumption (kWh)')}
            </label>
          </div>
          <input
            type="number"
            min="0"
            value={billing.fixedValue}
            onChange={handleFixedValueChange}
            style={{
              padding: '12px',
              borderRadius: '8px',
              border: '1px solid #d1d5db',
              fontSize: '1.1rem',
              width: '100%',
              boxSizing: 'border-box',
              boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
            }}
          />
        </div>
      )}
      
      {(billing.mode === 'variable-bill' || billing.mode === 'variable-consumption') && (
        <div style={{ 
          marginTop: '20px',
          padding: '16px',
          background: 'var(--color-surface-dark)',
          borderRadius: '8px',
          border: '1px solid var(--color-border)'
        }}>
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            marginBottom: '16px'
          }}>
            {billing.mode === 'variable-bill' ? (
              <FaMoneyBillWave size={18} color="#10b981" style={{ marginRight: '8px' }} />
            ) : (
              <FaBolt size={18} color="#eab308" style={{ marginRight: '8px' }} />
            )}
            <label style={{ fontWeight: '600', fontSize: '1rem', color: 'var(--color-text)' }}>
              {billing.mode === 'variable-bill' ? t('Monthly Bills (EGP)') : t('Monthly Consumption (kWh)')}
            </label>
          </div>
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fill, minmax(140px, 1fr))', 
            gap: '16px'
          }}>
            {MONTHS.map((month, idx) => (
              <div key={month}>
                <label style={{ 
                  fontSize: '0.85rem', 
                  fontWeight: '600', 
                  color: '#4b5563', 
                  display: 'block',
                  marginBottom: '6px'
                }}>
                  {t(month)}
                </label>
                <input
                  type="number"
                  min="0"
                  value={billing.monthlyValues[idx] || ''}
                  onChange={e => handleMonthlyValueChange(idx, e.target.value)}
                  style={{ 
                    width: '100%',
                    padding: '8px 10px',
                    borderRadius: '6px',
                    border: '1px solid #d1d5db',
                    fontSize: '0.95rem',
                    boxSizing: 'border-box'
                  }}
                />
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

export default EnergyBillingInputs;
