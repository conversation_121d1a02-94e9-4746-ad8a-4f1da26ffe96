import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { FaLeaf, FaCalculator } from 'react-icons/fa';
import OpenLayersMap from './components/OpenLayersMap';
import SystemParameters from './components/SystemParameters';
import EnergyBillingInputs from './components/EnergyBillingInputs';
import OptimalSizeModal from './components/OptimalSizeModal';
// import ResultsTable from './components/ResultsTable';
import SmartMonthlyBreakdownWithChart from './components/SmartMonthlyBreakdownWithChart';
import ResultsChart from './components/ResultsChart';
import FinancialSummary from './components/FinancialSummary';
import CarbonSummary from './components/CarbonSummary';
import FinancialInputs from './components/FinancialInputs';
import FinancialCharts from './components/FinancialCharts';

import FinancialParametersPopup from './components/FinancialParametersPopup';
import CarbonFactorsModal from './components/CarbonFactorsModal';
import HeaderBar from './components/HeaderBar';

function App() {
  const { t } = useTranslation();
  const [location, setLocation] = useState({ lat: 30.0444, lng: 31.2357 });
  const [params, setParams] = useState({
    peakPower: 5,
    losses: 14,
    tilt: 25,
    azimuth: 0,
  });
  const [billing, setBilling] = useState({
    mode: 'average-bill',
    consumptionType: 'residential',
    fixedValue: '',
    customTariff: '',
    monthlyValues: Array(12).fill(''),
  });

  const [showOptimalModal, setShowOptimalModal] = useState(false);

  const handleOptimalSize = () => {
    setShowOptimalModal(true);
  };

  const handleApplyOptimal = ({ peakPower, fixedValue, consumptionType, customTariff }) => {
    setParams(p => ({ ...p, peakPower }));
    setBilling(b => ({
      ...b,
      fixedValue,
      consumptionType,
      customTariff: consumptionType === 'custom_tariff' ? customTariff : '',
    }));
  };

  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState(null);
  const [financialSummary, setFinancialSummary] = useState(null);
  const [error, setError] = useState(null);

  const [activeResultTab, setActiveResultTab] = useState('financial'); // 'financial' or 'carbon'
  
  // Function to recalculate financial summary when parameters change
  const recalculateFinancials = async (updatedInputs) => {
    if (!results) return;
    
    try {
      const { calculateFinancialSummary } = await import('./utils/financial');
      // Use first year production and tariff as base
      const yearlyProduction = results.monthlyProduction.reduce((a, b) => a + b, 0);
      const yearlyTariff = results.totalBill / (results.yearlyConsumptions.reduce((a, b) => a + b, 0) || 1);
      
      setFinancialSummary(calculateFinancialSummary({
        ...updatedInputs,
        totalCost: Number(updatedInputs.totalCost),
        firstYearMaintenance: Number(updatedInputs.firstYearMaintenance),
        maintenanceAnnualIncrease: Number(updatedInputs.maintenanceAnnualIncrease),
        yearlyTariffIncreaseRate: Number(updatedInputs.yearlyTariffIncreaseRate),
        yearlyDegradationRate: Number(updatedInputs.yearlyDegradationRate),
        startYear: Number(updatedInputs.startYear),
        projectYears: Number(updatedInputs.projectYears),
        gridEmissionFactor: Number(updatedInputs.gridEmissionFactor),
        lcePV: Number(updatedInputs.lcePV),
        lceInverter: Number(updatedInputs.lceInverter),
        lceMount: Number(updatedInputs.lceMount),
        yearlyProduction,
        yearlyTariff,
        peakPower: params.peakPower,
      }));
    } catch (e) {
      console.error('Failed to recalculate financials:', e);
    }
  };
  
  // PV Panel Parameters State
  const [panelLength, setPanelLength] = useState(1.7); // meters
  const [panelWidth, setPanelWidth] = useState(1.0); // meters
  const [panelTilt, setPanelTilt] = useState(25); // degrees
  const [panelOrientation, setPanelOrientation] = useState('portrait'); // 'portrait' or 'landscape'
  const [panelSpacing, setPanelSpacing] = useState('0.03'); // meters, default 0.03m
  const [stackNumber, setStackNumber] = useState(1); // Number of modules stacked in a row (Y direction)
  const [rowSpacing, setRowSpacing] = useState(2.0); // meters, spacing between rows of PV tables

  // Building and table data state
  const [buildingData, setBuildingData] = useState({
    mainRoofArea: 0,
    totalShadedAreaOnRoof: 0
  });
  const [selectedTable, setSelectedTable] = useState(null);
  const [tableUpdateFunction, setTableUpdateFunction] = useState(null);
  const [manualPlacingActions, setManualPlacingActions] = useState(null);

  const [financialInputs, setFinancialInputs] = useState({
    totalCost: 100000,
    firstYearMaintenance: 0,
    maintenanceAnnualIncrease: 0,
    yearlyTariffIncreaseRate: 0,
    yearlyDegradationRate: 0.6, // Default 0.6% per year degradation
    startYear: new Date().getFullYear(), // Current year as default
    projectYears: 25,
    gridEmissionFactor: 0.458,
    lcePV: 1713,
    lceInverter: 303,
    lceMount: 40,
  });
  
  // State for carbon factors modal
  const [showCarbonModal, setShowCarbonModal] = useState(false);
  const [autoPlacementRequestData, setAutoPlacementRequestData] = useState(null);

  // Callback to handle auto-placement request from PanelParametersPopup
  const handleRequestAutoPlacement = (panelParams) => {
    console.log('App.js: Auto-placement requested with params:', panelParams);
    setAutoPlacementRequestData({
      timestamp: Date.now(),
      params: panelParams,
    });
  };

  // Callback handlers for building data and selected table
  const handleBuildingDataChange = useCallback((data) => {
    setBuildingData(data);
  }, []);

  const handleSelectedTableChange = useCallback((table) => {
    setSelectedTable(table);
  }, []);

  // Callback to receive table update function from OpenLayersMap
  const handleTableUpdateFunction = useCallback((updateFunction) => {
    setTableUpdateFunction(() => updateFunction);
  }, []);

  // Callback to handle table property changes from Manual Placing tab
  const handleApplyTableChanges = useCallback((tableId, newProperties) => {
    if (tableUpdateFunction) {
      tableUpdateFunction(tableId, newProperties);
    }
  }, [tableUpdateFunction]);

  // Callback to receive manual placing actions from OpenLayersMap
  const handleManualPlacingActions = useCallback((actions) => {
    setManualPlacingActions(actions);
  }, []);

  return (
    <>
      <HeaderBar />
    <div className="container">

      
      {/* Map and System Parameters in a flex container */}
      <div className="flex-row" style={{
        marginBottom: '24px',
        width: '100%'
      }}>
        {/* Map container - takes 2/3 on large screens, full width on small screens */}
        <div style={{
          flex: '2 1 0%',
          minWidth: '320px',
          maxWidth: window.innerWidth <= 1024 ? '100%' : 'calc(70% - 10px)',
          transition: 'all 0.3s ease'
        }}>
          <OpenLayersMap
            location={location}
            setLocation={setLocation}
            panelLength={panelLength}
            setPanelLength={setPanelLength}
            panelWidth={panelWidth}
            setPanelWidth={setPanelWidth}
            panelTilt={panelTilt}
            setPanelTilt={setPanelTilt}
            panelOrientation={panelOrientation}
            setPanelOrientation={setPanelOrientation}
            panelSpacing={panelSpacing}
            setPanelSpacing={setPanelSpacing}
            stackNumber={stackNumber}
            setStackNumber={setStackNumber}
            autoPlacementRequest={autoPlacementRequestData} // Pass the request data here
            onBuildingDataChange={handleBuildingDataChange}
            onSelectedTableChange={handleSelectedTableChange}
            onTableUpdate={handleTableUpdateFunction}
            onManualPlacingActions={handleManualPlacingActions}
          />
        </div>
        
        {/* System Parameters container - takes 1/3 on large screens, full width on small screens */}
        <div style={{
          flex: '1 1 0%',
          minWidth: '280px',
          maxWidth: window.innerWidth <= 1024 ? '100%' : 'calc(30% - 10px)',
          transition: 'all 0.3s ease'
        }}>

          <div style={{ display: 'flex', flexDirection: 'column' }}>
            <SystemParameters
              params={params}
              setParams={setParams}
              onOptimalSize={handleOptimalSize}
              location={location}
              mainRoofArea={buildingData.mainRoofArea}
              totalShadedAreaOnRoof={buildingData.totalShadedAreaOnRoof}
              selectedTable={selectedTable}
              panelLength={panelLength}
              setPanelLength={setPanelLength}
              panelWidth={panelWidth}
              setPanelWidth={setPanelWidth}
              panelTilt={panelTilt}
              setPanelTilt={setPanelTilt}
              panelOrientation={panelOrientation}
              setPanelOrientation={setPanelOrientation}
              panelSpacing={panelSpacing}
              setPanelSpacing={setPanelSpacing}
              stackNumber={stackNumber}
              setStackNumber={setStackNumber}
              rowSpacing={rowSpacing}
              setRowSpacing={setRowSpacing}
              onApplyParameters={handleRequestAutoPlacement}
              onApplyTableChanges={handleApplyTableChanges}
              manualPlacingActions={manualPlacingActions}
              pvTables={manualPlacingActions?.pvTables || []}
            />
          </div>
        </div>
      </div>
      <EnergyBillingInputs billing={billing} setBilling={setBilling} />
      <OptimalSizeModal
        open={showOptimalModal}
        onClose={() => setShowOptimalModal(false)}
        onApply={handleApplyOptimal}
      />
      <CarbonFactorsModal
        open={showCarbonModal}
        onClose={() => setShowCarbonModal(false)}
        values={financialInputs}
        setValues={setFinancialInputs}
      />
      {/* Removed the standalone financial and carbon buttons as they're now integrated with their respective sections */}
      <button
        style={{ 
          marginTop: 24, 
          padding: window.innerWidth <= 480 ? '10px 20px' : '12px 24px', 
          background: '#2563eb', 
          color: 'var(--color-text)', 
          border: 'none', 
          borderRadius: 6, 
          fontWeight: 600, 
          fontSize: 18, 
          width: '100%', 
          maxWidth: 360, 
          margin: '24px auto 0', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center', 
          gap: 8
        }}
        onClick={async () => {
          setResults(null);
          setError(null);
          // Input validation
          if (
            !location.lat || !location.lng ||
            isNaN(Number(params.peakPower)) || Number(params.peakPower) <= 0 ||
            isNaN(Number(params.losses)) ||
            isNaN(Number(params.tilt)) ||
            isNaN(Number(params.azimuth))
          ) {
            setError('Please enter valid system parameters and select a location.');
            return;
          }
          if (billing.mode === 'average-bill' || billing.mode === 'average-consumption') {
            if (!billing.fixedValue || isNaN(Number(billing.fixedValue)) || Number(billing.fixedValue) < 0) {
              setError('Please enter a valid average value for billing/consumption.');
              return;
            }
          }
          if (billing.mode === 'variable-bill' || billing.mode === 'variable-consumption') {
            if (!billing.monthlyValues.some(v => v && !isNaN(Number(v)))) {
              setError('Please enter at least one valid monthly value.');
              return;
            }
          }
          if (billing.consumptionType === 'custom_tariff' && (!billing.customTariff || isNaN(Number(billing.customTariff)) || Number(billing.customTariff) <= 0)) {
            setError('Please enter a valid custom tariff.');
            return;
          }
          setLoading(true);
          try {
            const { calculateSolarSavings } = await import('./utils/calc');
            // ---- Cost estimation based on system peak power ----
            const { estimateCost } = await import('./components/cost_estimation');
            const estimatedCost = estimateCost(params.peakPower);
            setFinancialInputs(fi => ({ ...fi, totalCost: estimatedCost }));
            // ----------------------------------------------------
            const res = await calculateSolarSavings({ location, params, billing });
            setResults(res);
            // Calculate financial summary
            const { calculateFinancialSummary } = await import('./utils/financial');
            // Use first year production and tariff as base
            const yearlyProduction = res.monthlyProduction.reduce((a, b) => a + b, 0);
            const yearlyTariff = res.totalBill / (res.yearlyConsumptions.reduce((a, b) => a + b, 0) || 1);
            setFinancialSummary(calculateFinancialSummary({
              ...financialInputs,
              totalCost: estimatedCost,
              firstYearMaintenance: Number(financialInputs.firstYearMaintenance),
              maintenanceAnnualIncrease: Number(financialInputs.maintenanceAnnualIncrease),
              yearlyTariffIncreaseRate: Number(financialInputs.yearlyTariffIncreaseRate),
              yearlyDegradationRate: Number(financialInputs.yearlyDegradationRate),
              startYear: Number(financialInputs.startYear),
              projectYears: Number(financialInputs.projectYears),
              gridEmissionFactor: Number(financialInputs.gridEmissionFactor),
              lcePV: Number(financialInputs.lcePV),
              lceInverter: Number(financialInputs.lceInverter),
              lceMount: Number(financialInputs.lceMount),
              yearlyProduction,
              yearlyTariff,
              peakPower: params.peakPower,
            }));
          } catch (e) {
            setError(e.message || 'Calculation failed');
          } finally {
            setLoading(false);
          }
        }}
        disabled={loading}
      >
        {loading ? (<><FaCalculator /> {t('Calculating...')}</>) : (<><FaCalculator /> {t('Calculate Solar Savings')}</>)}
      </button>
      {error && <div style={{ color: 'red', marginTop: 12 }}>{error}</div>}
      {results && (
        <div style={{ marginTop: 32 }}>
          <h2>{t('Results')}</h2>
          <SmartMonthlyBreakdownWithChart results={results} />
          
          {/* Financial and Carbon Summary Tabs */}
          <div style={{ marginTop: 32 }}>
            {/* Tab Buttons */}
            <div style={{ display: 'flex', gap: 4, marginBottom: 16, justifyContent: 'center' }}>
              <button
                onClick={() => setActiveResultTab('financial')}
                style={{
                  padding: '8px 18px',
                  minWidth: 140,
                  border: 'none',
                  background: activeResultTab === 'financial' ? '#2563eb' : '#e5e7eb',
                  color: activeResultTab === 'financial' ? 'var(--color-text)' : '#2563eb',
                  fontWeight: 700,
                  fontSize: 15,
                  cursor: 'pointer',
                  margin: '0 4px',
                  boxShadow: activeResultTab === 'financial' ? '0 2px 8px rgba(37, 99, 235, 0.12)' : 'none',
                  transition: '0.15s',
                  position: 'relative',
                  outline: 'none',
                  transform: 'skew(-20deg)',
                  borderRadius: '4px 18px',
                  boxSizing: 'border-box',
                  zIndex: activeResultTab === 'financial' ? 2 : 1,
                }}
              >
                <span style={{ display: 'inline-block', transform: 'skew(20deg)' }}>
                  <span style={{ display: 'inline-flex', alignItems: 'center', gap: 4 }}>
                    <span style={{ display: 'inline-block', transform: 'skew(-20deg)', background: activeResultTab === 'financial' ? '#2563eb' : '#2563eb', color: 'var(--color-text)', padding: '2px 8px', borderRadius: 6, marginRight: 4 }}>$</span>
                    {t('Financial Summary')}
                  </span>
                </span>
              </button>
              <button
                onClick={() => setActiveResultTab('carbon')}
                style={{
                  padding: '8px 18px',
                  minWidth: 140,
                  border: 'none',
                  background: activeResultTab === 'carbon' ? '#10b981' : '#e5e7eb',
                  color: activeResultTab === 'carbon' ? 'var(--color-text)' : '#2563eb',
                  fontWeight: 700,
                  fontSize: 15,
                  cursor: 'pointer',
                  margin: '0 4px',
                  boxShadow: activeResultTab === 'carbon' ? '0 2px 8px rgba(16, 185, 129, 0.12)' : 'none',
                  transition: '0.15s',
                  position: 'relative',
                  outline: 'none',
                  transform: 'skew(-20deg)',
                  borderRadius: '4px 18px',
                  boxSizing: 'border-box',
                  zIndex: activeResultTab === 'carbon' ? 2 : 1,
                }}
              >
                <span style={{ display: 'inline-block', transform: 'skew(20deg)' }}>
                  <span style={{ display: 'inline-flex', alignItems: 'center', gap: 4 }}>
                    <span style={{ display: 'inline-block', transform: 'skew(-20deg)', background: activeResultTab === 'carbon' ? '#10b981' : '#10b981', color: 'var(--color-text)', padding: '2px 8px', borderRadius: 6, marginRight: 4 }}>🌱</span>
                    {t('Carbon Savings')}
                  </span>
                </span>
              </button>
            </div>
            
            {/* Tab Content */}
            {activeResultTab === 'financial' && (
              <FinancialSummary 
                summary={financialSummary} 
                financialInputs={financialInputs}
                setFinancialInputs={setFinancialInputs}
                recalculateFinancials={recalculateFinancials}
              />
            )}
            
            {activeResultTab === 'carbon' && (
              <CarbonSummary 
                summary={financialSummary} 
                financialInputs={financialInputs}
                setFinancialInputs={setFinancialInputs}
                recalculateFinancials={recalculateFinancials}
              />
            )}
          </div>
        </div>
      )}
    </div>
    </>
  );
}

export default App;
