# Phase 1: Technical Specifications - Turf.js + polygon-clipping Integration

## Overview

Phase 1 focuses on replacing the unreliable <PERSON> polygon clipping library with the robust polygon-clipping library and enhancing Turf.js usage for spatial operations. This phase addresses the most critical issues in the current shadow calculation system.

## 1.1: Install and Configure polygon-clipping Library

### Library Comparison

| Feature | martinez-polygon-clipping | polygon-clipping |
|---------|---------------------------|------------------|
| Maintenance | Inactive (last update 2019) | Active (regular updates) |
| Reliability | Known issues with edge cases | Robust handling |
| Performance | Moderate | Optimized |
| Bundle Size | ~45KB | ~35KB |
| TypeScript | No | Yes |

### Installation Steps

```bash
# Remove unreliable Martinez library
npm uninstall martinez-polygon-clipping

# Install robust polygon-clipping library
npm install polygon-clipping

# Install additional Turf.js modules if needed
npm install @turf/boolean-point-in-polygon @turf/area @turf/bbox
```

### Utility Wrapper Implementation

Create `src/utils/geometryOperations.js`:

```javascript
import polygonClipping from 'polygon-clipping';
import { polygon as turfPolygon } from '@turf/helpers';
import area from '@turf/area';
import bbox from '@turf/bbox';

/**
 * Geometry operations utility using polygon-clipping library
 * Replaces martinez-polygon-clipping with more reliable implementation
 */

// Coordinate format conversion utilities
export const convertToPolygonClippingFormat = (leafletCoords) => {
  // Convert from Leaflet [lat, lng] to polygon-clipping [lng, lat]
  return [leafletCoords.map(coord => [coord[1], coord[0]])];
};

export const convertFromPolygonClippingFormat = (polygonClippingResult) => {
  // Convert back to Leaflet [lat, lng] format
  return polygonClippingResult.map(polygon => 
    polygon[0].map(coord => [coord[1], coord[0]])
  );
};

// Core geometric operations
export const unionPolygons = (polygons) => {
  try {
    const formatted = polygons.map(convertToPolygonClippingFormat);
    const result = polygonClipping.union(...formatted);
    return convertFromPolygonClippingFormat(result);
  } catch (error) {
    console.error('Union operation failed:', error);
    throw new GeometryOperationError('Union failed', error);
  }
};

export const intersectPolygons = (polygon1, polygon2) => {
  try {
    const p1 = convertToPolygonClippingFormat(polygon1);
    const p2 = convertToPolygonClippingFormat(polygon2);
    const result = polygonClipping.intersection(p1, p2);
    return convertFromPolygonClippingFormat(result);
  } catch (error) {
    console.error('Intersection operation failed:', error);
    throw new GeometryOperationError('Intersection failed', error);
  }
};

export const differencePolygons = (polygon1, polygon2) => {
  try {
    const p1 = convertToPolygonClippingFormat(polygon1);
    const p2 = convertToPolygonClippingFormat(polygon2);
    const result = polygonClipping.difference(p1, p2);
    return convertFromPolygonClippingFormat(result);
  } catch (error) {
    console.error('Difference operation failed:', error);
    throw new GeometryOperationError('Difference failed', error);
  }
};

// Custom error class for geometry operations
export class GeometryOperationError extends Error {
  constructor(message, originalError) {
    super(message);
    this.name = 'GeometryOperationError';
    this.originalError = originalError;
  }
}

// Validation utilities
export const validatePolygon = (coordinates) => {
  if (!Array.isArray(coordinates) || coordinates.length < 3) {
    return false;
  }
  
  // Check for valid coordinate pairs
  return coordinates.every(coord => 
    Array.isArray(coord) && 
    coord.length === 2 && 
    typeof coord[0] === 'number' && 
    typeof coord[1] === 'number' &&
    !isNaN(coord[0]) && 
    !isNaN(coord[1])
  );
};

export const calculatePolygonArea = (coordinates) => {
  if (!validatePolygon(coordinates)) {
    throw new GeometryOperationError('Invalid polygon for area calculation');
  }
  
  const turfPoly = turfPolygon([coordinates.map(coord => [coord[1], coord[0]])]);
  return area(turfPoly);
};
```

## 1.2: Create Robust Shadow Merging System

### Current Issues with Martinez Implementation

1. **Inconsistent Results**: Martinez sometimes returns empty results for valid intersections
2. **Error Handling**: Poor error handling leads to system crashes
3. **MultiPolygon Support**: Inadequate handling of complex polygon results
4. **Performance**: Slow with large numbers of polygons

### New Shadow Merging Implementation

Replace the existing `mergeShadowPolygons` function in `OpenLayersMap.js`:

```javascript
import { 
  unionPolygons, 
  validatePolygon, 
  GeometryOperationError 
} from '../utils/geometryOperations';

/**
 * Enhanced shadow merging using polygon-clipping library
 * Handles overlapping shadows while preserving isolated ones
 */
const mergeShadowPolygons = (shadowArray) => {
  if (!shadowArray || shadowArray.length === 0) return [];
  if (shadowArray.length === 1) return shadowArray;

  try {
    console.log(`[mergeShadowPolygons] Processing ${shadowArray.length} shadows`);
    
    // Validate all input polygons
    const validShadows = shadowArray.filter(shadow => {
      const isValid = validatePolygon(shadow.coordinates);
      if (!isValid) {
        console.warn(`[mergeShadowPolygons] Invalid shadow polygon:`, shadow.id);
      }
      return isValid;
    });

    if (validShadows.length === 0) {
      console.warn('[mergeShadowPolygons] No valid shadows to merge');
      return [];
    }

    // Extract coordinates for union operation
    const polygonCoords = validShadows.map(shadow => shadow.coordinates);
    
    // Perform union operation
    const mergedPolygons = unionPolygons(polygonCoords);
    
    // Convert results back to shadow format
    const resultShadows = mergedPolygons.map((coords, index) => ({
      id: `merged_shadow_${Date.now()}_${index}`,
      coordinates: coords,
      type: 'aggregated'
    }));

    console.log(`[mergeShadowPolygons] Merged ${shadowArray.length} → ${resultShadows.length} shadows`);
    return resultShadows;

  } catch (error) {
    if (error instanceof GeometryOperationError) {
      console.error('[mergeShadowPolygons] Geometry operation failed:', error.message);
    } else {
      console.error('[mergeShadowPolygons] Unexpected error:', error);
    }
    
    // Fallback: return original shadows
    console.log('[mergeShadowPolygons] Falling back to individual shadows');
    return shadowArray;
  }
};
```

### Smart Merging Algorithm

Implement intelligent merging that preserves isolated shadows:

```javascript
/**
 * Smart shadow merging that groups nearby shadows and preserves isolated ones
 */
const smartMergeShadowPolygons = (shadowArray, proximityThreshold = 0.0001) => {
  if (!shadowArray || shadowArray.length <= 1) return shadowArray;

  try {
    // Group shadows by proximity
    const shadowGroups = groupShadowsByProximity(shadowArray, proximityThreshold);
    
    const mergedResults = [];
    
    for (const group of shadowGroups) {
      if (group.length === 1) {
        // Isolated shadow - keep as is
        mergedResults.push(group[0]);
      } else {
        // Multiple shadows - merge them
        const groupCoords = group.map(shadow => shadow.coordinates);
        const merged = unionPolygons(groupCoords);
        
        merged.forEach((coords, index) => {
          mergedResults.push({
            id: `merged_group_${Date.now()}_${index}`,
            coordinates: coords,
            type: 'aggregated'
          });
        });
      }
    }
    
    return mergedResults;
    
  } catch (error) {
    console.error('[smartMergeShadowPolygons] Error:', error);
    return shadowArray; // Fallback
  }
};

const groupShadowsByProximity = (shadows, threshold) => {
  const groups = [];
  const processed = new Set();
  
  for (let i = 0; i < shadows.length; i++) {
    if (processed.has(i)) continue;
    
    const group = [shadows[i]];
    processed.add(i);
    
    // Find nearby shadows
    for (let j = i + 1; j < shadows.length; j++) {
      if (processed.has(j)) continue;
      
      if (areShadowsNearby(shadows[i], shadows[j], threshold)) {
        group.push(shadows[j]);
        processed.add(j);
      }
    }
    
    groups.push(group);
  }
  
  return groups;
};
```

## 1.3: Fix Building Orientation Handling

### Current Problem

The current implementation doesn't account for building rotation when calculating shadows, leading to incorrect shadow positions and orientations.

### Solution Architecture

```javascript
/**
 * Enhanced shadow calculation with building orientation support
 */
const calculateShadowWithOrientation = (
  obstacleCoords, 
  obstacleHeight,
  buildingAzimuth,
  sunPosition,
  buildingCenter
) => {
  try {
    // 1. Transform coordinates to building-relative coordinate system
    const relativeCoords = transformToBuildingCoordinates(
      obstacleCoords, 
      buildingCenter, 
      buildingAzimuth
    );
    
    // 2. Calculate shadow in building coordinate system
    const relativeShadowCoords = projectShadowInBuildingSpace(
      relativeCoords,
      obstacleHeight,
      sunPosition,
      buildingAzimuth
    );
    
    // 3. Transform back to world coordinates
    const worldShadowCoords = transformToWorldCoordinates(
      relativeShadowCoords,
      buildingCenter,
      buildingAzimuth
    );
    
    return worldShadowCoords;
    
  } catch (error) {
    console.error('[calculateShadowWithOrientation] Error:', error);
    throw new GeometryOperationError('Shadow calculation failed', error);
  }
};

// Coordinate transformation utilities
const transformToBuildingCoordinates = (coords, center, azimuth) => {
  const azimuthRad = (azimuth * Math.PI) / 180;
  const cosAz = Math.cos(-azimuthRad);
  const sinAz = Math.sin(-azimuthRad);
  
  return coords.map(([lat, lng]) => {
    // Translate to origin
    const deltaLat = lat - center.lat;
    const deltaLng = lng - center.lng;
    
    // Rotate
    const rotatedLat = deltaLat * cosAz - deltaLng * sinAz;
    const rotatedLng = deltaLat * sinAz + deltaLng * cosAz;
    
    return [rotatedLat + center.lat, rotatedLng + center.lng];
  });
};

const projectShadowInBuildingSpace = (coords, height, sunPos, buildingAzimuth) => {
  // Adjust sun position for building orientation
  const adjustedSunAzimuth = sunPos.azimuth - (buildingAzimuth * Math.PI / 180);
  
  const shadowPoints = [];
  const tanSunAltitude = Math.tan(sunPos.altitude);
  
  if (tanSunAltitude <= 0) return coords; // No shadow when sun is below horizon
  
  coords.forEach(([lat, lng]) => {
    const shadowLength = height / tanSunAltitude;
    
    // Project shadow using adjusted sun position
    const projectedPoint = L.CRS.EPSG3857.project(L.latLng(lat, lng));
    const shadowEndX = projectedPoint.x - shadowLength * Math.sin(adjustedSunAzimuth);
    const shadowEndY = projectedPoint.y + shadowLength * Math.cos(adjustedSunAzimuth);
    
    const shadowLatLng = L.CRS.EPSG3857.unproject(L.point(shadowEndX, shadowEndY));
    shadowPoints.push([shadowLatLng.lat, shadowLatLng.lng]);
  });
  
  return shadowPoints;
};
```

## 1.4: Comprehensive Error Handling

### Error Classification

1. **Input Validation Errors**: Invalid coordinates, missing data
2. **Geometric Operation Errors**: Library operation failures
3. **Coordinate Transformation Errors**: Projection failures
4. **Performance Errors**: Timeout, memory issues

### Error Handling Implementation

```javascript
/**
 * Comprehensive error handling for shadow calculations
 */
class ShadowCalculationError extends Error {
  constructor(message, type, originalError = null) {
    super(message);
    this.name = 'ShadowCalculationError';
    this.type = type;
    this.originalError = originalError;
    this.timestamp = new Date().toISOString();
  }
}

const ERROR_TYPES = {
  VALIDATION: 'validation',
  GEOMETRY: 'geometry',
  TRANSFORMATION: 'transformation',
  PERFORMANCE: 'performance',
  UNKNOWN: 'unknown'
};

// Enhanced error handling wrapper
const withErrorHandling = (operation, fallback = null) => {
  return async (...args) => {
    try {
      return await operation(...args);
    } catch (error) {
      const errorType = classifyError(error);
      const shadowError = new ShadowCalculationError(
        `Shadow calculation failed: ${error.message}`,
        errorType,
        error
      );
      
      // Log error for debugging
      console.error('[Shadow Calculation Error]', {
        type: errorType,
        message: error.message,
        stack: error.stack,
        timestamp: shadowError.timestamp
      });
      
      // Execute fallback if provided
      if (fallback) {
        console.log('[Shadow Calculation] Executing fallback strategy');
        return fallback(...args);
      }
      
      throw shadowError;
    }
  };
};

const classifyError = (error) => {
  if (error instanceof GeometryOperationError) return ERROR_TYPES.GEOMETRY;
  if (error.message.includes('Invalid coordinates')) return ERROR_TYPES.VALIDATION;
  if (error.message.includes('projection')) return ERROR_TYPES.TRANSFORMATION;
  if (error.message.includes('timeout')) return ERROR_TYPES.PERFORMANCE;
  return ERROR_TYPES.UNKNOWN;
};

// Validation utilities
const validateShadowInputs = (obstacles, pvTables, location, analysisDate) => {
  const errors = [];
  
  if (!obstacles || !Array.isArray(obstacles)) {
    errors.push('Invalid obstacles array');
  }
  
  if (!pvTables || !Array.isArray(pvTables)) {
    errors.push('Invalid PV tables array');
  }
  
  if (!location || typeof location.lat !== 'number' || typeof location.lng !== 'number') {
    errors.push('Invalid location coordinates');
  }
  
  if (!analysisDate || isNaN(new Date(analysisDate).getTime())) {
    errors.push('Invalid analysis date');
  }
  
  if (errors.length > 0) {
    throw new ShadowCalculationError(
      `Validation failed: ${errors.join(', ')}`,
      ERROR_TYPES.VALIDATION
    );
  }
};
```

## Testing Strategy

### Unit Tests

```javascript
// Example test structure for geometry operations
describe('Geometry Operations', () => {
  describe('unionPolygons', () => {
    test('should merge overlapping polygons', () => {
      const polygon1 = [[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]];
      const polygon2 = [[0.5, 0], [1.5, 0], [1.5, 1], [0.5, 1], [0.5, 0]];
      
      const result = unionPolygons([polygon1, polygon2]);
      
      expect(result).toHaveLength(1);
      expect(calculatePolygonArea(result[0])).toBeCloseTo(1.5, 2);
    });
    
    test('should preserve isolated polygons', () => {
      const polygon1 = [[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]];
      const polygon2 = [[2, 2], [3, 2], [3, 3], [2, 3], [2, 2]];
      
      const result = unionPolygons([polygon1, polygon2]);
      
      expect(result).toHaveLength(2);
    });
  });
});
```

### Integration Tests

```javascript
describe('Shadow Merging Integration', () => {
  test('should handle complex shadow scenarios', async () => {
    const shadows = generateTestShadows(10); // Helper function
    const merged = await mergeShadowPolygons(shadows);
    
    expect(merged.length).toBeLessThanOrEqual(shadows.length);
    expect(merged.every(shadow => validatePolygon(shadow.coordinates))).toBe(true);
  });
});
```

## Performance Benchmarks

### Benchmark Implementation

```javascript
const benchmarkShadowMerging = async (shadowCounts) => {
  const results = {};
  
  for (const count of shadowCounts) {
    const shadows = generateTestShadows(count);
    
    const startTime = performance.now();
    const merged = await mergeShadowPolygons(shadows);
    const endTime = performance.now();
    
    results[count] = {
      inputCount: count,
      outputCount: merged.length,
      duration: endTime - startTime,
      memoryUsage: getMemoryUsage()
    };
  }
  
  return results;
};

// Target performance metrics
const PERFORMANCE_TARGETS = {
  10: 50,   // 10 shadows in <50ms
  50: 200,  // 50 shadows in <200ms
  100: 500, // 100 shadows in <500ms
  500: 2000 // 500 shadows in <2s
};
```

## Migration Strategy

### Feature Flag Implementation

```javascript
// Feature flag for new shadow system
const USE_NEW_SHADOW_SYSTEM = process.env.REACT_APP_NEW_SHADOW_SYSTEM === 'true';

const calculateShadows = USE_NEW_SHADOW_SYSTEM 
  ? calculateShadowsNew 
  : calculateShadowsLegacy;
```

### Gradual Rollout Plan

1. **Development**: Enable new system for development builds
2. **Testing**: A/B testing with subset of users
3. **Staging**: Full deployment to staging environment
4. **Production**: Gradual rollout with monitoring
5. **Cleanup**: Remove legacy code after validation

This comprehensive Phase 1 implementation provides a solid foundation for the enhanced shadow calculation system while maintaining backward compatibility and ensuring robust error handling.
