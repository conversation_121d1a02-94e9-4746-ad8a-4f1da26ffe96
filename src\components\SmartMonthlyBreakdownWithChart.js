import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Legend,
  Tooltip,
} from 'chart.js';

ChartJS.register(CategoryScale, LinearScale, BarElement, Legend, Tooltip);

const MONTHS = [
  'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
  'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
];

const UNUSED_TABS = [
  { key: 'energy', label: <><span style={{display:'inline-flex',alignItems:'center',gap:4}}><span style={{display:'inline-block',transform:'skew(-20deg)',background:'#eab308',color:'var(--color-surface)',padding:'2px 8px',borderRadius:6,marginRight:4}}>&#9889;</span>Energy</span></> },
  { key: 'bill', label: <><span style={{display:'inline-flex',alignItems:'center',gap:4}}><span style={{display:'inline-block',transform:'skew(-20deg)',background:'#10b981',color:'var(--color-surface)',padding:'2px 8px',borderRadius:6,marginRight:4}}>&#36;</span>Bills</span></> },
  { key: 'tier', label: <><span style={{display:'inline-flex',alignItems:'center',gap:4}}><span style={{display:'inline-block',transform:'skew(-20deg)',background:'#2563eb',color:'var(--color-surface)',padding:'2px 8px',borderRadius:6,marginRight:4}}>&#128200;</span>Tariffs</span></> },
];

function getRelativeDecreases(beforeArr, afterArr) {
  const decreases = beforeArr.map((before, i) => Math.max(0, before - afterArr[i]));
  const max = Math.max(...decreases, 1);
  return decreases.map(d => d / max);
}

const chartTitles = {
  bill: 'Bill Comparison',
  energy: 'Energy Savings',
};

export default function SmartMonthlyBreakdownWithChart({ results }) {
  const { t } = useTranslation();
  const TABS = [
    { key: 'energy', label: <><span style={{display:'inline-flex',alignItems:'center',gap:4}}><span style={{display:'inline-block',transform:'skew(-20deg)',background:'#eab308',color:'var(--color-surface)',padding:'2px 8px',borderRadius:6,marginRight:4}}>&#9889;</span>{t('Energy')}</span></> },
    { key: 'bill', label: <><span style={{display:'inline-flex',alignItems:'center',gap:4}}><span style={{display:'inline-block',transform:'skew(-20deg)',background:'#10b981',color:'var(--color-surface)',padding:'2px 8px',borderRadius:6,marginRight:4}}>&#36;</span>{t('Bills')}</span></> },
    { key: 'tier', label: <><span style={{display:'inline-flex',alignItems:'center',gap:4}}><span style={{display:'inline-block',transform:'skew(-20deg)',background:'#2563eb',color:'var(--color-surface)',padding:'2px 8px',borderRadius:6,marginRight:4}}>&#128200;</span>{t('Tariffs')}</span></> },
  ];
  const [tab, setTab] = useState('bill');
  if (!results) return null;

  // For visual-only indicators
  const energyRel = getRelativeDecreases(results.yearlyConsumptions, results.newConsumptions);
  const billRel = getRelativeDecreases(results.bills, results.newBills);

  // Chart data
  let chartData, chartOptions;
  const grey = '#b0b0b0';
  if (tab === 'bill') {
    chartData = {
      labels: MONTHS,
      datasets: [
        {
          label: t('Bill Without Solar'),
          data: results.bills,
          backgroundColor: grey,
          borderRadius: 6,
        },
        {
          label: t('Bill With Solar'),
          data: results.newBills,
          backgroundColor: '#10b981',
          borderRadius: 6,
        },
      ],
    };
    chartOptions = {
      responsive: true,
      plugins: { legend: { position: 'top' } },
      scales: {
        y: { beginAtZero: true, ticks: { font: { size: 11 } } },
        x: { ticks: { font: { size: 11 } } },
      },
    };
  } else if (tab === 'energy') {
    chartData = {
      labels: MONTHS,
      datasets: [
        {
          label: t('Energy Need'),
          data: results.yearlyConsumptions,
          backgroundColor: grey,
          borderRadius: 6,
        },
        {
          label: t('PV Production'),
          data: results.monthlyProduction,
          backgroundColor: '#eab308',
          borderRadius: 6,
        },
      ],
    };
    chartOptions = {
      responsive: true,
      plugins: { legend: { position: 'top' } },
      scales: {
        y: { beginAtZero: true, ticks: { font: { size: 11 } } },
        x: { ticks: { font: { size: 11 } } },
      },
    };
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: 16, width: '100%' }}>
      {/* Navigation buttons in the middle of the screen */}
      <div style={{ display: 'flex', gap: 4, justifyContent: 'center', marginBottom: 16 }}>
        {TABS.map(t => (
          <button
            key={t.key}
            onClick={() => setTab(t.key)}
            style={{
              padding: '8px 18px',
              minWidth: 120,
              border: 'none',
              background: 'none',
              color: tab === t.key ? 'var(--color-surface)' : '#2563eb',
              fontWeight: 700,
              fontSize: 15,
              cursor: 'pointer',
              margin: '0 4px',
              boxShadow: tab === t.key ? '0 2px 8px rgba(37,99,235,0.12)' : 'none',
              transition: 'all 0.15s',
              position: 'relative',
              outline: 'none',
              transform: 'skew(-20deg)',
              background: tab === t.key
                ? (t.key === 'energy' ? '#eab308' : t.key === 'bill' ? '#10b981' : '#2563eb')
                : '#e5e7eb',
              borderRadius: 0,
              borderTopRightRadius: 18,
              borderBottomLeftRadius: 18,
              borderBottomRightRadius: 4,
              borderTopLeftRadius: 4,
              boxSizing: 'border-box',
              zIndex: tab === t.key ? 2 : 1,
            }}
          >
            <span style={{display:'inline-block',transform:'skew(20deg)'}}>{t.label}</span>
          </button>
        ))}
      </div>

      {/* Summary information for each tab */}
      <div style={{ background: 'var(--color-surface)', padding: '16px', borderRadius: '8px', marginBottom: '16px', boxShadow: '0 2px 8px rgba(0,0,0,0.04)', width: '100%' }}>
        {tab === 'bill' && (
          <div>
            <h2 style={{ margin: '0 0 12px 0', fontSize: '18px', color: '#10b981', textAlign: 'center' }}>{t('Summary')}</h2>
            <div style={{ display: 'flex', justifyContent: 'space-around', flexWrap: 'wrap', gap: '12px', width: '100%' }}>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '14px', color: 'var(--color-text)' }}>{t('Annual Bill without Solar')}</div>
                <div style={{ fontSize: '18px', fontWeight: 'bold', color: 'var(--color-text)' }}>{results.totalBill.toFixed(2)} EGP</div>
              </div>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '14px', color: 'var(--color-text)' }}>{t('Annual Bill with Solar')}</div>
                <div style={{ fontSize: '18px', fontWeight: 'bold', color: 'var(--color-text)' }}>{results.totalNewBill.toFixed(2)} EGP</div>
              </div>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '14px', color: 'var(--color-text)' }}>{t('Annual Savings')}</div>
                <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#10b981' }}>{(results.totalBill - results.totalNewBill).toFixed(2)} EGP</div>
              </div>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '14px', color: 'var(--color-text)' }}>{t('Savings Percentage')}</div>
                <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#10b981' }}>{results.savingsPercentage.toFixed(2)}%</div>
              </div>
            </div>
          </div>
        )}
        {tab === 'energy' && (
          <div>
            <h2 style={{ margin: '0 0 12px 0', fontSize: '18px', color: '#eab308', textAlign: 'center' }}>{t('Summary')}</h2>
            <div style={{ display: 'flex', justifyContent: 'space-around', flexWrap: 'wrap', gap: '12px', width: '100%' }}>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '14px', color: 'var(--color-text)' }}>{t('Annual Energy Need without Solar')}</div>
                <div style={{ fontSize: '18px', fontWeight: 'bold', color: 'var(--color-text)' }}>{results.yearlyConsumptions.reduce((a, b) => a + b, 0).toFixed(2)} kWh</div>
              </div>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '14px', color: 'var(--color-text)' }}>{t('Annual Energy Need with Solar')}</div>
                <div style={{ fontSize: '18px', fontWeight: 'bold', color: 'var(--color-text)' }}>{results.newConsumptions.reduce((a, b) => a + b, 0).toFixed(2)} kWh</div>
              </div>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '14px', color: 'var(--color-text)' }}>{t('Annual PV Production')}</div>
                <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#eab308' }}>{results.monthlyProduction.reduce((a, b) => a + b, 0).toFixed(2)} kWh</div>
              </div>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '14px', color: 'var(--color-text)' }}>{t('Energy Savings')}</div>
                <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#eab308' }}>
                  {(100 * (1 - results.newConsumptions.reduce((a, b) => a + b, 0) / results.yearlyConsumptions.reduce((a, b) => a + b, 0))).toFixed(2)}%
                </div>
              </div>
            </div>
          </div>
        )}
        {tab === 'tier' && (
          <div>
            <h2 style={{ margin: '0 0 12px 0', fontSize: '18px', color: '#2563eb', textAlign: 'center' }}>{t('Summary')}</h2>
            <div style={{ display: 'flex', justifyContent: 'space-around', flexWrap: 'wrap', gap: '12px', width: '100%' }}>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '14px', color: 'var(--color-text)' }}>{t('Average Tariff Before')}</div>
                <div style={{ fontSize: '18px', fontWeight: 'bold', color: 'var(--color-text)' }}>
                  {(results.tariffRatesBefore.reduce((a, b) => a + (b || 0), 0) / results.tariffRatesBefore.filter(r => r !== null).length).toFixed(2)} EGP/kWh
                </div>
              </div>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '14px', color: 'var(--color-text)' }}>{t('Average Tariff After')}</div>
                <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#2563eb' }}>
                  {(results.tariffRatesAfter.reduce((a, b) => a + (b || 0), 0) / results.tariffRatesAfter.filter(r => r !== null).length).toFixed(2)} EGP/kWh
                </div>
              </div>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '14px', color: 'var(--color-text)' }}>{t('Average Tariff Decrease')}</div>
                <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#2563eb' }}>
                  {((results.tariffRatesBefore.reduce((a, b) => a + (b || 0), 0) / results.tariffRatesBefore.filter(r => r !== null).length) - 
                   (results.tariffRatesAfter.reduce((a, b) => a + (b || 0), 0) / results.tariffRatesAfter.filter(r => r !== null).length)).toFixed(2)} EGP/kWh
                </div>
              </div>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '14px', color: 'var(--color-text)' }}>{t('Tariff Reduction')}</div>
                <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#2563eb' }}>
                  {(100 * (1 - (results.tariffRatesAfter.reduce((a, b) => a + (b || 0), 0) / results.tariffRatesAfter.filter(r => r !== null).length) / 
                  (results.tariffRatesBefore.reduce((a, b) => a + (b || 0), 0) / results.tariffRatesBefore.filter(r => r !== null).length))).toFixed(2)}%
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      <div style={{ display: 'flex', gap: '20px', alignItems: 'stretch', flexWrap: 'wrap', justifyContent: 'center' }}>
        <div style={{ flex: '1 1 400px', minWidth: '300px', maxWidth: '600px' }}>
          <div className="tabular-results" style={{ marginTop: 8, height: '100%', display: 'flex', flexDirection: 'column' }}>
          <div style={{ overflowX: 'auto', flex: '1' }}>
            {tab === 'energy' && (
              <table style={{ minWidth: 320, borderRadius: 10, overflow: 'hidden', boxShadow: '0 2px 8px rgba(0,0,0,0.04)', fontSize: 13, width: '100%' }}>
                <thead>
                  <tr style={{ background: 'var(--color-surface)', height: '40px' }}>
                    <th style={{ textAlign: 'center' }}>Month</th>
                    <th style={{ textAlign: 'center' }}>Before</th>
                    <th style={{ textAlign: 'center' }}>PV (kWh)</th>
                    <th style={{ textAlign: 'center' }}>After</th>
                  </tr>
                </thead>
                <tbody>
                  {MONTHS.map((month, i) => {
                    const before = results.yearlyConsumptions[i] || 0;
                    const after = results.newConsumptions[i] || 0;
                    const solar = results.monthlyProduction[i] || 0;
                    const rel = energyRel[i];
                    return (
                      <tr key={month} style={{ background: i % 2 ? 'var(--color-surface)' : 'var(--color-surface)', height: '36px' }}>
                        <td style={{ fontWeight: 600, textAlign: 'center' }}>{month}</td>
                        <td style={{ textAlign: 'center' }}>{before.toFixed(2)}</td>
                        <td style={{ textAlign: 'center' }}>
                          {rel > 0 && (
                            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: 20 }}>
                              <div style={{
                                width: 22,
                                height: 7,
                                background: `linear-gradient(90deg, #eab308 ${rel * 100}%, var(--color-muted) ${rel * 100}%)`,
                                borderRadius: 4,
                                marginRight: 4,
                              }} />
                              <span style={{ color: '#eab308', fontWeight: 700, fontSize: 13, marginRight: 4 }}>{solar.toFixed(2)}</span>
                              <span style={{ color: '#eab308', fontWeight: 700, fontSize: 15 }}>↓</span>
                            </div>
                          )}
                        </td>
                        <td style={{ fontWeight: 600, textAlign: 'center' }}>{after.toFixed(2)}</td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            )}
            {tab === 'bill' && (
              <table style={{ minWidth: 320, borderRadius: 10, overflow: 'hidden', boxShadow: '0 2px 8px rgba(0,0,0,0.04)', fontSize: 13, width: '100%' }}>
                <thead>
                  <tr style={{ background: 'var(--color-surface)', height: '40px' }}>
                    <th style={{ textAlign: 'center' }}>Month</th>
                    <th style={{ textAlign: 'center' }}>Before</th>
                    <th style={{ textAlign: 'center' }}>Savings (EGP)</th>
                    <th style={{ textAlign: 'center' }}>After</th>
                  </tr>
                </thead>
                <tbody>
                  {MONTHS.map((month, i) => {
                    const before = results.bills[i] || 0;
                    const after = results.newBills[i] || 0;
                    const savings = results.savings[i] || 0;
                    const rel = billRel[i];
                    return (
                      <tr key={month} style={{ background: i % 2 ? 'var(--color-surface)' : 'var(--color-surface)', height: '36px' }}>
                        <td style={{ fontWeight: 600, textAlign: 'center' }}>{month}</td>
                        <td style={{ textAlign: 'center' }}>{before.toFixed(2)}</td>
                        <td style={{ textAlign: 'center' }}>
                          {rel > 0 && (
                            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: 20 }}>
                              <div style={{
                                width: 22,
                                height: 7,
                                background: `linear-gradient(90deg, #10b981 ${rel * 100}%, var(--color-muted) ${rel * 100}%)`,
                                borderRadius: 4,
                                marginRight: 4,
                              }} />
                              <span style={{ color: '#10b981', fontWeight: 700, fontSize: 13, marginRight: 4 }}>{savings.toFixed(2)}</span>
                              <span style={{ color: '#10b981', fontWeight: 700, fontSize: 15 }}>↓</span>
                            </div>
                          )}
                        </td>
                        <td style={{ fontWeight: 600, textAlign: 'center' }}>{after.toFixed(2)}</td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            )}
            {tab === 'tier' && (
              <table
                style={{
                  minWidth: 320,
                  borderRadius: 10,
                  overflow: 'hidden',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.04)',
                  fontSize: 13,
                  width: '100%',
                  tableLayout: 'fixed',
                }}
              >
                <thead>
                  <tr style={{ background: 'var(--color-surface)', height: '40px' }}>
                    <th style={{ textAlign: 'center' }}>Month</th>
                    <th style={{ textAlign: 'center' }}>Old-New</th>
                    <th style={{ textAlign: 'center' }}>Old</th>
                    <th style={{ textAlign: 'center' }}>Change</th>
                    <th style={{ textAlign: 'center' }}>New</th>
                  </tr>
                </thead>
                <tbody>
                  {MONTHS.map((month, i) => {
                    const beforeTier = results.billTiers && results.billTiers[i];
                    const afterTier = results.newBillTiers && results.newBillTiers[i];
                    const beforeRate = results.tariffRatesBefore && results.tariffRatesBefore[i];
                    const afterRate = results.tariffRatesAfter && results.tariffRatesAfter[i];
                    const rateDiff = (beforeRate != null && afterRate != null) ? beforeRate - afterRate : 0;
                    // For bar width scaling
                    const maxDiff = Math.max(...(results.tariffRatesBefore.map((r, idx) => (r ?? 0) - (results.tariffRatesAfter[idx] ?? 0))), 0.01);
                    let changeContent = null;
                    if (beforeRate != null && afterRate != null && Math.abs(rateDiff) > 0.001) {
                      changeContent = (
                        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: 16 }}>
                          <div style={{ width: `${Math.max(6, 28 * Math.abs(rateDiff) / maxDiff)}px`, height: 5, background: '#2563eb', borderRadius: 2, marginRight: 2 }} />
                          <span style={{ color: '#2563eb', fontWeight: 700, fontSize: 11, margin: '0 1px' }}>{Math.abs(rateDiff).toFixed(2)}</span>
                          {rateDiff > 0 ? (
                            <span style={{ color: '#2563eb', fontWeight: 700, fontSize: 12, marginLeft: 1 }}>↓</span>
                          ) : (
                            <span style={{ color: '#2563eb', fontWeight: 700, fontSize: 12, marginLeft: 1 }}>↑</span>
                          )}
                        </div>
                      );
                    } else if (beforeRate != null && afterRate != null) {
                      changeContent = <span style={{ color: '#2563eb', fontWeight: 700, fontSize: 13 }}>•</span>;
                    } else {
                      changeContent = '-';
                    }
                    return (
                      <tr key={month} style={{ height: '36px', background: i % 2 ? 'var(--color-surface)' : 'var(--color-surface)' }}>
                        <td style={{ fontWeight: 600, textAlign: 'center' }}>{month}</td>
                        <td style={{ fontWeight: 600, textAlign: 'center' }}>{beforeTier ?? '-'}-{afterTier ?? '-'}</td>
                        <td style={{ textAlign: 'center' }}>{beforeRate != null ? beforeRate.toFixed(2) : '-'}</td>
                        <td style={{ textAlign: 'center' }}>{changeContent}</td>
                        <td style={{ textAlign: 'center' }}>{afterRate != null ? afterRate.toFixed(2) : '-'}</td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            )}
          </div>
        </div>
      </div>
      <div style={{ flex: '1 1 400px', minWidth: '300px', maxWidth: '600px', display: 'flex', flexDirection: 'column' }}>
        {tab !== 'tier' && (
          <div className="chart-container" style={{ background: 'var(--color-surface)', borderRadius: 8, padding: 8, boxShadow: '0 2px 8px rgba(0,0,0,0.04)', width: '100%', height: '100%', minHeight: '450px', overflow: 'hidden', display: 'flex', flexDirection: 'column' }}>
            <div style={{ fontWeight: 700, fontSize: 16, textAlign: 'center', marginBottom: 4 }}>{chartTitles[tab]}</div>
            <div style={{ flex: 1, display: 'flex', alignItems: 'flex-end' }}>
              <Bar data={chartData} options={{...chartOptions, maintainAspectRatio: false}} />
            </div>
          </div>
        )}
        {tab === 'tier' && (
          <div className="chart-container" style={{ background: 'var(--color-surface)', borderRadius: 8, padding: 8, boxShadow: '0 2px 8px rgba(0,0,0,0.04)', width: '100%', height: '100%', minHeight: '450px', overflow: 'hidden', display: 'flex', flexDirection: 'column' }}>
            <div style={{ fontWeight: 700, fontSize: 16, textAlign: 'center', marginBottom: 4 }}>Tariff Rate Comparison</div>
            <div style={{ flex: 1, display: 'flex', alignItems: 'flex-end' }}>
              <Bar
                data={{
                  labels: MONTHS,
                  datasets: [
                    {
                      label: 'Old Tariff Rate',
                      data: results.tariffRatesBefore,
                      backgroundColor: '#b0b0b0',
                      borderRadius: 6,
                    },
                    {
                      label: 'New Tariff Rate',
                      data: results.tariffRatesAfter,
                      backgroundColor: '#2563eb',
                      borderRadius: 6,
                    },
                  ],
                }}
                options={{
                  responsive: true,
                  plugins: { legend: { position: 'top' } },
                  scales: {
                    y: { beginAtZero: true, ticks: { font: { size: 11 } } },
                    x: { ticks: { font: { size: 11 } } },
                  },
                  maintainAspectRatio: false,
                }}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  </div>
  );
}
