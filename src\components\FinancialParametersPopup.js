import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FaMoneyBillWave, FaTimes } from 'react-icons/fa';
import ReactDOM from 'react-dom';

export default function FinancialParametersPopup({ values, setValues, onClose, autoUpdate = false, isOpen = false }) {
  const { t } = useTranslation();
  // If autoUpdate is true, we're using external control
  const [internalIsOpen, setInternalIsOpen] = useState(false);
  
  // Determine which state to use
  const popupIsOpen = autoUpdate ? isOpen : internalIsOpen;

  const openPopup = () => setInternalIsOpen(true);
  const closePopup = () => {
    if (autoUpdate) {
      // External control
      if (onClose) {
        onClose(values);
      }
    } else {
      // Internal control
      setInternalIsOpen(false);
      if (onClose) {
        onClose(values);
      }
    }
  };

  return (
    <div>
      {/* <PERSON>ton to open the popup - only render if not in auto-update mode */}
      {!autoUpdate && (
        <button
          onClick={openPopup}
          style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '8px',
          padding: '10px 16px',
          background: 'var(--color-surface)',
          color: 'var(--color-text)',
          border: 'none',
          borderRadius: '8px',
          fontWeight: '600',
          fontSize: '0.95rem',
          cursor: 'pointer',
          transition: 'background 0.2s',
          boxShadow: '0 2px 4px rgba(37, 99, 235, 0.2)'
        }}
      >
        <FaMoneyBillWave size={18} />
        <span>{t('Financial Parameters')}</span>
      </button>
      )}

      {/* Popup overlay */}
      {popupIsOpen && ReactDOM.createPortal(
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1000
        }} onClick={closePopup}>
          {/* Popup content */}
          <div style={{
            background: 'var(--color-surface)',
            borderRadius: '12px',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
            padding: '24px',
            width: '90%',
            maxWidth: '600px',
            maxHeight: '90vh',
            overflowY: 'auto',
            position: 'relative'
          }} onClick={e => e.stopPropagation()}>
            {/* Close button */}
            <button
              onClick={closePopup}
              style={{
                position: 'absolute',
                top: '12px',
                right: '12px',
                background: 'var(--color-surface)',
                border: 'none',
                fontSize: '24px',
                cursor: 'pointer',
                color: 'var(--color-text)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                padding: '4px'
              }}
            >
              <FaTimes />
            </button>

            {/* Popup header */}
            <h2 style={{
              margin: '0 0 20px 0',
              color: 'var(--color-text)',
              fontSize: '1.5rem',
              fontWeight: '700',
              borderBottom: '2px solid var(--color-accent)',
              paddingBottom: '10px'
            }}>
              Financial Parameters
            </h2>

            {/* Financial parameters form */}
            <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
              <div style={{ display: 'flex', flexWrap: 'wrap', gap: '16px' }}>
                <div style={{ flex: '1 1 250px' }}>
                  <label style={{ fontWeight: '600', fontSize: '0.9rem', color: 'var(--color-text)', display: 'block', marginBottom: '6px' }}>
                    Total Cost (EGP):
                  </label>
                  <input 
                    type="number" 
                    min="0" 
                    value={values.totalCost} 
                    onChange={e => setValues(v => ({ ...v, totalCost: e.target.value }))}
                    style={{
                      padding: '10px',
                      borderRadius: '8px',
                      border: '1px solid #d1d5db',
                      fontSize: '1rem',
                      width: '100%',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
                
                <div style={{ flex: '1 1 250px' }}>
                  <label style={{ fontWeight: '600', fontSize: '0.9rem', color: 'var(--color-text)', display: 'block', marginBottom: '6px' }}>
                    First Year Maintenance (EGP):
                  </label>
                  <input 
                    type="number" 
                    min="0" 
                    value={values.firstYearMaintenance} 
                    onChange={e => setValues(v => ({ ...v, firstYearMaintenance: e.target.value }))}
                    style={{
                      padding: '10px',
                      borderRadius: '8px',
                      border: '1px solid #d1d5db',
                      fontSize: '1rem',
                      width: '100%',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
              </div>
              
              <div style={{ display: 'flex', flexWrap: 'wrap', gap: '16px' }}>
                <div style={{ flex: '1 1 250px' }}>
                  <label style={{ fontWeight: '600', fontSize: '0.9rem', color: 'var(--color-text)', display: 'block', marginBottom: '6px' }}>
                    Maintenance Annual Increase (%):
                  </label>
                  <input 
                    type="number" 
                    min="0" 
                    value={values.maintenanceAnnualIncrease} 
                    onChange={e => setValues(v => ({ ...v, maintenanceAnnualIncrease: e.target.value }))}
                    style={{
                      padding: '10px',
                      borderRadius: '8px',
                      border: '1px solid #d1d5db',
                      fontSize: '1rem',
                      width: '100%',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
                
                <div style={{ flex: '1 1 250px' }}>
                  <label style={{ fontWeight: '600', fontSize: '0.9rem', color: 'var(--color-text)', display: 'block', marginBottom: '6px' }}>
                    Yearly Tariff Increase Rate (%):
                  </label>
                  <input 
                    type="number" 
                    min="0" 
                    value={values.yearlyTariffIncreaseRate} 
                    onChange={e => setValues(v => ({ ...v, yearlyTariffIncreaseRate: e.target.value }))}
                    style={{
                      padding: '10px',
                      borderRadius: '8px',
                      border: '1px solid #d1d5db',
                      fontSize: '1rem',
                      width: '100%',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
              </div>
              
              <div style={{ display: 'flex', flexWrap: 'wrap', gap: '16px' }}>
                <div style={{ flex: '1 1 250px' }}>
                  <label style={{ fontWeight: '600', fontSize: '0.9rem', color: 'var(--color-text)', display: 'block', marginBottom: '6px' }}>
                    Yearly Production Degradation Rate (%):
                  </label>
                  <input 
                    type="number" 
                    min="0" 
                    max="100" 
                    step="0.01" 
                    value={values.yearlyDegradationRate || 0.5} 
                    onChange={e => setValues(v => ({ ...v, yearlyDegradationRate: e.target.value }))}
                    style={{
                      padding: '10px',
                      borderRadius: '8px',
                      border: '1px solid #d1d5db',
                      fontSize: '1rem',
                      width: '100%',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
                
                <div style={{ flex: '1 1 250px' }}>
                  <label style={{ fontWeight: '600', fontSize: '0.9rem', color: 'var(--color-text)', display: 'block', marginBottom: '6px' }}>
                    Start Year:
                  </label>
                  <input 
                    type="number" 
                    min="2024" 
                    max="2100" 
                    value={values.startYear || new Date().getFullYear()} 
                    onChange={e => setValues(v => ({ ...v, startYear: e.target.value }))}
                    style={{
                      padding: '10px',
                      borderRadius: '8px',
                      border: '1px solid #d1d5db',
                      fontSize: '1rem',
                      width: '100%',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
              </div>
              
              <div style={{ flex: '1 1 250px' }}>
                <label style={{ fontWeight: '600', fontSize: '0.9rem', color: 'var(--color-text)', display: 'block', marginBottom: '6px' }}>
                  Project Years:
                </label>
                <input 
                  type="number" 
                  min="1" 
                  value={values.projectYears} 
                  onChange={e => setValues(v => ({ ...v, projectYears: e.target.value }))}
                  style={{
                    padding: '10px',
                    borderRadius: '8px',
                    border: '1px solid #d1d5db',
                    fontSize: '1rem',
                    width: '100%',
                    boxSizing: 'border-box'
                  }}
                />
              </div>

              {/* Save button */}
              <button
                onClick={closePopup}
                style={{
                  marginTop: '16px',
                  padding: '12px 16px',
                  background: 'var(--color-surface)',
                  color: 'var(--color-text)',
                  border: 'none',
                  borderRadius: '8px',
                  fontWeight: '600',
                  fontSize: '1rem',
                  cursor: 'pointer',
                  transition: 'background 0.2s',
                  boxShadow: '0 2px 4px rgba(37, 99, 235, 0.2)'
                }}
              >
                {t('Save Parameters')}
              </button>
            </div>
          </div>
        </div>,
        document.body
      )}
    </div>
  );
}