// Financial and carbon calculations for solar PV

// Calculate IRR using <PERSON><PERSON><PERSON><PERSON><PERSON> method
function calculateIRR(cashFlows, guess = 0.1, maxIterations = 1000, tolerance = 1e-6) {
  let x0 = guess;
  let x1 = 0;
  let i = 0;
  function npv(rate) {
    return cashFlows.reduce((sum, flow, i) => sum + flow / Math.pow(1 + rate, i), 0);
  }
  function npvDerivative(rate) {
    return cashFlows.reduce((sum, flow, i) => {
      if (i === 0) return sum;
      return sum - (i * flow) / Math.pow(1 + rate, i + 1);
    }, 0);
  }
  while (i < maxIterations) {
    const f = npv(x0);
    const fPrime = npvDerivative(x0);
    if (Math.abs(fPrime) < tolerance) break;
    x1 = x0 - f / fPrime;
    if (Math.abs(x1 - x0) < tolerance) return x1;
    x0 = x1;
    i++;
  }
  return x1;
}

export function calculateFinancialSummary({
  totalCost,
  firstYearMaintenance = 0,
  maintenanceAnnualIncrease = 0,
  yearlyTariffIncreaseRate = 0,
  yearlyDegradationRate = 0.5, // Default 0.5% per year degradation
  startYear = new Date().getFullYear(), // Default to current year
  projectYears = 25,
  yearlyProduction,
  yearlyTariff,
  gridEmissionFactor = 0.458, // kgCO2/kWh
  lcePV = 1713, // kgCO2/kWp
  lceInverter = 303, // kgCO2/unit
  lceMount = 40, // kgCO2/kWp
  peakPower = 1,
}) {
  // Arrays for each year
  const years = [];
  const production = [];
  const tariffs = [];
  const maintenance = [];
  const netProfit = [];
  const cumulativeProfit = [];
  const carbonSavings = [];
  const cumulativeCarbonSavings = [];
  const cashFlows = [-1 * totalCost];

  // LCE System calculation
  const lceSystem = peakPower * (lcePV + lceMount) + lceInverter; // kgCO2

  for (let i = 0; i < projectYears; i++) {
    years.push(i + 1);
    // Use the actual year for display
    const actualYear = startYear + i;
    // Production: degrade by yearlyDegradationRate%/year
    const degradationFactor = yearlyDegradationRate / 100; // Convert from percentage to decimal
    const prod = yearlyProduction * Math.pow(1 - degradationFactor, i);
    production.push(prod);
    // Tariff: increase by yearlyTariffIncreaseRate
    const tariff = yearlyTariff * Math.pow(1 + yearlyTariffIncreaseRate / 100, i);
    tariffs.push(tariff);
    // Maintenance: increase by maintenanceAnnualIncrease
    const maint = firstYearMaintenance * Math.pow(1 + maintenanceAnnualIncrease / 100, i);
    maintenance.push(maint);
    // Net profit
    const profit = prod * tariff - maint;
    netProfit.push(profit);
    cashFlows.push(profit);
    // Cumulative profit
    cumulativeProfit.push(i === 0 ? profit - totalCost : cumulativeProfit[i - 1] + profit);
    // Carbon savings
    const yearlyCarbon = prod * gridEmissionFactor;
    carbonSavings.push(yearlyCarbon);
    cumulativeCarbonSavings.push(i === 0 ? yearlyCarbon - lceSystem : cumulativeCarbonSavings[i - 1] + yearlyCarbon);
  }

  // Metrics
  // Payback period
  let paybackPeriod = null;
  for (let i = 0; i < cumulativeProfit.length - 1; i++) {
    if (cumulativeProfit[i] < 0 && cumulativeProfit[i + 1] >= 0) {
      const absNegative = Math.abs(cumulativeProfit[i]);
      const positive = cumulativeProfit[i + 1];
      const fraction = absNegative / (absNegative + positive);
      paybackPeriod = i + 1 + fraction;
      break;
    }
  }
  if (paybackPeriod === null) paybackPeriod = cumulativeProfit.every(p => p < 0) ? Infinity : 0;
  const irr = calculateIRR(cashFlows) * 100;
  const npv = cumulativeProfit[cumulativeProfit.length - 1];
  const roi = 100 * npv / totalCost;
  const lcoe = (maintenance.reduce((sum, v) => sum + v, 0) + totalCost) / production.reduce((sum, v) => sum + v, 0);
  const totalCarbonSavings = (carbonSavings.reduce((sum, v) => sum + v, 0) - lceSystem) / 1000; // tons
  const equivalentTrees = Math.round(totalCarbonSavings * 45);
  const fuelSaved = Math.round(totalCarbonSavings * 1000 * 0.4);

  return {
    years,
    startYear, // Include startYear in the returned object
    production,
    tariffs,
    maintenance,
    netProfit,
    cumulativeProfit,
    carbonSavings,
    cumulativeCarbonSavings,
    paybackPeriod,
    irr,
    npv,
    roi,
    lcoe,
    totalCarbonSavings,
    equivalentTrees,
    fuelSaved,
  };
}
