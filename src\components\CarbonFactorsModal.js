import React from 'react';
import { useTranslation } from 'react-i18next';
import ReactDOM from 'react-dom';

export default function CarbonFactorsModal({ open, onClose, values, setValues }) {
  const { t } = useTranslation();
  if (!open) return null;

  return ReactDOM.createPortal(
    <div 
      style={{ 
        position: 'fixed', 
        left: 0, 
        top: 0, 
        width: '100vw', 
        height: '100vh', 
        background: 'var(--color-surface)', 
        zIndex: 2000, 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center' 
      }} 
      onClick={onClose}
    >
      <div 
        style={{ 
          background: 'var(--color-surface)', 
          border: '1px solid var(--color-border)', 
          borderRadius: 8, 
          padding: 24, 
          minWidth: 340, 
          boxShadow: '0 8px 32px rgba(0,0,0,0.18)' 
        }} 
        onClick={e => e.stopPropagation()}
      >
        <h3 style={{ marginTop: 0, color: 'var(--color-text)' }}>{t('Carbon Emission Factors')}</h3>
        <div style={{ display: 'flex', flexDirection: 'column', gap: 12 }}>
          <label>
            {t('Grid Emission Factor (kgCO₂/kWh)')}:
            <input 
              type="number" 
              min="0" 
              step="0.001" 
              value={values.gridEmissionFactor} 
              onChange={e => setValues(v => ({ ...v, gridEmissionFactor: e.target.value }))} 
              style={{ width: '100%', padding: '8px', marginTop: '4px', borderRadius: '4px', border: '1px solid var(--color-border)' }}
            />
          </label>
          <label>
            {t('LCE PV (kgCO₂/kWp)')}:
            <input 
              type="number" 
              min="0" 
              value={values.lcePV} 
              onChange={e => setValues(v => ({ ...v, lcePV: e.target.value }))} 
              style={{ width: '100%', padding: '8px', marginTop: '4px', borderRadius: '4px', border: '1px solid var(--color-border)' }}
            />
          </label>
          <label>
            {t('LCE Inverter (kgCO₂/unit)')}:
            <input 
              type="number" 
              min="0" 
              value={values.lceInverter} 
              onChange={e => setValues(v => ({ ...v, lceInverter: e.target.value }))} 
              style={{ width: '100%', padding: '8px', marginTop: '4px', borderRadius: '4px', border: '1px solid var(--color-border)' }}
            />
          </label>
          <label>
            {t('LCE Mount (kgCO₂/kWp)')}:
            <input 
              type="number" 
              min="0" 
              value={values.lceMount} 
              onChange={e => setValues(v => ({ ...v, lceMount: e.target.value }))} 
              style={{ width: '100%', padding: '8px', marginTop: '4px', borderRadius: '4px', border: '1px solid var(--color-border)' }}
            />
          </label>
        </div>
        <div style={{ display: 'flex', justifyContent: 'center', marginTop: 24 }}>
          <button 
            style={{ 
              padding: '10px 24px', 
              background: 'var(--color-primary)', 
              color: 'var(--color-text)', 
              border: 'none', 
              borderRadius: 6, 
              fontWeight: 600, 
              fontSize: 16,
              boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
              cursor: 'pointer',
              transition: 'all 0.2s ease'
            }} 
            onClick={onClose}
          >
            {t('Save Parameters')}
          </button>
        </div>
      </div>
    </div>,
    document.body
  );
}
