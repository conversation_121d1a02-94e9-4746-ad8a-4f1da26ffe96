import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { FaSolarPanel, FaTrash, FaCopy, FaCheck } from 'react-icons/fa';

function SystemParameters({
  params,
  setParams,
  onOptimalSize,
  location,
  mainRoofArea,
  totalShadedAreaOnRoof,
  selectedTable,
  // Panel parameter props
  panelLength,
  setPanelLength,
  panelWidth,
  setPanelWidth,
  panelTilt,
  setPanelTilt,
  panelOrientation,
  setPanelOrientation,
  panelSpacing,
  setPanelSpacing,
  stackNumber,
  setStackNumber,
  rowSpacing,
  setRowSpacing,
  onApplyParameters,
  onApplyTableChanges,
  manualPlacingActions,
  pvTables // Add pvTables prop for summary calculations
}) {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('default');

  // Local state for Auto Placing tab
  const [placementMode, setPlacementMode] = useState('fill'); // 'fill' or 'fixed'
  const [desiredPanelCount, setDesiredPanelCount] = useState(10);
  const [azimuthAngle, setAzimuthAngle] = useState(0); // 0=South, -90=East, +90=West

  // Local state for Manual Placing tab (table editing)
  const [editTableLength, setEditTableLength] = useState(2.278);
  const [editTableWidth, setEditTableWidth] = useState(1.134);
  const [editTableTilt, setEditTableTilt] = useState(25);
  const [editTableOrientation, setEditTableOrientation] = useState('portrait');
  const [editTableModulesX, setEditTableModulesX] = useState(5);
  const [editTableModulesY, setEditTableModulesY] = useState(1);
  const [editTableAzimuth, setEditTableAzimuth] = useState(0);
  const [editTableModulePower, setEditTableModulePower] = useState(600);

  // Effect to populate editing form when a table is selected
  useEffect(() => {
    if (selectedTable && selectedTable.properties) {
      const props = selectedTable.properties;
      setEditTableLength(props.panelLength || 2.278);
      setEditTableWidth(props.panelWidth || 1.134);
      setEditTableTilt(props.panelTilt || 25);
      setEditTableOrientation(props.panelOrientation || 'portrait');
      setEditTableModulesX(props.modulesX || 5);
      setEditTableModulesY(props.modulesY || 1);
      setEditTableAzimuth(props.azimuth || 0);
      setEditTableModulePower(props.modulePower || 600);
    }
  }, [selectedTable]);

  // Effect to automatically switch to manual-placing tab when a table is selected
  useEffect(() => {
    if (selectedTable) {
      setActiveTab('manual-placing');
    }
  }, [selectedTable]);

  // Calculate non-shaded roof area
  const nonShadedRoofArea = mainRoofArea - totalShadedAreaOnRoof;

  // Tab styles
  const tabStyle = (isActive) => ({
    padding: '10px 16px',
    background: isActive ? 'var(--color-accent)' : 'transparent',
    color: isActive ? 'white' : 'var(--color-text)',
    border: 'none',
    borderRadius: '8px 8px 0 0',
    fontWeight: '600',
    fontSize: '0.9rem',
    cursor: 'pointer',
    transition: 'all 0.2s',
    marginRight: '4px',
    borderBottom: isActive ? 'none' : '1px solid #e5e7eb'
  });

  const tabContentStyle = {
    background: 'var(--color-surface)',
    borderRadius: '0 8px 8px 8px',
    padding: '20px',
    minHeight: '400px',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between'
  };

  return (
    <div className="system-parameters" style={{
      background: 'var(--color-surface)',
      borderRadius: '12px',
      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
      padding: '0',
      height: '100%',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* Tab Headers */}
      <div style={{
        display: 'flex',
        padding: '20px 20px 0 20px',
        borderBottom: '1px solid #e5e7eb'
      }}>
        <button
          onClick={() => setActiveTab('default')}
          style={tabStyle(activeTab === 'default')}
        >
          {t('Default')}
        </button>
        <button
          onClick={() => setActiveTab('manual-placing')}
          style={tabStyle(activeTab === 'manual-placing')}
        >
          {t('Manual Placing')}
        </button>
        <button
          onClick={() => setActiveTab('auto-placing')}
          style={tabStyle(activeTab === 'auto-placing')}
        >
          {t('Auto Placing')}
        </button>
      </div>

      {/* Tab Content */}
      <div style={{ flex: 1 }}>
        {activeTab === 'default' && (
          <div style={tabContentStyle}>
            <div>
              {/* Building Information Section */}
              {!selectedTable && (
                <div style={{
                  marginBottom: '24px',
                  padding: '16px',
                  background: '#f8fafc',
                  borderRadius: '8px',
                  border: '1px solid #e2e8f0'
                }}>
                  <h3 style={{
                    margin: '0 0 12px 0',
                    color: '#374151',
                    fontSize: '1.1rem',
                    fontWeight: '600'
                  }}>{t('Building Information')}</h3>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <span style={{ fontWeight: '500', color: '#6b7280' }}>{t('Selected Location')}:</span>
                      <span style={{ fontWeight: '600', color: '#374151' }}>
                        {location ? `${location.lat.toFixed(4)}, ${location.lng.toFixed(4)}` : t('Not selected')}
                      </span>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <span style={{ fontWeight: '500', color: '#6b7280' }}>{t('Total Main Roof Area')}:</span>
                      <span style={{ fontWeight: '600', color: '#374151' }}>
                        {mainRoofArea ? `${mainRoofArea.toFixed(2)} m²` : '0.00 m²'}
                      </span>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <span style={{ fontWeight: '500', color: '#6b7280' }}>{t('Non-Shaded Roof Area')}:</span>
                      <span style={{ fontWeight: '600', color: '#10b981' }}>
                        {nonShadedRoofArea > 0 ? `${nonShadedRoofArea.toFixed(2)} m²` : '0.00 m²'}
                      </span>
                    </div>
                  </div>
                </div>
              )}

              {/* System parameters form - Two Column Layout */}
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '16px'
              }}>
                {/* Row 1: System Peak Power and System Losses */}
                <div style={{ display: 'flex', gap: '12px' }}>
                  <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '6px' }}>
                    <label style={{ fontWeight: '600', fontSize: '0.9rem', color: '#4b5563' }}>
                      {t('System Peak Power (kWp)')}:
                    </label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={params.peakPower}
                      onChange={e => setParams(p => ({ ...p, peakPower: e.target.value }))}
                      style={{
                        padding: '10px',
                        borderRadius: '8px',
                        border: '1px solid #d1d5db',
                        fontSize: '1rem',
                        width: '100%',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '6px' }}>
                    <label style={{ fontWeight: '600', fontSize: '0.9rem', color: '#4b5563' }}>
                      {t('System Losses (%)')}:
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      step="0.1"
                      value={params.losses}
                      onChange={e => setParams(p => ({ ...p, losses: e.target.value }))}
                      style={{
                        padding: '10px',
                        borderRadius: '8px',
                        border: '1px solid #d1d5db',
                        fontSize: '1rem',
                        width: '100%',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                </div>

                {/* Row 2: Panel Tilt and Panel Azimuth */}
                <div style={{ display: 'flex', gap: '12px' }}>
                  <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '6px' }}>
                    <label style={{ fontWeight: '600', fontSize: '0.9rem', color: '#4b5563' }}>
                      {t('Panel Tilt (°)')}:
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="90"
                      step="1"
                      value={params.tilt}
                      onChange={e => setParams(p => ({ ...p, tilt: e.target.value }))}
                      style={{
                        padding: '10px',
                        borderRadius: '8px',
                        border: '1px solid #d1d5db',
                        fontSize: '1rem',
                        width: '100%',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '6px' }}>
                    <label style={{ fontWeight: '600', fontSize: '0.9rem', color: '#4b5563' }}>
                      {t('Panel Azimuth (°)')}:
                    </label>
                    <input
                      type="number"
                      min="-180"
                      max="180"
                      step="1"
                      value={params.azimuth}
                      onChange={e => setParams(p => ({ ...p, azimuth: e.target.value }))}
                      style={{
                        padding: '10px',
                        borderRadius: '8px',
                        border: '1px solid #d1d5db',
                        fontSize: '1rem',
                        width: '100%',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>

            <button
              type="button"
              onClick={onOptimalSize}
              style={{
                marginTop: '20px',
                padding: '12px 16px',
                background: '#2563eb',
                color: 'var(--color-text)',
                border: 'none',
                borderRadius: '8px',
                fontWeight: '600',
                fontSize: '0.95rem',
                cursor: 'pointer',
                transition: 'background 0.2s',
                ':hover': { background: '#1d4ed8' },
                width: '100%',
                textAlign: 'center',
                boxShadow: '0 2px 4px rgba(37, 99, 235, 0.2)'
              }}
            >
              {t('Calculate optimal system size based on your bill')}
            </button>
          </div>
        )}

        {activeTab === 'auto-placing' && (
          <div style={tabContentStyle}>
            <div>
              {/* Buttons at the top */}
              <div style={{
                display: 'flex',
                gap: '12px',
                marginBottom: '20px'
              }}>
                {/* Remove Tables Button */}
                <button
                  onClick={() => {
                    if (manualPlacingActions?.handleClearTables) {
                      manualPlacingActions.handleClearTables();
                    }
                  }}
                  style={{
                    flex: 1,
                    padding: '12px 20px',
                    background: '#dc2626',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    fontWeight: '600',
                    fontSize: '1rem',
                    cursor: 'pointer',
                    transition: 'background 0.2s',
                    textAlign: 'center',
                    boxShadow: '0 2px 4px rgba(220, 38, 38, 0.2)'
                  }}
                  onMouseEnter={(e) => e.target.style.background = '#b91c1c'}
                  onMouseLeave={(e) => e.target.style.background = '#dc2626'}
                >
                  {t('Remove Tables')}
                </button>

                {/* Auto Place Button */}
                <button
                  onClick={() => {
                    if (onApplyParameters) {
                      // Clear existing tables first if any exist
                      if (pvTables && pvTables.length > 0 && manualPlacingActions?.handleClearTables) {
                        manualPlacingActions.handleClearTables();
                      }

                      const actualPanelLength = panelOrientation === 'portrait' ? panelLength : panelWidth;
                      const tiltAngleInRadians = panelTilt * (Math.PI / 180);
                      const totalHeight = stackNumber * actualPanelLength * Math.sin(tiltAngleInRadians);

                      onApplyParameters({
                        panelLength,
                        panelWidth,
                        panelTilt,
                        panelOrientation,
                        azimuthAngle,
                        panelSpacing,
                        stackNumber,
                        rowSpacing,
                        placementMode: 'fill', // Always use fill mode
                        totalHeight
                      });
                    }
                  }}
                  style={{
                    flex: 1,
                    padding: '12px 20px',
                    background: '#10b981',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    fontWeight: '600',
                    fontSize: '1rem',
                    cursor: 'pointer',
                    transition: 'background 0.2s',
                    textAlign: 'center',
                    boxShadow: '0 2px 4px rgba(16, 185, 129, 0.2)'
                  }}
                  onMouseEnter={(e) => e.target.style.background = '#059669'}
                  onMouseLeave={(e) => e.target.style.background = '#10b981'}
                >
                  {t('Auto Place')}
                </button>
              </div>

              {/* Panel parameters form - Two Column Layout */}
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '16px'
              }}>
                {/* Row 1: Panel Length and Panel Width */}
                <div style={{ display: 'flex', gap: '12px' }}>
                  <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '6px' }}>
                    <label style={{ fontWeight: '600', fontSize: '0.9rem', color: '#4b5563' }}>
                      {t('Panel Length (m)')}:
                    </label>
                    <input
                      type="number"
                      min="0.1"
                      step="0.01"
                      value={panelLength}
                      onChange={e => setPanelLength(Number(e.target.value))}
                      style={{
                        padding: '10px',
                        borderRadius: '8px',
                        border: '1px solid #d1d5db',
                        fontSize: '1rem',
                        width: '100%',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '6px' }}>
                    <label style={{ fontWeight: '600', fontSize: '0.9rem', color: '#4b5563' }}>
                      {t('Panel Width (m)')}:
                    </label>
                    <input
                      type="number"
                      min="0.1"
                      step="0.01"
                      value={panelWidth}
                      onChange={e => setPanelWidth(Number(e.target.value))}
                      style={{
                        padding: '10px',
                        borderRadius: '8px',
                        border: '1px solid #d1d5db',
                        fontSize: '1rem',
                        width: '100%',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                </div>

                {/* Row 2: Panel Tilt and Azimuth Angle */}
                <div style={{ display: 'flex', gap: '12px' }}>
                  <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '6px' }}>
                    <label style={{ fontWeight: '600', fontSize: '0.9rem', color: '#4b5563' }}>
                      {t('Panel Tilt (°)')}:
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="90"
                      value={panelTilt}
                      onChange={e => setPanelTilt(Number(e.target.value))}
                      style={{
                        padding: '10px',
                        borderRadius: '8px',
                        border: '1px solid #d1d5db',
                        fontSize: '1rem',
                        width: '100%',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '6px' }}>
                    <label style={{ fontWeight: '600', fontSize: '0.9rem', color: '#4b5563' }}>
                      {t('Azimuth Angle (°)')}:
                    </label>
                    <input
                      type="number"
                      min="-180"
                      max="180"
                      step="1"
                      value={azimuthAngle}
                      onChange={e => setAzimuthAngle(Number(e.target.value))}
                      style={{
                        padding: '10px',
                        borderRadius: '8px',
                        border: '1px solid #d1d5db',
                        fontSize: '1rem',
                        width: '100%',
                        boxSizing: 'border-box'
                      }}
                      title="0° = South, -90° = East, 90° = West, 180° = North"
                    />
                  </div>
                </div>

                {/* Row 3: Orientation and Stack Number */}
                <div style={{ display: 'flex', gap: '12px' }}>
                  <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '6px' }}>
                    <label style={{ fontWeight: '600', fontSize: '0.9rem', color: '#4b5563' }}>
                      {t('Orientation')}:
                    </label>
                    <select
                      value={panelOrientation}
                      onChange={e => setPanelOrientation(e.target.value)}
                      style={{
                        padding: '10px',
                        borderRadius: '8px',
                        border: '1px solid #d1d5db',
                        fontSize: '1rem',
                        width: '100%',
                        boxSizing: 'border-box'
                      }}
                    >
                      <option value="portrait">{t('Portrait')}</option>
                      <option value="landscape">{t('Landscape')}</option>
                    </select>
                  </div>
                  <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '6px' }}>
                    <label style={{ fontWeight: '600', fontSize: '0.9rem', color: '#4b5563' }}>
                      {t('Stack Number')}:
                    </label>
                    <input
                      type="number"
                      min="1"
                      value={stackNumber}
                      onChange={e => setStackNumber(Number(e.target.value))}
                      style={{
                        padding: '10px',
                        borderRadius: '8px',
                        border: '1px solid #d1d5db',
                        fontSize: '1rem',
                        width: '100%',
                        boxSizing: 'border-box'
                      }}
                      title="Number of modules stacked in Y direction (perpendicular to azimuth)"
                    />
                  </div>
                </div>

                {/* Row 4: Row Spacing and Panels Spacing */}
                <div style={{ display: 'flex', gap: '12px' }}>
                  <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '6px' }}>
                    <label style={{ fontWeight: '600', fontSize: '0.9rem', color: '#4b5563' }}>
                      {t('Row Spacing (m)')}:
                    </label>
                    <input
                      type="number"
                      min="0.1"
                      step="0.1"
                      value={rowSpacing}
                      onChange={e => setRowSpacing(Number(e.target.value))}
                      style={{
                        padding: '10px',
                        borderRadius: '8px',
                        border: '1px solid #d1d5db',
                        fontSize: '1rem',
                        width: '100%',
                        boxSizing: 'border-box'
                      }}
                      title="Spacing between rows of PV tables"
                    />
                  </div>
                  <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '6px' }}>
                    <label style={{ fontWeight: '600', fontSize: '0.9rem', color: '#4b5563' }}>
                      {t('Panels Spacing')}:
                    </label>
                    <input
                      type="text"
                      value={panelSpacing}
                      onChange={e => setPanelSpacing(e.target.value)}
                      placeholder="auto"
                      style={{
                        padding: '10px',
                        borderRadius: '8px',
                        border: '1px solid #d1d5db',
                        fontSize: '1rem',
                        width: '100%',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                </div>

              </div>
            </div>
          </div>
        )}

        {activeTab === 'manual-placing' && (
          <div style={tabContentStyle}>
            <div>
              {/* Manual Placing Action Buttons */}
              <div style={{
                display: 'flex',
                gap: '8px',
                marginBottom: '20px',
                width: '100%'
              }}>
                <button
                  onClick={() => manualPlacingActions?.setDrawTableMode(true)}
                  disabled={!manualPlacingActions?.mainRoofLayer || manualPlacingActions?.drawTableMode}
                  style={{
                    flex: 1,
                    padding: '12px',
                    border: 'none',
                    borderRadius: '6px',
                    background: manualPlacingActions?.drawTableMode ? '#10b981' : '#3b82f6',
                    color: 'white',
                    fontWeight: '600',
                    cursor: (!manualPlacingActions?.mainRoofLayer || manualPlacingActions?.drawTableMode) ? 'not-allowed' : 'pointer',
                    opacity: (!manualPlacingActions?.mainRoofLayer || manualPlacingActions?.drawTableMode) ? 0.5 : 1,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    height: '44px',
                    transition: 'all 0.2s ease'
                  }}
                  title={!manualPlacingActions?.mainRoofLayer ? 'Draw a roof area first' : 'Place PV Table'}
                >
                  <FaSolarPanel size={18} />
                </button>

                {manualPlacingActions?.selectedTableId && (
                  <button
                    onClick={() => manualPlacingActions?.handleDeleteSelectedTable()}
                    style={{
                      flex: 1,
                      padding: '12px',
                      border: 'none',
                      borderRadius: '6px',
                      background: '#ef4444',
                      color: 'white',
                      fontWeight: '600',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      height: '44px',
                      transition: 'all 0.2s ease'
                    }}
                    title="Delete selected table"
                  >
                    <FaTrash size={18} />
                  </button>
                )}

                {manualPlacingActions?.selectedTableId && (
                  <button
                    onClick={() => manualPlacingActions?.handleDuplicateTable(manualPlacingActions.selectedTableId)}
                    style={{
                      flex: 1,
                      padding: '12px',
                      border: 'none',
                      borderRadius: '6px',
                      background: '#f59e0b',
                      color: 'white',
                      fontWeight: '600',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      height: '44px',
                      transition: 'all 0.2s ease'
                    }}
                    title="Duplicate selected table"
                  >
                    <FaCopy size={18} />
                  </button>
                )}

                {manualPlacingActions?.selectedTableId && (
                  <button
                    onClick={() => {
                      // Apply changes first, then deselect
                      if (selectedTable && onApplyTableChanges) {
                        onApplyTableChanges(selectedTable.id, {
                          panelLength: editTableLength,
                          panelWidth: editTableWidth,
                          panelTilt: editTableTilt,
                          panelOrientation: editTableOrientation,
                          modulesX: editTableModulesX,
                          modulesY: editTableModulesY,
                          azimuth: editTableAzimuth,
                          modulePower: editTableModulePower
                        });
                      }
                      // Then deselect
                      manualPlacingActions?.handleDeselectAllTables();
                    }}
                    style={{
                      flex: 1,
                      padding: '12px',
                      border: 'none',
                      borderRadius: '6px',
                      background: '#10b981',
                      color: 'white',
                      fontWeight: '600',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      height: '44px',
                      transition: 'all 0.2s ease'
                    }}
                    title="Save changes and deselect table"
                  >
                    <FaCheck size={18} />
                  </button>
                )}
              </div>

              {selectedTable ? (
                <div>

                  {/* Table property editing controls */}
                  <div style={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '16px'
                  }}>
                    {/* Row 1: Module Power and Orientation */}
                    <div style={{ display: 'flex', gap: '12px' }}>
                      <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '6px' }}>
                        <label style={{ fontWeight: '600', fontSize: '0.9rem', color: '#4b5563' }}>
                          {t('Module Power (W)')}:
                        </label>
                        <input
                          type="number"
                          min="1"
                          step="1"
                          value={editTableModulePower}
                          onChange={e => setEditTableModulePower(Number(e.target.value))}
                          style={{
                            padding: '10px',
                            borderRadius: '8px',
                            border: '1px solid #d1d5db',
                            fontSize: '1rem',
                            width: '100%',
                            boxSizing: 'border-box'
                          }}
                        />
                      </div>
                      <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '6px' }}>
                        <label style={{ fontWeight: '600', fontSize: '0.9rem', color: '#4b5563' }}>
                          {t('Orientation')}:
                        </label>
                        <select
                          value={editTableOrientation}
                          onChange={e => setEditTableOrientation(e.target.value)}
                          style={{
                            padding: '10px',
                            borderRadius: '8px',
                            border: '1px solid #d1d5db',
                            fontSize: '1rem',
                            width: '100%',
                            boxSizing: 'border-box'
                          }}
                        >
                          <option value="portrait">{t('Portrait')}</option>
                          <option value="landscape">{t('Landscape')}</option>
                        </select>
                      </div>
                    </div>

                    {/* Row 2: Panel Length and Panel Width */}
                    <div style={{ display: 'flex', gap: '12px' }}>
                      <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '6px' }}>
                        <label style={{ fontWeight: '600', fontSize: '0.9rem', color: '#4b5563' }}>
                          {t('Panel Length (m)')}:
                        </label>
                        <input
                          type="number"
                          min="0.1"
                          step="0.01"
                          value={editTableLength}
                          onChange={e => setEditTableLength(Number(e.target.value))}
                          style={{
                            padding: '10px',
                            borderRadius: '8px',
                            border: '1px solid #d1d5db',
                            fontSize: '1rem',
                            width: '100%',
                            boxSizing: 'border-box'
                          }}
                        />
                      </div>
                      <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '6px' }}>
                        <label style={{ fontWeight: '600', fontSize: '0.9rem', color: '#4b5563' }}>
                          {t('Panel Width (m)')}:
                        </label>
                        <input
                          type="number"
                          min="0.1"
                          step="0.01"
                          value={editTableWidth}
                          onChange={e => setEditTableWidth(Number(e.target.value))}
                          style={{
                            padding: '10px',
                            borderRadius: '8px',
                            border: '1px solid #d1d5db',
                            fontSize: '1rem',
                            width: '100%',
                            boxSizing: 'border-box'
                          }}
                        />
                      </div>
                    </div>

                    {/* Row 3: Panel Tilt and Azimuth Angle */}
                    <div style={{ display: 'flex', gap: '12px' }}>
                      <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '6px' }}>
                        <label style={{ fontWeight: '600', fontSize: '0.9rem', color: '#4b5563' }}>
                          {t('Panel Tilt (°)')}:
                        </label>
                        <input
                          type="number"
                          min="0"
                          max="90"
                          value={editTableTilt}
                          onChange={e => setEditTableTilt(Number(e.target.value))}
                          style={{
                            padding: '10px',
                            borderRadius: '8px',
                            border: '1px solid #d1d5db',
                            fontSize: '1rem',
                            width: '100%',
                            boxSizing: 'border-box'
                          }}
                        />
                      </div>
                      <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '6px' }}>
                        <label style={{ fontWeight: '600', fontSize: '0.9rem', color: '#4b5563' }}>
                          {t('Azimuth Angle (°)')}:
                        </label>
                        <input
                          type="number"
                          min="-180"
                          max="180"
                          step="1"
                          value={editTableAzimuth}
                          onChange={e => setEditTableAzimuth(Number(e.target.value))}
                          style={{
                            padding: '10px',
                            borderRadius: '8px',
                            border: '1px solid #d1d5db',
                            fontSize: '1rem',
                            width: '100%',
                            boxSizing: 'border-box'
                          }}
                          title="0° = South, -90° = East, 90° = West, 180° = North"
                        />
                      </div>
                    </div>

                    {/* Row 4: Modules X and Modules Y */}
                    <div style={{ display: 'flex', gap: '12px' }}>
                      <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '6px' }}>
                        <label style={{ fontWeight: '600', fontSize: '0.9rem', color: '#4b5563' }}>
                          {t('Modules X (horizontal)')}:
                        </label>
                        <input
                          type="number"
                          min="1"
                          value={editTableModulesX}
                          onChange={e => setEditTableModulesX(Number(e.target.value))}
                          style={{
                            padding: '10px',
                            borderRadius: '8px',
                            border: '1px solid #d1d5db',
                            fontSize: '1rem',
                            width: '100%',
                            boxSizing: 'border-box'
                          }}
                        />
                      </div>
                      <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '6px' }}>
                        <label style={{ fontWeight: '600', fontSize: '0.9rem', color: '#4b5563' }}>
                          {t('Modules Y (vertical)')}:
                        </label>
                        <input
                          type="number"
                          min="1"
                          value={editTableModulesY}
                          onChange={e => setEditTableModulesY(Number(e.target.value))}
                          style={{
                            padding: '10px',
                            borderRadius: '8px',
                            border: '1px solid #d1d5db',
                            fontSize: '1rem',
                            width: '100%',
                            boxSizing: 'border-box'
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div style={{
                  padding: '20px',
                  background: '#f8fafc',
                  borderRadius: '8px',
                  border: '1px solid #e2e8f0'
                }}>
                  <h3 style={{
                    margin: '0 0 16px 0',
                    color: '#1e293b',
                    fontSize: '1.1rem',
                    fontWeight: '600',
                    textAlign: 'center'
                  }}>
                    {t('PV System Summary')}
                  </h3>

                  <div style={{
                    display: 'grid',
                    gridTemplateColumns: '1fr',
                    gap: '12px'
                  }}>
                    {(() => {
                      // Calculate total modules and power
                      let totalModules = 0;
                      let totalPowerW = 0;

                      if (pvTables && Array.isArray(pvTables)) {
                        pvTables.forEach(table => {
                          const props = table.properties || {};
                          const modulesX = props.modulesX || 1;
                          const modulesY = props.modulesY || 1;
                          const modulePower = props.modulePower || 400;
                          const tableModules = modulesX * modulesY;
                          totalModules += tableModules;
                          totalPowerW += tableModules * modulePower;
                        });
                      }

                      const totalPowerKW = totalPowerW / 1000;
                      const usableRoofArea = mainRoofArea || 0;
                      const nonShadedArea = Math.max(0, usableRoofArea - (totalShadedAreaOnRoof || 0));

                      return (
                        <>
                          <div style={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            padding: '8px 0',
                            borderBottom: '1px solid #e2e8f0'
                          }}>
                            <span style={{ fontWeight: '500', color: '#475569' }}>
                              {t('Total Number of Modules')}:
                            </span>
                            <span style={{ fontWeight: '600', color: '#1e293b' }}>
                              {totalModules}
                            </span>
                          </div>

                          <div style={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            padding: '8px 0',
                            borderBottom: '1px solid #e2e8f0'
                          }}>
                            <span style={{ fontWeight: '500', color: '#475569' }}>
                              {t('Total PV Power Rating')}:
                            </span>
                            <span style={{ fontWeight: '600', color: '#059669' }}>
                              {totalPowerKW.toFixed(2)} kW
                            </span>
                          </div>

                          <div style={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            padding: '8px 0',
                            borderBottom: '1px solid #e2e8f0'
                          }}>
                            <span style={{ fontWeight: '500', color: '#475569' }}>
                              {t('Usable Roof Area')}:
                            </span>
                            <span style={{ fontWeight: '600', color: '#1e293b' }}>
                              {usableRoofArea.toFixed(2)} m²
                            </span>
                          </div>

                          <div style={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            padding: '8px 0'
                          }}>
                            <span style={{ fontWeight: '500', color: '#475569' }}>
                              {t('Non-Shaded Roof Area')}:
                            </span>
                            <span style={{ fontWeight: '600', color: '#1e293b' }}>
                              {nonShadedArea.toFixed(2)} m²
                            </span>
                          </div>
                        </>
                      );
                    })()}
                  </div>

                  <div style={{
                    marginTop: '16px',
                    padding: '12px',
                    background: '#f1f5f9',
                    borderRadius: '6px',
                    textAlign: 'center'
                  }}>
                    <div style={{ fontSize: '0.9rem', color: '#64748b' }}>
                      {t('Click on a solar panel table on the map to edit its properties')}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default SystemParameters;
