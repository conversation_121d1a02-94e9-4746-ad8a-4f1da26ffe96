/**
 * WASM Loader Utility for Clipper2
 * 
 * Handles initialization and loading of Clipper2 WASM module
 * with proper error handling and fallback mechanisms
 */

let clipper2Instance = null;
let initializationPromise = null;
let initializationError = null;

/**
 * Initialize Clipper2 WASM module
 * @returns {Promise<Object>} Clipper2 WASM instance
 */
export const initializeClipper2 = async () => {
  // Return existing instance if already initialized
  if (clipper2Instance) {
    return clipper2Instance;
  }

  // Return existing promise if initialization is in progress
  if (initializationPromise) {
    return initializationPromise;
  }

  // If previous initialization failed, throw the error
  if (initializationError) {
    throw initializationError;
  }

  initializationPromise = (async () => {
    try {
      console.log('[wasmLoader] Starting Clipper2 WASM initialization...');

      // Dynamic import of clipper2-wasm
      const Clipper2ZFactory = await import('clipper2-wasm/dist/es/clipper2z.js');

      // Initialize the WASM module with proper configuration
      const clipper2 = await Clipper2ZFactory.default({
        locateFile: (path) => {
          // For Create React App, the WASM file is served from the public folder
          if (path.endsWith('.wasm')) {
            return `/${path}`;
          }
          return path;
        }
      });

      console.log('[wasmLoader] Clipper2 WASM initialized successfully');
      clipper2Instance = clipper2;

      return clipper2Instance;
    } catch (error) {
      console.error('[wasmLoader] Failed to initialize Clipper2 WASM:', error);
      initializationError = new Error(`Clipper2 WASM initialization failed: ${error.message}`);
      throw initializationError;
    }
  })();

  return initializationPromise;
};

/**
 * Get Clipper2 instance (must be initialized first)
 * @returns {Object|null} Clipper2 WASM instance or null if not initialized
 */
export const getClipper2Instance = () => {
  return clipper2Instance;
};

/**
 * Check if Clipper2 is initialized
 * @returns {boolean} True if initialized, false otherwise
 */
export const isClipper2Initialized = () => {
  return clipper2Instance !== null;
};

/**
 * Reset Clipper2 initialization state (for testing purposes)
 */
export const resetClipper2State = () => {
  clipper2Instance = null;
  initializationPromise = null;
  initializationError = null;
};

/**
 * Check if WASM is supported in the current environment
 * @returns {boolean} True if WASM is supported
 */
export const isWasmSupported = () => {
  try {
    if (typeof WebAssembly === 'object' && 
        typeof WebAssembly.instantiate === 'function') {
      // Test basic WASM support
      const module = new WebAssembly.Module(
        new Uint8Array([0x00, 0x61, 0x73, 0x6d, 0x01, 0x00, 0x00, 0x00])
      );
      return module instanceof WebAssembly.Module;
    }
    return false;
  } catch (error) {
    console.warn('[wasmLoader] WASM support check failed:', error);
    return false;
  }
};

/**
 * Initialize Clipper2 with comprehensive error handling
 * @returns {Promise<{success: boolean, instance?: Object, error?: Error}>}
 */
export const safeInitializeClipper2 = async () => {
  try {
    // Check WASM support first
    if (!isWasmSupported()) {
      throw new Error('WebAssembly is not supported in this environment');
    }

    const instance = await initializeClipper2();
    return {
      success: true,
      instance
    };
  } catch (error) {
    console.error('[wasmLoader] Safe initialization failed:', error);
    return {
      success: false,
      error
    };
  }
};
