/**
 * Shared utilities for shadow calculations
 * Used by both main thread and Web Worker implementations
 */

/**
 * Prepare obstacle data for shadow calculations
 * Converts Leaflet layers to serializable format for Web Worker
 */
export const prepareObstacleData = (obstacles) => {
  return obstacles.map(obstacle => {
    if (!obstacle.layer || typeof obstacle.layer.getLatLngs !== 'function') {
      console.warn('[prepareObstacleData] Invalid obstacle layer:', obstacle);
      return null;
    }

    let coordinates = [];
    const rawLatLngs = obstacle.layer.getLatLngs();

    // Extract coordinates from Leaflet layer
    if (rawLatLngs && rawLatLngs.length > 0) {
      if (rawLatLngs[0] && typeof rawLatLngs[0].lat === 'number') {
        // Simple polygon: [LatLng, LatLng, ...]
        coordinates = rawLatLngs.map(latlng => [latlng.lat, latlng.lng]);
      } else if (Array.isArray(rawLatLngs[0]) && rawLatLngs[0][0] && typeof rawLatLngs[0][0].lat === 'number') {
        // Polygon with holes: [[LatLng, LatLng, ...], [hole1], [hole2], ...]
        coordinates = rawLatLngs[0].map(latlng => [latlng.lat, latlng.lng]);
      }
    }

    if (coordinates.length === 0) {
      console.warn('[prepareObstacleData] No valid coordinates found for obstacle:', obstacle);
      return null;
    }

    return {
      id: obstacle.id,
      coordinates,
      height: obstacle.height,
      type: obstacle.type || 'obstacle',
      area: obstacle.area || 0
    };
  }).filter(obstacle => obstacle !== null);
};

/**
 * Prepare PV table data for shadow calculations
 * Converts PV table rectangles to serializable format
 */
export const preparePVTableData = (pvTables) => {
  return pvTables.map(table => {
    const panels = table.rectangles.map((rect, panelIndex) => {
      // rect is an array of [lat, lng] pairs defining the rectangle corners
      const coordinates = rect.map(corner => [corner[0], corner[1]]);

      // Calculate height based on table properties
      const tableProps = table.properties || {};
      const tablePanelTilt = tableProps.panelTilt || 25;
      const tablePanelLength = tableProps.panelLength || 2.278;
      const modulesY = tableProps.modulesY || 1;

      // Height = sin(tilt) * (panel_length * modules_in_Y_direction)
      const effectiveLength = tablePanelLength * modulesY;
      const calculatedHeight = Math.sin(tablePanelTilt * Math.PI / 180) * effectiveLength;

      return {
        id: `${table.id}-panel-${panelIndex}`,
        coordinates,
        height: calculatedHeight,
        type: 'manual-pv-panel',
        tableId: table.id,
        panelIndex,
        tableProperties: tableProps
      };
    });

    return panels;
  }).flat();
};

/**
 * Prepare main roof bounds for clipping calculations
 */
export const prepareMainRoofData = (mainRoofLayer) => {
  if (!mainRoofLayer || typeof mainRoofLayer.getLatLngs !== 'function') {
    return null;
  }

  const mainRoofLatLngs = mainRoofLayer.getLatLngs();
  let coordinates = [];

  if (mainRoofLatLngs && mainRoofLatLngs.length > 0) {
    if (mainRoofLatLngs[0] && typeof mainRoofLatLngs[0].lat === 'number') {
      coordinates = mainRoofLatLngs.map(latlng => [latlng.lat, latlng.lng]);
    } else if (Array.isArray(mainRoofLatLngs[0]) && mainRoofLatLngs[0][0] && typeof mainRoofLatLngs[0][0].lat === 'number') {
      coordinates = mainRoofLatLngs[0].map(latlng => [latlng.lat, latlng.lng]);
    }
  }

  return coordinates.length > 0 ? coordinates : null;
};

/**
 * Validate shadow calculation parameters
 */
export const validateShadowParams = (params) => {
  const errors = [];

  if (!params.location || typeof params.location.lat !== 'number' || typeof params.location.lng !== 'number') {
    errors.push('Invalid location coordinates');
  }

  if (!params.analysisDate || isNaN(new Date(params.analysisDate + 'T12:00:00').getTime())) {
    errors.push('Invalid analysis date');
  }

  if (!params.analysisDurationHours || params.analysisDurationHours <= 0) {
    errors.push('Invalid analysis duration');
  }

  if (!Array.isArray(params.obstacles)) {
    errors.push('Obstacles must be an array');
  }

  if (!Array.isArray(params.pvTables)) {
    errors.push('PV tables must be an array');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Format shadow calculation results for display
 */
export const formatShadowResults = (results) => {
  if (!results || !Array.isArray(results.shadows)) {
    return {
      shadows: [],
      totalShadedArea: 0,
      formattedArea: '0.00 m²'
    };
  }

  const totalArea = results.totalShadedArea || 0;
  const formattedArea = totalArea > 1000000 
    ? `${(totalArea / 1000000).toFixed(2)} km²`
    : `${totalArea.toFixed(2)} m²`;

  return {
    shadows: results.shadows,
    totalShadedArea: totalArea,
    formattedArea,
    calculationTime: results.calculationTime || 0,
    usedFallback: results.usedFallback || false
  };
};

/**
 * Create shadow calculation parameters from component state
 */
export const createShadowCalculationParams = (componentState) => {
  const {
    obstacles,
    pvTables,
    location,
    analysisDate,
    analysisDurationHours,
    mainRoofLayer,
    autoPlacedPvFeatures
  } = componentState;

  // Prepare obstacle data
  const obstacleData = prepareObstacleData(obstacles);

  // Prepare PV table data
  const pvTableData = preparePVTableData(pvTables);

  // Prepare auto-placed PV features if they exist
  let autoPlacedData = [];
  if (autoPlacedPvFeatures && autoPlacedPvFeatures.features) {
    autoPlacedData = autoPlacedPvFeatures.features.map((feature, index) => {
      if (feature.geometry && feature.geometry.type === 'Polygon' && feature.geometry.coordinates) {
        const geoJsonOuterRing = feature.geometry.coordinates[0]; // LngLat format
        const coordinates = geoJsonOuterRing.map(coord => [coord[1], coord[0]]); // Convert to LatLng
        
        return {
          id: feature.properties?.id || feature.id || `auto-pv-${index}`,
          coordinates,
          height: feature.properties?.height || 0,
          type: 'auto-pv-panel'
        };
      }
      return null;
    }).filter(item => item !== null);
  }

  // Prepare main roof bounds
  const mainRoofBounds = prepareMainRoofData(mainRoofLayer);

  return {
    obstacles: [...obstacleData, ...pvTableData, ...autoPlacedData],
    pvTables: pvTableData,
    location,
    analysisDate,
    analysisDurationHours,
    mainRoofBounds
  };
};
