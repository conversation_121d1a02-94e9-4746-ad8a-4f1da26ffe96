/**
 * State validation utilities for map components
 * Provides validation, sanitization, and type checking for state objects
 */

// Validation schemas
export const VALIDATION_SCHEMAS = {
  pvTable: {
    id: { type: 'string', required: true },
    position: { 
      type: 'object', 
      required: true,
      properties: {
        lat: { type: 'number', min: -90, max: 90 },
        lng: { type: 'number', min: -180, max: 180 }
      }
    },
    properties: {
      type: 'object',
      properties: {
        panelLength: { type: 'number', min: 0.1, max: 10 },
        panelWidth: { type: 'number', min: 0.1, max: 5 },
        panelTilt: { type: 'number', min: 0, max: 90 },
        azimuth: { type: 'number', min: -180, max: 180 },
        modulesX: { type: 'number', min: 1, max: 50 },
        modulesY: { type: 'number', min: 1, max: 50 }
      }
    },
    rectangles: { type: 'array', required: true },
    selected: { type: 'boolean', default: false },
    lastModified: { type: 'number', required: true }
  },
  
  obstacle: {
    id: { type: 'string', required: true },
    coordinates: { type: 'array', required: true, minLength: 3 },
    height: { type: 'number', min: 0, max: 1000 },
    type: { type: 'string', enum: ['building', 'tree', 'fence-segment', 'manual-pv-panel', 'auto-pv-panel'] }
  },
  
  location: {
    lat: { type: 'number', min: -90, max: 90, required: true },
    lng: { type: 'number', min: -180, max: 180, required: true }
  }
};

/**
 * Validates a value against a schema
 */
export const validateValue = (value, schema) => {
  const errors = [];
  
  // Check required
  if (schema.required && (value === undefined || value === null)) {
    errors.push('Value is required');
    return { isValid: false, errors };
  }
  
  // Skip validation if value is undefined/null and not required
  if (value === undefined || value === null) {
    return { isValid: true, errors: [] };
  }
  
  // Type validation
  if (schema.type) {
    const actualType = Array.isArray(value) ? 'array' : typeof value;
    if (actualType !== schema.type) {
      errors.push(`Expected ${schema.type}, got ${actualType}`);
    }
  }
  
  // Number validations
  if (schema.type === 'number' && typeof value === 'number') {
    if (schema.min !== undefined && value < schema.min) {
      errors.push(`Value ${value} is below minimum ${schema.min}`);
    }
    if (schema.max !== undefined && value > schema.max) {
      errors.push(`Value ${value} is above maximum ${schema.max}`);
    }
  }
  
  // String validations
  if (schema.type === 'string' && typeof value === 'string') {
    if (schema.enum && !schema.enum.includes(value)) {
      errors.push(`Value "${value}" is not in allowed values: ${schema.enum.join(', ')}`);
    }
  }
  
  // Array validations
  if (schema.type === 'array' && Array.isArray(value)) {
    if (schema.minLength !== undefined && value.length < schema.minLength) {
      errors.push(`Array length ${value.length} is below minimum ${schema.minLength}`);
    }
  }
  
  // Object property validation
  if (schema.type === 'object' && schema.properties && typeof value === 'object') {
    for (const [key, propSchema] of Object.entries(schema.properties)) {
      const propResult = validateValue(value[key], propSchema);
      if (!propResult.isValid) {
        errors.push(...propResult.errors.map(err => `${key}: ${err}`));
      }
    }
  }
  
  return { isValid: errors.length === 0, errors };
};

/**
 * Validates a complete object against a schema
 */
export const validateObject = (obj, schemaName) => {
  const schema = VALIDATION_SCHEMAS[schemaName];
  if (!schema) {
    return { isValid: false, errors: [`Unknown schema: ${schemaName}`] };
  }
  
  const errors = [];
  
  for (const [key, fieldSchema] of Object.entries(schema)) {
    const result = validateValue(obj[key], fieldSchema);
    if (!result.isValid) {
      errors.push(...result.errors.map(err => `${key}: ${err}`));
    }
  }
  
  return { isValid: errors.length === 0, errors };
};

/**
 * Sanitizes and applies defaults to an object
 */
export const sanitizeObject = (obj, schemaName) => {
  const schema = VALIDATION_SCHEMAS[schemaName];
  if (!schema) return obj;
  
  const sanitized = { ...obj };
  
  for (const [key, fieldSchema] of Object.entries(schema)) {
    // Apply defaults
    if (sanitized[key] === undefined && fieldSchema.default !== undefined) {
      sanitized[key] = fieldSchema.default;
    }
    
    // Clamp numbers to valid ranges
    if (fieldSchema.type === 'number' && typeof sanitized[key] === 'number') {
      if (fieldSchema.min !== undefined) {
        sanitized[key] = Math.max(sanitized[key], fieldSchema.min);
      }
      if (fieldSchema.max !== undefined) {
        sanitized[key] = Math.min(sanitized[key], fieldSchema.max);
      }
    }
  }
  
  return sanitized;
};

/**
 * Creates a safe state updater that validates changes
 */
export const createSafeUpdater = (schemaName, onError = console.error) => {
  return (currentState, updates) => {
    try {
      const newState = { ...currentState, ...updates };
      const validation = validateObject(newState, schemaName);
      
      if (!validation.isValid) {
        onError('State validation failed:', validation.errors);
        return currentState; // Return unchanged state on validation failure
      }
      
      return sanitizeObject(newState, schemaName);
    } catch (error) {
      onError('State update error:', error);
      return currentState;
    }
  };
};

/**
 * Validates an array of objects
 */
export const validateArray = (array, schemaName) => {
  if (!Array.isArray(array)) {
    return { isValid: false, errors: ['Expected array'] };
  }
  
  const errors = [];
  
  array.forEach((item, index) => {
    const result = validateObject(item, schemaName);
    if (!result.isValid) {
      errors.push(`Item ${index}: ${result.errors.join(', ')}`);
    }
  });
  
  return { isValid: errors.length === 0, errors };
};

export default {
  validateValue,
  validateObject,
  validateArray,
  sanitizeObject,
  createSafeUpdater,
  VALIDATION_SCHEMAS
};
