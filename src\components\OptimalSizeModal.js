import React, { useState } from 'react';

const TARIFFS = [
  { value: 'residential', label: 'Residential' },
  { value: 'commercial', label: 'Commercial' },
  { value: 'low_voltage', label: 'Low Voltage Use' },
  { value: 'medium_voltage', label: 'Medium Voltage Use' },
  { value: 'irrigation_low_voltage', label: 'Irrigation (Low Voltage)' },
  { value: 'custom_tariff', label: 'Custom Tariff' },
];

export default function OptimalSizeModal({ open, onClose, onApply }) {
  const [bill, setBill] = useState('');
  const [consumptionType, setConsumptionType] = useState('residential');
  const [customTariff, setCustomTariff] = useState('');
  const [result, setResult] = useState(null);

  // Dummy calculation logic (replace with real one later)
  function calculate() {
    let tariff = 2.5;
    if (consumptionType === 'custom_tariff') {
      tariff = parseFloat(customTariff) || 2.5;
    }
    const monthlyConsumption = bill && tariff ? parseFloat(bill) / tariff : 0;
    const maxPV = (monthlyConsumption * 12) / 3504;
    setResult({ maxPV, monthlyConsumption });
  }

  function handleApply() {
    if (result) {
      onApply({
        peakPower: result.maxPV,
        fixedValue: bill,
        consumptionType,
        customTariff,
      });
      onClose();
    }
  }

  if (!open) return null;
  return (
    <div className="modal-backdrop" style={{ position: 'fixed', top:0, left:0, width:'100vw', height:'100vh', background:'rgba(0,0,0,0.4)', zIndex:1000, display:'flex', alignItems:'center', justifyContent:'center' }}>
      <div className="modal-content" style={{ background:'var(--color-surface)', borderRadius:10, padding:24, minWidth:320, maxWidth:400, boxShadow:'0 4px 24px rgba(0,0,0,0.15)' }}>
        <h2>Calculate Max Allowed Solar Capacity</h2>
        <div className="form-group">
          <label>Monthly Bill Amount (EGP)</label>
          <input type="number" min="0" value={bill} onChange={e => setBill(e.target.value)} style={{ width:'100%' }} />
        </div>
        <div className="form-group">
          <label>Consumption Type</label>
          <select value={consumptionType} onChange={e => setConsumptionType(e.target.value)} style={{ width:'100%' }}>
            {TARIFFS.map(t => <option key={t.value} value={t.value}>{t.label}</option>)}
          </select>
        </div>
        {consumptionType === 'custom_tariff' && (
          <div className="form-group">
            <label>Custom Tariff Rate (EGP per kWh)</label>
            <input type="number" min="0" step="0.01" value={customTariff} onChange={e => setCustomTariff(e.target.value)} style={{ width:'100%' }} />
          </div>
        )}
        <div style={{ display:'flex', gap:8, marginTop:16 }}>
          <button onClick={calculate} style={{ flex:1, background: 'var(--color-surface)', color: 'var(--color-text)', border:'none', borderRadius:5, padding:'10px 0', fontWeight:600 }}>Calculate</button>
          <button onClick={onClose} style={{ flex:1, background: 'var(--color-surface)', color: 'var(--color-text)', border:'none', borderRadius:5, padding:'10px 0', fontWeight:600 }}>Cancel</button>
        </div>
        {result && (
          <div style={{ marginTop:18, background:'var(--color-surface)', borderRadius:6, padding:12, textAlign:'center' }}>
            <div>Your max allowed solar capacity is: <b>{result.maxPV.toFixed(2)}</b> kWp</div>
            <div>Estimated monthly consumption: <b>{result.monthlyConsumption.toFixed(2)}</b> kWh</div>
            <button onClick={handleApply} style={{ marginTop:12, width:'100%', background:'#10b981', color: 'var(--color-text)', border:'none', borderRadius:5, padding:'10px 0', fontWeight:600 }}>Apply to Calculator</button>
          </div>
        )}
      </div>
    </div>
  );
}
