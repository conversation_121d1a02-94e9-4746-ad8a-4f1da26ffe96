/**
 * Clipper2 Geometry Operations
 *
 * Provides geometry operations using Clipper2 WASM to replace Martinez functionality
 * Maintains API compatibility with existing Martinez usage
 */

import { initializeClipper2, getClipper2Instance, isClipper2Initialized } from './wasmLoader';
import { startShadowCalculation } from './shadowCalculationMonitor';

/**
 * Custom error class for Clipper2 operations
 */
export class Clipper2OperationError extends Error {
  constructor(message, originalError = null) {
    super(message);
    this.name = 'Clipper2OperationError';
    this.originalError = originalError;
  }
}

// Coordinate scaling factor to convert geographic degrees to suitable range for Clipper2
const COORDINATE_SCALE = 1000000; // Scale by 1 million to convert degrees to a suitable integer range

/**
 * Convert coordinates from Martinez format to Clipper2 PathsD format
 * Martinez uses [[[lng, lat], [lng, lat], ...]] format
 * Clipper2 uses PathsD with PathD containing PointD objects
 *
 * Note: Geographic coordinates are scaled up to avoid precision issues in Clipper2
 */
const convertToClipper2PathsD = (martinezCoords, clipper2) => {
  if (!martinezCoords || !Array.isArray(martinezCoords)) {
    throw new Clipper2OperationError('Invalid coordinates format');
  }

  console.log(`[convertToClipper2PathsD] Converting ${martinezCoords.length} rings to PathsD with scale factor ${COORDINATE_SCALE}`);
  const pathsD = new clipper2.PathsD();

  // Martinez format: [[[lng, lat], [lng, lat], ...]]
  martinezCoords.forEach((ring, ringIndex) => {
    if (!ring || !Array.isArray(ring)) {
      console.warn(`[convertToClipper2PathsD] Invalid ring ${ringIndex}:`, ring);
      return;
    }

    console.log(`[convertToClipper2PathsD] Converting ring ${ringIndex} with ${ring.length} points`);
    const pathD = new clipper2.PathD();

    ring.forEach((coord, coordIndex) => {
      if (!coord || !Array.isArray(coord) || coord.length < 2) {
        console.warn(`[convertToClipper2PathsD] Invalid coordinate ${coordIndex} in ring ${ringIndex}:`, coord);
        return;
      }

      // Scale coordinates to avoid precision issues with geographic degrees
      const scaledX = coord[0] * COORDINATE_SCALE;
      const scaledY = coord[1] * COORDINATE_SCALE;

      if (coordIndex < 3) { // Log first 3 coordinates for debugging
        console.log(`[convertToClipper2PathsD] Ring ${ringIndex}, Coord ${coordIndex}: [${coord[0]}, ${coord[1]}] -> scaled [${scaledX}, ${scaledY}]`);
      }

      const pointD = new clipper2.PointD(scaledX, scaledY, 0);
      pathD.push_back(pointD);
    });

    if (pathD.size() >= 3) { // Need at least 3 points for a valid polygon
      pathsD.push_back(pathD);
      console.log(`[convertToClipper2PathsD] Added ring ${ringIndex} with ${pathD.size()} points to PathsD`);
    } else {
      console.warn(`[convertToClipper2PathsD] Skipping ring ${ringIndex} - insufficient points (${pathD.size()})`);
      pathD.delete(); // Clean up unused PathD
    }
  });

  console.log(`[convertToClipper2PathsD] Final PathsD has ${pathsD.size()} paths`);
  return pathsD;
};

/**
 * Convert Clipper2 PathsD result back to Martinez format
 */
const convertFromClipper2PathsD = (pathsD) => {
  if (!pathsD) {
    console.log('[convertFromClipper2PathsD] No pathsD provided');
    return [];
  }

  const result = [];
  const pathCount = pathsD.size();

  console.log(`[convertFromClipper2PathsD] Converting ${pathCount} paths from Clipper2`);

  for (let i = 0; i < pathCount; i++) {
    const pathD = pathsD.get(i);
    const pointCount = pathD.size();
    const ring = [];

    console.log(`[convertFromClipper2PathsD] Path ${i} has ${pointCount} points`);

    for (let j = 0; j < pointCount; j++) {
      const pointD = pathD.get(j);
      // Scale coordinates back down to geographic range
      const originalX = pointD.x / COORDINATE_SCALE;
      const originalY = pointD.y / COORDINATE_SCALE;
      ring.push([originalX, originalY]);
    }

    if (ring.length > 0) {
      console.log(`[convertFromClipper2PathsD] Path ${i} converted to ring with ${ring.length} points:`, ring.slice(0, 3));
      result.push([ring]); // Martinez expects nested array format
    }
  }

  console.log(`[convertFromClipper2PathsD] Final result: ${result.length} polygons`);
  return result;
};

/**
 * Ensure Clipper2 is initialized before operations
 */
const ensureClipper2Ready = async () => {
  if (!isClipper2Initialized()) {
    await initializeClipper2();
  }
  
  const instance = getClipper2Instance();
  if (!instance) {
    throw new Clipper2OperationError('Clipper2 WASM instance not available');
  }
  
  return instance;
};

/**
 * Union operation - combines multiple polygons into one
 * @param {...Array} polygons - Polygons in Martinez format
 * @returns {Array} Result in Martinez format
 */
export const unionPolygonsClipper2 = async (...polygons) => {
  let subjectPaths = null;
  let clipPaths = null;
  let resultPaths = null;

  // Start performance monitoring
  const monitor = startShadowCalculation('clipper2', polygons.length, 'union');

  try {
    const clipper2 = await ensureClipper2Ready();

    if (polygons.length === 0) {
      monitor.end(true, 0);
      return [];
    }

    if (polygons.length === 1) {
      monitor.end(true, 1);
      return polygons[0];
    }

    console.log(`[clipper2Operations] Union operation with ${polygons.length} polygons`);

    // Convert first polygon to subject
    subjectPaths = convertToClipper2PathsD(polygons[0], clipper2);

    // Union with remaining polygons one by one
    for (let i = 1; i < polygons.length; i++) {
      clipPaths = convertToClipper2PathsD(polygons[i], clipper2);

      // Perform union operation using UnionD function
      console.log(`[unionPolygonsClipper2] Performing union operation ${i} of ${polygons.length - 1}`);
      const tempResult = clipper2.UnionD(subjectPaths, clipPaths, clipper2.FillRule.NonZero, 2);

      // Clean up previous subject paths
      if (subjectPaths) {
        subjectPaths.delete();
      }

      subjectPaths = tempResult;

      // Clean up clip paths
      if (clipPaths) {
        clipPaths.delete();
      }
      clipPaths = null;
    }

    // Convert result back to Martinez format
    const martinezResult = convertFromClipper2PathsD(subjectPaths);

    console.log(`[clipper2Operations] Union completed, result has ${martinezResult.length} parts`);
    monitor.end(true, martinezResult.length);
    return martinezResult;

  } catch (error) {
    console.error('[clipper2Operations] Union operation failed:', error);
    monitor.end(false, 0, error.message);
    throw new Clipper2OperationError(`Union operation failed: ${error.message}`, error);
  } finally {
    // Clean up memory
    if (subjectPaths) subjectPaths.delete();
    if (clipPaths) clipPaths.delete();
    if (resultPaths) resultPaths.delete();
  }
};

/**
 * Intersection operation - finds overlapping areas between polygons
 * @param {Array} polygon1 - First polygon in Martinez format
 * @param {Array} polygon2 - Second polygon in Martinez format
 * @returns {Array} Result in Martinez format
 */
export const intersectPolygonsClipper2 = async (polygon1, polygon2) => {
  let subjectPaths = null;
  let clipPaths = null;
  let resultPaths = null;

  // Start performance monitoring
  const monitor = startShadowCalculation('clipper2', 2, 'intersection');

  try {
    const clipper2 = await ensureClipper2Ready();

    if (!polygon1 || !polygon2) {
      monitor.end(true, 0);
      return [];
    }

    console.log('[clipper2Operations] Intersection operation');

    // Convert polygons to Clipper2 PathsD format
    subjectPaths = convertToClipper2PathsD(polygon1, clipper2);
    clipPaths = convertToClipper2PathsD(polygon2, clipper2);

    // Perform intersection operation using IntersectD function
    resultPaths = clipper2.IntersectD(subjectPaths, clipPaths, clipper2.FillRule.NonZero, 2);

    // Convert back to Martinez format
    const martinezResult = convertFromClipper2PathsD(resultPaths);

    console.log(`[clipper2Operations] Intersection completed, result has ${martinezResult.length} parts`);
    monitor.end(true, martinezResult.length);
    return martinezResult;

  } catch (error) {
    console.error('[clipper2Operations] Intersection operation failed:', error);
    monitor.end(false, 0, error.message);
    throw new Clipper2OperationError(`Intersection operation failed: ${error.message}`, error);
  } finally {
    // Clean up memory
    if (subjectPaths) subjectPaths.delete();
    if (clipPaths) clipPaths.delete();
    if (resultPaths) resultPaths.delete();
  }
};

/**
 * Difference operation - subtracts second polygon from first
 * @param {Array} polygon1 - First polygon in Martinez format
 * @param {Array} polygon2 - Second polygon in Martinez format
 * @returns {Array} Result in Martinez format
 */
export const differencePolygonsClipper2 = async (polygon1, polygon2) => {
  let subjectPaths = null;
  let clipPaths = null;
  let resultPaths = null;

  try {
    const clipper2 = await ensureClipper2Ready();

    if (!polygon1) {
      return [];
    }

    if (!polygon2) {
      return polygon1;
    }

    console.log('[clipper2Operations] Difference operation');

    // Convert polygons to Clipper2 PathsD format
    subjectPaths = convertToClipper2PathsD(polygon1, clipper2);
    clipPaths = convertToClipper2PathsD(polygon2, clipper2);

    // Perform difference operation using DifferenceD function
    resultPaths = clipper2.DifferenceD(subjectPaths, clipPaths, clipper2.FillRule.NonZero, 2);

    // Convert back to Martinez format
    const martinezResult = convertFromClipper2PathsD(resultPaths);

    console.log(`[clipper2Operations] Difference completed, result has ${martinezResult.length} parts`);
    return martinezResult;

  } catch (error) {
    console.error('[clipper2Operations] Difference operation failed:', error);
    throw new Clipper2OperationError(`Difference operation failed: ${error.message}`, error);
  } finally {
    // Clean up memory
    if (subjectPaths) subjectPaths.delete();
    if (clipPaths) clipPaths.delete();
    if (resultPaths) resultPaths.delete();
  }
};

/**
 * Martinez API compatibility layer
 * Provides the same interface as Martinez for easy replacement
 */
export const martinez = {
  union: unionPolygonsClipper2,
  intersection: intersectPolygonsClipper2,
  difference: differencePolygonsClipper2
};

// Basic test function is exported inline above

/**
 * Simple test to verify Clipper2 WASM is working
 * @returns {Promise<boolean>} True if basic WASM functionality works
 */
export const testClipper2Basic = async () => {
  try {
    console.log('[testClipper2Basic] Testing basic Clipper2 WASM functionality...');

    const clipper2 = await ensureClipper2Ready();

    // Test basic PathsD creation
    const pathsD = new clipper2.PathsD();
    const pathD = new clipper2.PathD();

    // Add some simple points
    pathD.push_back(new clipper2.PointD(0, 0, 0));
    pathD.push_back(new clipper2.PointD(1, 0, 0));
    pathD.push_back(new clipper2.PointD(1, 1, 0));
    pathD.push_back(new clipper2.PointD(0, 1, 0));

    pathsD.push_back(pathD);

    console.log('[testClipper2Basic] Created PathsD with size:', pathsD.size());
    console.log('[testClipper2Basic] PathD has points:', pathD.size());

    // Test UnionSelfD (simplest operation)
    const result = clipper2.UnionSelfD(pathsD, clipper2.FillRule.NonZero, 2);
    console.log('[testClipper2Basic] UnionSelfD result size:', result.size());

    // Clean up
    pathsD.delete();
    result.delete();

    console.log('[testClipper2Basic] Basic WASM test passed!');
    return true;

  } catch (error) {
    console.error('[testClipper2Basic] Basic WASM test failed:', error);
    return false;
  }
};

/**
 * Test Clipper2 operations with simple polygons
 * @returns {Promise<boolean>} True if tests pass
 */
export const testClipper2Operations = async () => {
  try {
    console.log('[clipper2Operations] Running basic tests...');

    // First test basic WASM functionality
    const basicTest = await testClipper2Basic();
    if (!basicTest) {
      console.error('[clipper2Operations] Basic WASM test failed, skipping advanced tests');
      return false;
    }

    // Simple test polygons (Martinez format: [[[lng, lat], [lng, lat], ...]])
    const poly1 = [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]];
    const poly2 = [[[0.5, 0.5], [1.5, 0.5], [1.5, 1.5], [0.5, 1.5], [0.5, 0.5]]];

    console.log('[clipper2Operations] Input poly1:', poly1);
    console.log('[clipper2Operations] Input poly2:', poly2);

    // Test intersection
    const intersection = await intersectPolygonsClipper2(poly1, poly2);
    console.log('[clipper2Operations] Intersection test result:', intersection);

    // Test union
    const union = await unionPolygonsClipper2(poly1, poly2);
    console.log('[clipper2Operations] Union test result:', union);

    // Test difference
    const difference = await differencePolygonsClipper2(poly1, poly2);
    console.log('[clipper2Operations] Difference test result:', difference);

    console.log('[clipper2Operations] Basic tests completed successfully');
    return true;

  } catch (error) {
    console.error('[clipper2Operations] Tests failed:', error);
    return false;
  }
};
