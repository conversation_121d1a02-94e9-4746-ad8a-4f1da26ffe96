/**
 * Clipper2 Replacement for Martinez Operations
 * 
 * Provides drop-in replacements for Martinez polygon operations using Clipper2 WASM
 * Maintains the same API as <PERSON> for easy integration
 */

import { intersectPolygonsClipper2, unionPolygonsClipper2, differencePolygonsClipper2 } from './clipper2Operations';
import { isClipper2Initialized, safeInitializeClipper2 } from './wasmLoader';
import * as martinez from 'martinez-polygon-clipping'; // Fallback

/**
 * Initialize Clipper2 if not already initialized
 * @returns {Promise<boolean>} True if initialized successfully
 */
const ensureClipper2Ready = async () => {
  if (!isClipper2Initialized()) {
    const result = await safeInitializeClipper2();
    return result.success;
  }
  return true;
};

/**
 * Clipper2-based intersection with <PERSON> fallback
 * @param {Array} polygon1 - First polygon in Martinez format
 * @param {Array} polygon2 - Second polygon in Martinez format
 * @returns {Promise<Array>} Intersection result
 */
export const intersection = async (polygon1, polygon2) => {
  try {
    const clipper2Ready = await ensureClipper2Ready();
    
    if (clipper2Ready) {
      console.log('[clipper2Replacement] Using Clipper2 for intersection');
      return await intersectPolygonsClipper2(polygon1, polygon2);
    } else {
      console.log('[clipper2Replacement] Clipper2 not available, using Martinez fallback for intersection');
      return martinez.intersection(polygon1, polygon2);
    }
  } catch (error) {
    console.warn('[clipper2Replacement] Clipper2 intersection failed, using Martinez fallback:', error);
    return martinez.intersection(polygon1, polygon2);
  }
};

/**
 * Clipper2-based union with Martinez fallback
 * @param {...Array} polygons - Polygons to union
 * @returns {Promise<Array>} Union result
 */
export const union = async (...polygons) => {
  try {
    const clipper2Ready = await ensureClipper2Ready();
    
    if (clipper2Ready) {
      console.log('[clipper2Replacement] Using Clipper2 for union');
      return await unionPolygonsClipper2(...polygons);
    } else {
      console.log('[clipper2Replacement] Clipper2 not available, using Martinez fallback for union');
      return martinez.union(...polygons);
    }
  } catch (error) {
    console.warn('[clipper2Replacement] Clipper2 union failed, using Martinez fallback:', error);
    return martinez.union(...polygons);
  }
};

/**
 * Clipper2-based difference with Martinez fallback
 * @param {Array} polygon1 - First polygon
 * @param {Array} polygon2 - Second polygon
 * @returns {Promise<Array>} Difference result
 */
export const difference = async (polygon1, polygon2) => {
  try {
    const clipper2Ready = await ensureClipper2Ready();
    
    if (clipper2Ready) {
      console.log('[clipper2Replacement] Using Clipper2 for difference');
      return await differencePolygonsClipper2(polygon1, polygon2);
    } else {
      console.log('[clipper2Replacement] Clipper2 not available, using Martinez fallback for difference');
      return martinez.difference(polygon1, polygon2);
    }
  } catch (error) {
    console.warn('[clipper2Replacement] Clipper2 difference failed, using Martinez fallback:', error);
    return martinez.difference(polygon1, polygon2);
  }
};

/**
 * Synchronous Martinez-compatible API (for backward compatibility)
 * These functions will attempt to use cached Clipper2 results or fall back to Martinez
 */
export const martinezCompatible = {
  /**
   * Synchronous intersection (falls back to Martinez)
   */
  intersection: (polygon1, polygon2) => {
    console.log('[clipper2Replacement] Synchronous intersection call, using Martinez');
    return martinez.intersection(polygon1, polygon2);
  },

  /**
   * Synchronous union (falls back to Martinez)
   */
  union: (...polygons) => {
    console.log('[clipper2Replacement] Synchronous union call, using Martinez');
    return martinez.union(...polygons);
  },

  /**
   * Synchronous difference (falls back to Martinez)
   */
  difference: (polygon1, polygon2) => {
    console.log('[clipper2Replacement] Synchronous difference call, using Martinez');
    return martinez.difference(polygon1, polygon2);
  }
};

/**
 * Enhanced intersection with better error handling and logging
 * @param {Array} polygon1 - First polygon
 * @param {Array} polygon2 - Second polygon
 * @param {string} context - Context for logging (e.g., 'fence shadow', 'PV table')
 * @returns {Promise<Array>} Intersection result
 */
export const enhancedIntersection = async (polygon1, polygon2, context = 'unknown') => {
  console.log(`[clipper2Replacement] Enhanced intersection for ${context}`);
  
  if (!polygon1 || !polygon2) {
    console.warn(`[clipper2Replacement] Invalid polygons for ${context} intersection`);
    return [];
  }

  try {
    const result = await intersection(polygon1, polygon2);
    console.log(`[clipper2Replacement] ${context} intersection completed, result has ${result ? result.length : 0} parts`);
    return result || [];
  } catch (error) {
    console.error(`[clipper2Replacement] ${context} intersection failed:`, error);
    return [];
  }
};

/**
 * Batch intersection operations for multiple polygon pairs
 * @param {Array} polygonPairs - Array of [polygon1, polygon2] pairs
 * @param {string} context - Context for logging
 * @returns {Promise<Array>} Array of intersection results
 */
export const batchIntersection = async (polygonPairs, context = 'batch') => {
  console.log(`[clipper2Replacement] Batch intersection for ${context}, ${polygonPairs.length} pairs`);
  
  const results = [];
  
  for (let i = 0; i < polygonPairs.length; i++) {
    const [polygon1, polygon2] = polygonPairs[i];
    try {
      const result = await intersection(polygon1, polygon2);
      results.push(result);
    } catch (error) {
      console.error(`[clipper2Replacement] Batch intersection failed for pair ${i}:`, error);
      results.push([]);
    }
  }
  
  console.log(`[clipper2Replacement] Batch intersection completed, ${results.length} results`);
  return results;
};

/**
 * Performance monitoring wrapper
 * @param {Function} operation - The operation to monitor
 * @param {string} operationName - Name for logging
 * @returns {Function} Wrapped operation
 */
export const withPerformanceMonitoring = (operation, operationName) => {
  return async (...args) => {
    const startTime = performance.now();
    try {
      const result = await operation(...args);
      const endTime = performance.now();
      console.log(`[clipper2Replacement] ${operationName} completed in ${(endTime - startTime).toFixed(2)}ms`);
      return result;
    } catch (error) {
      const endTime = performance.now();
      console.error(`[clipper2Replacement] ${operationName} failed after ${(endTime - startTime).toFixed(2)}ms:`, error);
      throw error;
    }
  };
};

// Export performance-monitored versions
export const monitoredIntersection = withPerformanceMonitoring(intersection, 'intersection');
export const monitoredUnion = withPerformanceMonitoring(union, 'union');
export const monitoredDifference = withPerformanceMonitoring(difference, 'difference');
