import React from 'react';
import { Line, Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Legend,
  Tooltip,
} from 'chart.js';

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, BarElement, Legend, Tooltip);

export default function FinancialCharts({ summary }) {
  if (!summary) return null;
  const years = summary.years;
  return (
    <div style={{ marginTop: 0 }}>
      <div className="chart-container">
        <h3>Net and Cumulative Profit</h3>
        <Bar
          data={{
            labels: years,
            datasets: [
              {
                label: 'Net Profit (EGP)',
                data: summary.netProfit,
                backgroundColor: 'rgba(239, 68, 68, 0.8)',
                borderColor: 'rgb(239, 68, 68)',
                borderWidth: 1,
                borderRadius: 4,
                barPercentage: 0.6,
                categoryPercentage: 0.8,
                order: 2
              },
              {
                label: 'Cumulative Profit (EGP)',
                data: summary.cumulativeProfit,
                backgroundColor: 'rgba(16, 185, 129, 0.8)',
                borderColor: 'rgb(16, 185, 129)',
                borderWidth: 1,
                borderRadius: 4,
                barPercentage: 0.6,
                categoryPercentage: 0.8,
                order: 1
              },
            ],
          }}
          options={{
            responsive: true,
            plugins: { 
              legend: { display: true },
              tooltip: {
                callbacks: {
                  label: function(context) {
                    return `${context.dataset.label}: ${context.raw.toLocaleString()} EGP`;
                  }
                }
              }
            },
            scales: {
              y: { 
                title: { display: true, text: 'EGP' },
                grid: { color: 'rgba(0, 0, 0, 0.05)' }
              },
              x: { 
                title: { display: true, text: 'Year' },
                grid: { display: false }
              },
            },
          }}
          height={350}
        />
      </div>
    </div>
  );
}
