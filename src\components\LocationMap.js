import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, useMapEvents, useMap } from 'react-leaflet'; // LayersControl removed
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { FaSatelliteDish, FaMap, FaLayerGroup } from 'react-icons/fa';

// Fix default marker icon issue in Leaflet + React
import iconUrl from 'leaflet/dist/images/marker-icon.png';
import iconShadow from 'leaflet/dist/images/marker-shadow.png';

const defaultIcon = L.icon({
  iconUrl,
  shadowUrl: iconShadow,
  iconAnchor: [12, 41],
});
L.Marker.prototype.options.icon = defaultIcon;

const DEFAULT_POSITION = { lat: 30.0444, lng: 31.2357 }; // Cairo

// Satellite imagery providers with high-quality options
const SATELLITE_PROVIDERS = {
  esri: {
    name: 'ESRI World Imagery',
    url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
    attribution: 'Tiles © Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
    maxZoom: 23
  },
  esriClarity: {
    name: 'ESRI Clarity (Beta)',
    url: 'https://clarity.maptiles.arcgis.com/arcgis/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
    attribution: 'Esri, Maxar, Earthstar Geographics, and the GIS User Community',
    maxZoom: 23
  },
  google: {
    name: 'Google Satellite',
    url: 'https://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={z}',
    attribution: '© Google',
    maxZoom: 22
  },
  mapbox: {
    name: 'Mapbox Satellite',
    url: 'https://api.mapbox.com/styles/v1/mapbox/satellite-v9/tiles/{z}/{x}/{y}?access_token=pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw',
    attribution: '© Mapbox © OpenStreetMap',
    maxZoom: 22
  }
};

// Component to handle map clicks
function LocationEvents({ onLocationChange }) {
  useMapEvents({
    click(e) {
      onLocationChange(e.latlng);
    },
  });
  return null;
}

// Component to handle map navigation when location changes
function MapNavigator({ location }) {
  const map = useMap();
  
  useEffect(() => {
    if (location) {
      map.flyTo([location.lat, location.lng], map.getZoom());
    }
  }, [location, map]);
  
  return null;
}

export default function LocationMap({ location, setLocation, setLocationName }) {
  const [searchLat, setSearchLat] = useState('');
  const [searchLng, setSearchLng] = useState('');
  const [displayName, setDisplayName] = useState('Not selected');
  const [isSatellite, setIsSatellite] = useState(false);
  const [satelliteProvider, setSatelliteProvider] = useState('esriClarity'); // Default to high-res ESRI Clarity
  const [showProviderMenu, setShowProviderMenu] = useState(false);
  const mapRef = useRef(null);

  // Reverse geocode to get location name
  useEffect(() => {
    async function fetchLocationName(lat, lng) {
      try {
        const res = await fetch(
          `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=10`
        );
        const data = await res.json();
        if (data && data.display_name) {
          setDisplayName(data.display_name);
          if (setLocationName) setLocationName(data.display_name);
        } else {
          setDisplayName(`${lat.toFixed(2)}, ${lng.toFixed(2)}`);
        }
      } catch {
        setDisplayName(`${lat.toFixed(2)}, ${lng.toFixed(2)}`);
      }
    }
    if (location) fetchLocationName(location.lat, location.lng);
  }, [location, setLocationName]);

  // Handle coordinate search
  const handleSearch = (e) => {
    e.preventDefault();
    const lat = parseFloat(searchLat);
    const lng = parseFloat(searchLng);
    if (!isNaN(lat) && !isNaN(lng) && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180) {
      // Just set the location - the MapNavigator component will handle the map navigation
      setLocation({ lat, lng });
    } else {
      alert('Please enter valid coordinates.');
    }
  };
  
  // Satellite/OSM toggle handler
  const handleToggleMap = () => {
    setIsSatellite((prev) => {
      // When switching to satellite, adjust zoom level for better detail if needed
      if (!prev && mapRef.current) {
        const currentZoom = mapRef.current.getZoom();
        if (currentZoom < 16) {
          mapRef.current.setZoom(16);
        }
      }
      return !prev;
    });
    setShowProviderMenu(false); // Close provider menu when toggling
  };
  
  // Handle satellite provider change
  const handleSatelliteProviderChange = (provider) => {
    setSatelliteProvider(provider);
    setShowProviderMenu(false);
  };

  return (
    <div className="map-container" style={{ width: '100%', maxWidth: 450 }}>
      <form onSubmit={handleSearch} className="coordinate-search" style={{ 
        marginBottom: 10,
        display: 'flex', 
        flexWrap: 'wrap',
        gap: '8px',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}>
        <div style={{ 
          display: 'flex', 
          flexWrap: 'wrap', 
          gap: '8px',
          flex: '1 1 auto',
          minWidth: '200px'
        }}>
          <input
            type="number"
            step="0.0001"
            placeholder="Latitude (-90 to 90)"
            value={searchLat}
            onChange={e => setSearchLat(e.target.value)}
            style={{ flex: '1 1 100px', minWidth: '100px' }}
          />
          <input
            type="number"
            step="0.0001"
            placeholder="Longitude (-180 to 180)"
            value={searchLng}
            onChange={e => setSearchLng(e.target.value)}
            style={{ flex: '1 1 100px', minWidth: '100px' }}
          />
        </div>
        <button 
          type="submit" 
          style={{ 
            padding: '6px 12px',
            backgroundColor: '#2563eb',
            color: 'var(--color-text)',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            flex: '0 0 auto'
          }}
        >
          Find Location
        </button>
      </form>
      <MapContainer
        center={location || DEFAULT_POSITION}
        zoom={8}
        maxZoom={23}
        style={{ height: 350, width: '100%', borderRadius: 8, position: 'relative' }}
        scrollWheelZoom={true}
        ref={mapRef}
      >
        <TileLayer
          url={isSatellite
            ? SATELLITE_PROVIDERS[satelliteProvider].url
            : 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png'}
          attribution={isSatellite
            ? SATELLITE_PROVIDERS[satelliteProvider].attribution
            : '&copy; OpenStreetMap contributors'}
          maxNativeZoom={isSatellite ? SATELLITE_PROVIDERS[satelliteProvider].maxZoom : 19}
          maxZoom={23} // Corresponds to MapContainer's maxZoom, enables overzooming
        />
        {location && <Marker position={location} />}
        <LocationEvents onLocationChange={setLocation} />
        {/* Add the MapNavigator component to handle map navigation when location changes */}
        <MapNavigator location={location} />
        
        {/* Map controls container */}
        <div className="leaflet-bottom leaflet-left" style={{ marginBottom: '10px', marginLeft: '10px' }}>
          <div className="leaflet-control leaflet-bar" style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
            {/* Satellite provider selector (only visible when in satellite mode) */}
            {isSatellite && (
              <div style={{ position: 'relative' }}>
                <button 
                  type="button" 
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowProviderMenu(!showProviderMenu);
                  }}
                  style={{
                    width: '30px',
                    height: '30px',
                    background: 'var(--color-surface)',
                    border: '2px solid rgba(0,0,0,0.2)',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '22px',
                    color: 'var(--color-text)'
                  }}
                  title="Change Satellite Provider"
                >
                  <FaLayerGroup size={14} />
                </button>
                
                {/* Provider selection dropdown */}
                {showProviderMenu && (
                  <div 
                    style={{
                      position: 'absolute',
                      bottom: '40px',
                      left: '0',
                      background: 'var(--color-surface)',
                      border: '1px solid #ccc',
                      borderRadius: '4px',
                      boxShadow: '0 2px 10px rgba(0,0,0,0.2)',
                      zIndex: 1000,
                      width: '180px'
                    }}
                    onClick={(e) => e.stopPropagation()} // Prevent click from closing menu
                  >
                    {Object.keys(SATELLITE_PROVIDERS).map(key => (
                      <div 
                        key={key}
                        onClick={(e) => {
                          e.stopPropagation(); // Prevent event bubbling
                          handleSatelliteProviderChange(key);
                        }}
                        style={{
                          padding: '8px 12px',
                          borderBottom: '1px solid #eee',
                          cursor: 'pointer',
                          backgroundColor: satelliteProvider === key ? 'rgba(37,99,235,0.15)' : 'var(--color-surface)',
                          fontWeight: satelliteProvider === key ? 'bold' : 'normal'
                        }}
                      >
                        {SATELLITE_PROVIDERS[key].name}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
            
            {/* Satellite view toggle button */}
            <button 
              type="button" 
              onClick={handleToggleMap}
              style={{
                width: '36px',
                height: '36px',
                background: 'var(--color-surface)',
                border: '2px solid rgba(0,0,0,0.2)',
                borderRadius: '4px',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '22px',
                color: 'var(--color-text)'
              }}
              title={isSatellite ? 'Switch to Map View' : 'Switch to Satellite View'}
            >
              {isSatellite ? <FaMap size={30} /> : <FaSatelliteDish size={30} />}
            </button>
          </div>
        </div>
      </MapContainer>
      <div className="location-display" style={{ marginTop: 10 }}>
        <p>Selected Location: <span>{location ? `Lat: ${location.lat.toFixed(4)}, Lng: ${location.lng.toFixed(4)}` : 'Not selected'}</span></p>
        <div style={{ fontSize: 13, color: '#555', marginTop: 4 }}>{displayName}</div>
      </div>
    </div>
  );
}
