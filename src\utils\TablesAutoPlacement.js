/**
 * Grid-Based PV Table Auto-Placement Algorithm
 * 
 * This module implements a grid-based algorithm for automatically placing PV tables
 * at grid intersection points across non-shaded areas of a roof.
 * 
 * Key Features:
 * - Grid-based placement with azimuth-oriented grid overlay
 * - Grid intersection points as placement locations
 * - Dynamic modulesX calculation based on available space
 * - Obstacle and shadow avoidance
 * - Multi-polygon support for disconnected areas
 * - Compatible with existing manual PV table structure
 */

import * as martinez from 'martinez-polygon-clipping';
import { point as turfPoint, polygon as turfPolygonHelper } from '@turf/helpers';
import transformRotate from '@turf/transform-rotate';

/**
 * Smart difference operation using Clipper2 with Martinez fallback
 * @param {Array} polygon1 - First polygon in Martinez format
 * @param {Array} polygon2 - Second polygon in Martinez format
 * @returns {Promise<Array>} Difference result
 */
const smartDifference = async (polygon1, polygon2) => {
  // Temporarily use <PERSON> directly to avoid Clipper2 coordinate conversion issues
  console.log('[TablesAutoPlacement] Using <PERSON> for difference operation');
  return martinez.diff(polygon1, polygon2);
};
import bbox from '@turf/bbox';
import destination from '@turf/destination';
import booleanPointInPolygon from '@turf/boolean-point-in-polygon';

/**
 * Calculate clean non-shaded areas by subtracting obstacles and shadows from main roof
 * @param {Object} mainRoofLayer - Leaflet polygon layer for main roof
 * @param {Array} obstacles - Array of obstacle objects with layer property
 * @param {Array} shadowLayers - Array of shadow objects with coordinates
 * @returns {Array} Array of clean polygon geometries in GeoJSON format
 */
export const calculateNonShadedAreas = async (mainRoofLayer, obstacles = [], shadowLayers = []) => {
  console.log('[calculateNonShadedAreas] Starting calculation with:', {
    hasMainRoof: !!mainRoofLayer,
    obstacleCount: obstacles.length,
    shadowCount: shadowLayers.length
  });

  if (!mainRoofLayer) {
    console.warn('[calculateNonShadedAreas] No main roof layer provided');
    return [];
  }

  try {
    // Convert main roof to Martinez polygon format
    const roofGeoJSON = mainRoofLayer.toGeoJSON();
    let currentUsableGeom = roofGeoJSON.geometry.type === 'Polygon' 
      ? [roofGeoJSON.geometry.coordinates] 
      : roofGeoJSON.geometry.coordinates;

    console.log('[calculateNonShadedAreas] Initial roof geometry type:', roofGeoJSON.geometry.type);

    // Subtract obstacles from usable area
    for (const obstacle of obstacles) {
      if (obstacle.layer) {
        try {
          const obstacleGeoJSON = obstacle.layer.toGeoJSON().geometry;
          const martinezObstacleGeom = obstacleGeoJSON.type === 'Polygon'
            ? [obstacleGeoJSON.coordinates]
            : obstacleGeoJSON.coordinates;

          console.log(`[calculateNonShadedAreas] Processing obstacle ${obstacle.id || 'unknown'}:`);
          console.log('  - Obstacle GeoJSON type:', obstacleGeoJSON.type);
          console.log('  - Martinez geometry sample:', martinezObstacleGeom[0]?.slice(0, 2));

          if (currentUsableGeom && martinezObstacleGeom) {
            currentUsableGeom = await smartDifference(currentUsableGeom, martinezObstacleGeom);
            console.log(`[calculateNonShadedAreas] Subtracted obstacle ${obstacle.id || 'unknown'}`);
          }
        } catch (error) {
          console.error(`[calculateNonShadedAreas] Error processing obstacle:`, error);
        }
      }
    }

    // Subtract shadows from usable area
    for (const shadow of shadowLayers) {
      if (shadow.coordinates && Array.isArray(shadow.coordinates)) {
        try {
          let shadowMartinezGeom;
          
          // Handle different shadow coordinate formats
          if (shadow.coordinates.length > 0 && typeof shadow.coordinates[0] === 'object' && 'lat' in shadow.coordinates[0]) {
            // Format: [{lat, lng}, {lat, lng}, ...]
            shadowMartinezGeom = [[shadow.coordinates.map(p => [p.lng, p.lat])]];
          } else if (shadow.coordinates.length > 0 && Array.isArray(shadow.coordinates[0])) {
            // Format: [[lat, lng], [lat, lng], ...]
            shadowMartinezGeom = [[shadow.coordinates.map(p => [p[1], p[0]])]]; // Convert to [lng, lat]
          } else {
            console.warn('[calculateNonShadedAreas] Skipping shadow due to unexpected coordinate format:', shadow.coordinates);
            continue;
          }
          
          if (currentUsableGeom && shadowMartinezGeom) {
            currentUsableGeom = await smartDifference(currentUsableGeom, shadowMartinezGeom);
            console.log(`[calculateNonShadedAreas] Subtracted shadow ${shadow.id || 'unknown'}`);
          }
        } catch (error) {
          console.error(`[calculateNonShadedAreas] Error processing shadow:`, error);
        }
      }
    }

    if (!currentUsableGeom || currentUsableGeom.length === 0) {
      console.log('[calculateNonShadedAreas] No usable area remaining after subtractions');
      return [];
    }

    // Convert Martinez result to standard GeoJSON polygons
    const cleanAreas = [];
    
    if (Array.isArray(currentUsableGeom)) {
      for (const geom of currentUsableGeom) {
        if (geom && geom.length > 0) {
          // Each geom is a polygon with outer ring and possible holes
          const polygon = turfPolygonHelper(geom);
          cleanAreas.push(polygon.geometry);
        }
      }
    }

    console.log(`[calculateNonShadedAreas] Found ${cleanAreas.length} clean areas`);
    return cleanAreas;

  } catch (error) {
    console.error('[calculateNonShadedAreas] Error calculating non-shaded areas:', error);
    return [];
  }
};

/**
 * Calculate the area of a polygon from coordinates
 * @param {Array} coordinates - Array of [lat, lng] coordinate pairs
 * @returns {number} Area in square meters
 */
export const calculateAreaFromCoords = (coordinates) => {
  if (!coordinates || !Array.isArray(coordinates) || coordinates.length < 3) {
    return 0;
  }

  // Use the shoelace formula for polygon area calculation
  // Convert to projected coordinates for more accurate area calculation
  let area = 0;
  const n = coordinates.length;

  for (let i = 0; i < n; i++) {
    const j = (i + 1) % n;
    const [lat1, lng1] = coordinates[i];
    const [lat2, lng2] = coordinates[j];
    
    // Convert to meters using approximate conversion
    const x1 = lng1 * 111000 * Math.cos(lat1 * Math.PI / 180);
    const y1 = lat1 * 111000;
    const x2 = lng2 * 111000 * Math.cos(lat2 * Math.PI / 180);
    const y2 = lat2 * 111000;
    
    area += (x1 * y2 - x2 * y1);
  }

  return Math.abs(area) / 2;
};

/**
 * Sort polygon areas by size (largest first)
 * @param {Array} polygons - Array of polygon geometries
 * @returns {Array} Sorted array with area information
 */
export const sortPolygonsByArea = (polygons) => {
  return polygons.map(polygon => {
    const outerRing = polygon.coordinates[0];
    const area = calculateAreaFromCoords(outerRing.map(coord => [coord[1], coord[0]])); // Convert lng,lat to lat,lng
    return {
      geometry: polygon,
      area: area,
      coordinates: outerRing
    };
  }).sort((a, b) => b.area - a.area);
};

/**
 * Check if a polygon area is large enough for PV table placement
 * @param {Object} polygonInfo - Polygon with area information
 * @param {Object} tableParams - PV table parameters
 * @returns {boolean} True if area is suitable for placement
 */
export const isAreaSuitableForPlacement = (polygonInfo, tableParams) => {
  const { panelLength, panelWidth, stackNumber } = tableParams;
  
  // Calculate minimum area needed for at least one table
  const tableLength = panelLength * stackNumber; // Y direction (stacked modules)
  const tableWidth = panelWidth; // X direction (will be calculated dynamically)
  const minAreaNeeded = tableLength * tableWidth;
  
  // Add some buffer for spacing and practical placement
  const bufferFactor = 1.5;
  const requiredArea = minAreaNeeded * bufferFactor;
  
  console.log(`[isAreaSuitableForPlacement] Area: ${polygonInfo.area.toFixed(2)} m², Required: ${requiredArea.toFixed(2)} m²`);
  
  return polygonInfo.area >= requiredArea;
};

/**
 * Get the bounding box of a polygon
 * @param {Array} coordinates - Array of [lng, lat] coordinates
 * @returns {Object} Bounding box with north, south, east, west properties
 */
export const getPolygonBounds = (coordinates) => {
  if (!coordinates || coordinates.length === 0) {
    return null;
  }

  let north = -Infinity, south = Infinity, east = -Infinity, west = Infinity;
  
  for (const [lng, lat] of coordinates) {
    north = Math.max(north, lat);
    south = Math.min(south, lat);
    east = Math.max(east, lng);
    west = Math.min(west, lng);
  }

  return { north, south, east, west };
};

/**
 * Calculate grid orientation based on azimuth angle
 * @param {number} azimuthAngle - Azimuth angle in degrees (0=South, -90=East, +90=West)
 * @returns {Object} Grid orientation information
 */
export const calculateGridOrientation = (azimuthAngle) => {
  // Convert azimuth to radians
  const azimuthRad = azimuthAngle * Math.PI / 180;

  // Grid lines run perpendicular to azimuth (along the rows)
  // For proper spacing, grid lines should be perpendicular to the panel normal
  const gridDirectionRad = azimuthRad + Math.PI / 2;
  let gridDirectionDeg = (gridDirectionRad * 180 / Math.PI);

  // Normalize angle to 0-360 range
  while (gridDirectionDeg < 0) gridDirectionDeg += 360;
  while (gridDirectionDeg >= 360) gridDirectionDeg -= 360;

  // Spacing direction is perpendicular to grid direction (between rows)
  const spacingDirectionRad = azimuthRad;
  let spacingDirectionDeg = azimuthAngle;

  // Normalize spacing direction angle
  while (spacingDirectionDeg < 0) spacingDirectionDeg += 360;
  while (spacingDirectionDeg >= 360) spacingDirectionDeg -= 360;

  return {
    azimuthAngle,
    azimuthRad,
    gridDirectionRad,
    gridDirectionDeg,
    // Direction for spacing between grid lines (perpendicular to panels)
    spacingDirectionRad,
    spacingDirectionDeg
  };
};

/**
 * Generate grid intersection points within a polygon area
 * @param {Object} polygonInfo - Polygon with area and coordinates
 * @param {Object} tableParams - PV table parameters
 * @param {Object} gridOrientation - Grid orientation information
 * @returns {Array} Array of grid intersection points
 */
export const generateGridPoints = (polygonInfo, tableParams, gridOrientation) => {
  const { panelLength, stackNumber, rowSpacing } = tableParams;
  const { coordinates } = polygonInfo;

  // Calculate table dimensions
  const tableDepth = panelLength * stackNumber; // Y direction (perpendicular to grid)
  const totalRowSpacing = tableDepth + rowSpacing;

  console.log(`[generateGridPoints] Table depth: ${tableDepth}m, Row spacing: ${rowSpacing}m, Total spacing: ${totalRowSpacing}m`);

  // Get polygon bounds
  const bounds = getPolygonBounds(coordinates);
  if (!bounds) return [];

  // Calculate grid direction vectors
  const gridDirRad = gridOrientation.gridDirectionRad; // Along grid lines
  const spacingDirRad = gridOrientation.spacingDirectionRad; // Between grid lines
  const spacingDirDeg = gridOrientation.spacingDirectionDeg;

  // Start from polygon center
  const centerLat = (bounds.north + bounds.south) / 2;
  const centerLng = (bounds.east + bounds.west) / 2;
  const polygonCenter = turfPoint([centerLng, centerLat]);
  const polygon = turfPolygonHelper([coordinates]);

  console.log(`[generateGridPoints] Polygon bounds:`, bounds);
  console.log(`[generateGridPoints] Polygon center: [${centerLat.toFixed(6)}, ${centerLng.toFixed(6)}]`);
  console.log(`[generateGridPoints] Azimuth: ${gridOrientation.azimuthAngle}°, Grid direction: ${gridOrientation.gridDirectionDeg.toFixed(1)}°, Spacing direction: ${spacingDirDeg.toFixed(1)}°`);

  // Find grid line positions (perpendicular to azimuth direction)
  const gridLines = [];

  // Sample in both spacing directions to find grid line positions
  let maxPositiveDistance = 0;
  let maxNegativeDistance = 0;

  // Check positive spacing direction - find the furthest point that's still inside
  for (let dist = 0.001; dist < 1; dist += 0.005) { // Start from 1m, 5m steps, up to 1km
    const testPoint = destination(polygonCenter, dist, spacingDirDeg);
    if (booleanPointInPolygon(testPoint, polygon)) {
      maxPositiveDistance = dist * 1000; // Convert to meters
    }
    // Don't break - continue to find the maximum extent
  }

  // Check negative spacing direction - find the furthest point that's still inside
  for (let dist = 0.001; dist < 1; dist += 0.005) { // Start from 1m, 5m steps, up to 1km
    const testPoint = destination(polygonCenter, dist, spacingDirDeg + 180);
    if (booleanPointInPolygon(testPoint, polygon)) {
      maxNegativeDistance = dist * 1000; // Convert to meters
    }
    // Don't break - continue to find the maximum extent
  }

  const totalAvailableWidth = maxPositiveDistance + maxNegativeDistance;
  const maxGridLines = Math.floor(totalAvailableWidth / totalRowSpacing);

  console.log(`[generateGridPoints] Available width: ${totalAvailableWidth.toFixed(2)}m, Max grid lines: ${maxGridLines}`);

  // Generate grid line positions
  for (let i = 0; i < maxGridLines; i++) {
    // Calculate offset from center in the spacing direction
    const offsetDistance = (i - (maxGridLines - 1) / 2) * totalRowSpacing;

    // Calculate grid line center position
    const gridLineCenter = destination(
      polygonCenter,
      Math.abs(offsetDistance) / 1000, // Convert to km
      offsetDistance >= 0 ? spacingDirDeg : spacingDirDeg + 180
    );

    gridLines.push({
      index: i,
      center: gridLineCenter.geometry.coordinates, // [lng, lat]
      direction: gridDirRad, // Grid line direction
      offsetDistance
    });

    console.log(`[generateGridPoints] Grid line ${i}: offset ${offsetDistance.toFixed(2)}m, center [${gridLineCenter.geometry.coordinates[1].toFixed(6)}, ${gridLineCenter.geometry.coordinates[0].toFixed(6)}]`);
  }

  // Generate grid intersection points along each grid line
  const gridPoints = [];
  const gridDirDeg = gridOrientation.gridDirectionDeg;

  for (const gridLine of gridLines) {
    const lineCenter = turfPoint(gridLine.center);

    // Find extent of this grid line within the polygon
    let maxPositiveLineDistance = 0;
    let maxNegativeLineDistance = 0;

    // Check positive grid direction - find the furthest point that's still inside
    for (let dist = 0.001; dist < 1; dist += 0.005) { // Start from 1m, 5m steps, up to 1km
      const testPoint = destination(lineCenter, dist, gridDirDeg);
      if (booleanPointInPolygon(testPoint, polygon)) {
        maxPositiveLineDistance = dist * 1000; // Convert to meters
      }
      // Don't break - continue to find the maximum extent
    }

    // Check negative grid direction - find the furthest point that's still inside
    for (let dist = 0.001; dist < 1; dist += 0.005) { // Start from 1m, 5m steps, up to 1km
      const testPoint = destination(lineCenter, dist, gridDirDeg + 180);
      if (booleanPointInPolygon(testPoint, polygon)) {
        maxNegativeLineDistance = dist * 1000; // Convert to meters
      }
      // Don't break - continue to find the maximum extent
    }

    // Generate grid points along this line with proper table spacing
    // Calculate minimum spacing between table centers to avoid overlap
    const panelSpacingValue = parseFloat(tableParams.panelSpacing) || 0.03;
    const tableWidth = tableParams.panelWidth; // Width of each table
    const minTableSpacing = tableWidth + panelSpacingValue + 0.5; // Add 0.5m buffer between tables

    const totalLineLength = maxPositiveLineDistance + maxNegativeLineDistance;

    // Only place one table per grid line to avoid overlapping
    // Place it at the center of the available line length
    if (totalLineLength >= tableWidth) {
      // Check if line center point is within polygon
      if (booleanPointInPolygon(lineCenter, polygon)) {
        gridPoints.push({
          gridLineIndex: gridLine.index,
          pointIndex: 0, // Only one point per line
          position: lineCenter.geometry.coordinates, // [lng, lat]
          gridDirection: gridDirRad,
          spacingDirection: spacingDirRad,
          availableLength: totalLineLength
        });

        console.log(`[generateGridPoints] Added grid point at line ${gridLine.index}, available length: ${totalLineLength.toFixed(1)}m`);
      }
    }
  }

  console.log(`[generateGridPoints] Generated ${gridPoints.length} grid points for polygon area ${polygonInfo.area.toFixed(2)} m²`);
  return gridPoints;
};

/**
 * Calculate optimal modulesX for a grid point based on available space
 * @param {Object} gridPoint - Grid point position and direction information
 * @param {Object} polygonInfo - Polygon area information
 * @param {Object} tableParams - PV table parameters
 * @returns {number} Optimal number of modules in X direction
 */
export const calculateOptimalModulesX = (gridPoint, polygonInfo, tableParams) => {
  const { panelWidth, panelSpacing } = tableParams;
  const { position, gridDirection } = gridPoint;
  const { coordinates } = polygonInfo;

  // Convert string spacing to number if needed
  let spacing = 0.03; // Default 3cm
  if (panelSpacing && panelSpacing !== 'auto') {
    const parsedSpacing = parseFloat(panelSpacing);
    if (!isNaN(parsedSpacing) && parsedSpacing > 0) {
      spacing = parsedSpacing;
    }
  }

  const moduleWithSpacing = panelWidth + spacing;
  const polygon = turfPolygonHelper([coordinates]);
  const gridPointCenter = turfPoint(position);

  // Find the available length in the grid direction from this point
  const gridDirectionDeg = gridDirection * 180 / Math.PI;
  const maxSampleDistance = 1; // Up to 1km

  console.log(`[calculateOptimalModulesX] Calculating modules for grid point at [${position[1].toFixed(6)}, ${position[0].toFixed(6)}]`);

  // Find maximum extent in positive grid direction - find the furthest point that's still inside
  let maxPositiveDistance = 0;
  for (let dist = 0.001; dist < maxSampleDistance; dist += 0.005) { // Start from 1m, 5m steps
    const testPoint = destination(gridPointCenter, dist, gridDirectionDeg);
    if (booleanPointInPolygon(testPoint, polygon)) {
      maxPositiveDistance = dist * 1000; // Convert to meters
    }
    // Don't break - continue to find the maximum extent
  }

  // Find maximum extent in negative grid direction - find the furthest point that's still inside
  let maxNegativeDistance = 0;
  for (let dist = 0.001; dist < maxSampleDistance; dist += 0.005) { // Start from 1m, 5m steps
    const testPoint = destination(gridPointCenter, dist, gridDirectionDeg + 180);
    if (booleanPointInPolygon(testPoint, polygon)) {
      maxNegativeDistance = dist * 1000; // Convert to meters
    }
    // Don't break - continue to find the maximum extent
  }

  // Calculate total available length
  const totalAvailableLength = maxPositiveDistance + maxNegativeDistance;

  // Calculate maximum modules that can fit
  const maxModules = Math.floor(totalAvailableLength / moduleWithSpacing);

  console.log(`[calculateOptimalModulesX] Available length: ${totalAvailableLength.toFixed(1)}m, Max modules: ${maxModules}`);

  return Math.max(1, maxModules); // At least 1 module
};

/**
 * Create PV table from grid point (main function for grid-based placement)
 * @param {Object} gridPoint - Grid point position and direction information
 * @param {Object} polygonInfo - Polygon area information
 * @param {Object} tableParams - PV table parameters
 * @param {number} areaIndex - Area index for unique ID
 * @param {number} pointIndex - Point index for unique ID
 * @returns {Object|null} PV table object or null if invalid
 */
export const createPVTableFromGridPoint = (gridPoint, polygonInfo, tableParams, areaIndex, pointIndex) => {
  const modulesX = calculateOptimalModulesX(gridPoint, polygonInfo, tableParams);

  if (modulesX < 1) {
    return null; // Not enough space for even one module
  }

  return createPVTableFromRowSegment(
    {
      center: gridPoint.position,
      direction: gridPoint.gridDirection,
      segment: {
        maxModules: modulesX,
        length: modulesX * (tableParams.panelWidth + (parseFloat(tableParams.panelSpacing) || 0.03))
      }
    },
    polygonInfo,
    tableParams,
    modulesX,
    areaIndex,
    pointIndex,
    0
  );
};

/**
 * Main Grid-based PV table placement algorithm
 * @param {Object} mainRoofLayer - Leaflet polygon layer for main roof
 * @param {Array} obstacles - Array of obstacle objects
 * @param {Array} shadowLayers - Array of shadow objects
 * @param {Object} tableParams - PV table parameters
 * @returns {Array} Array of PV table objects compatible with existing system
 */
export const placePVTablesGridBased = async (mainRoofLayer, obstacles, shadowLayers, tableParams) => {
  console.log('[placePVTablesGridBased] Starting grid-based placement with params:', tableParams);

  const { azimuthAngle } = tableParams;

  // Step 1: Calculate clean non-shaded areas
  const cleanAreas = await calculateNonShadedAreas(mainRoofLayer, obstacles, shadowLayers);
  if (cleanAreas.length === 0) {
    console.log('[placePVTablesGridBased] No clean areas found for placement');
    return [];
  }

  // Step 2: Sort areas by size and filter suitable ones
  const sortedAreas = sortPolygonsByArea(cleanAreas);
  const suitableAreas = sortedAreas.filter(area => isAreaSuitableForPlacement(area, tableParams));

  if (suitableAreas.length === 0) {
    console.log('[placePVTablesGridBased] No suitable areas found for placement');
    return [];
  }

  console.log(`[placePVTablesGridBased] Found ${suitableAreas.length} suitable areas for placement`);

  // Step 3: Calculate grid orientation
  const gridOrientation = calculateGridOrientation(azimuthAngle);
  console.log(`[placePVTablesGridBased] Grid orientation - Azimuth: ${azimuthAngle}°, Grid direction: ${gridOrientation.gridDirectionDeg.toFixed(1)}°`);

  // Step 4: Generate PV tables for each suitable area
  const allTables = [];

  for (let areaIndex = 0; areaIndex < suitableAreas.length; areaIndex++) {
    const area = suitableAreas[areaIndex];
    console.log(`[placePVTablesGridBased] Processing area ${areaIndex + 1}/${suitableAreas.length} (${area.area.toFixed(2)} m²)`);

    // Generate grid points for this area
    const gridPoints = generateGridPoints(area, tableParams, gridOrientation);

    for (let pointIndex = 0; pointIndex < gridPoints.length; pointIndex++) {
      const gridPoint = gridPoints[pointIndex];

      console.log(`[placePVTablesGridBased] Creating table for grid point ${pointIndex}: [${gridPoint.position[1].toFixed(6)}, ${gridPoint.position[0].toFixed(6)}]`);

      // Create PV table for this grid point
      const table = createPVTableFromGridPoint(
        gridPoint,
        area,
        tableParams,
        areaIndex,
        pointIndex
      );

      if (table) {
        allTables.push(table);
      }
    }
  }

  console.log(`[placePVTablesGridBased] Generated ${allTables.length} PV tables`);
  return allTables;
};

// Legacy function name for compatibility
export const placePVTablesSkeletonStyle = placePVTablesGridBased;

/**
 * Create a PV table object from grid point information (enhanced version)
 * @param {Object} rowInfo - Grid point position and direction information with segment data
 * @param {Object} areaInfo - Area information (unused but kept for compatibility)
 * @param {Object} tableParams - PV table parameters
 * @param {number} modulesX - Number of modules in X direction
 * @param {number} areaIndex - Area index for unique ID
 * @param {number} rowIndex - Row/point index for unique ID
 * @param {number} segmentIndex - Segment index for unique ID
 * @returns {Object} PV table object compatible with existing system
 */
export const createPVTableFromRowSegment = (rowInfo, areaInfo, tableParams, modulesX, areaIndex, rowIndex, segmentIndex = 0) => {
  const {
    panelLength,
    panelWidth,
    panelTilt,
    panelOrientation,
    azimuthAngle,
    panelSpacing,
    stackNumber
  } = tableParams;

  try {
    // Generate unique table ID with grid information
    const tableId = `grid_${areaIndex}_${rowIndex}_${segmentIndex}_${Date.now()}`;

    // Calculate table center position
    const [centerLng, centerLat] = rowInfo.center;

    // Convert string spacing to number if needed
    let spacing = 0.03; // Default 3cm
    if (panelSpacing && panelSpacing !== 'auto') {
      const parsedSpacing = parseFloat(panelSpacing);
      if (!isNaN(parsedSpacing) && parsedSpacing > 0) {
        spacing = parsedSpacing;
      }
    }

    // Calculate panel dimensions based on orientation
    const panelW = panelOrientation === 'portrait' ? panelWidth : panelLength;
    const panelH = panelOrientation === 'portrait' ? panelLength : panelWidth;

    // Calculate projected panel height based on tilt
    const tiltRadians = panelTilt * Math.PI / 180;
    const projectedPanelH = panelH * Math.cos(tiltRadians);

    // Calculate total table dimensions
    const totalWidth = modulesX * (panelW + spacing) - spacing;
    const totalHeight = stackNumber * (projectedPanelH + spacing) - spacing;

    // Generate panel rectangles
    const rectangles = [];

    for (let x = 0; x < modulesX; x++) {
      for (let y = 0; y < stackNumber; y++) {
        // Calculate panel position relative to table center
        const localXMeters = (x * (panelW + spacing)) - (totalWidth / 2) + (panelW / 2);
        const localYMeters = (y * (projectedPanelH + spacing)) - (totalHeight / 2) + (projectedPanelH / 2);

        // Convert meter offsets to degree offsets
        const panelOffsetX_deg = localXMeters / (111000 * Math.cos(centerLat * Math.PI / 180));
        const panelOffsetY_deg = localYMeters / 111000;

        // Calculate unrotated panel center
        const unrotatedPanelCenterLng = centerLng + panelOffsetX_deg;
        const unrotatedPanelCenterLat = centerLat + panelOffsetY_deg;

        // Calculate panel corners (unrotated)
        const halfPanelW_deg = (panelW / 2) / (111000 * Math.cos(centerLat * Math.PI / 180));
        const halfPanelH_deg = (projectedPanelH / 2) / 111000;

        const corners = [
          [unrotatedPanelCenterLat - halfPanelH_deg, unrotatedPanelCenterLng - halfPanelW_deg], // SW
          [unrotatedPanelCenterLat - halfPanelH_deg, unrotatedPanelCenterLng + halfPanelW_deg], // SE
          [unrotatedPanelCenterLat + halfPanelH_deg, unrotatedPanelCenterLng + halfPanelW_deg], // NE
          [unrotatedPanelCenterLat + halfPanelH_deg, unrotatedPanelCenterLng - halfPanelW_deg]  // NW
        ];

        // Apply rotation if azimuth is not 0
        // Use the same rotation logic as manual placement for consistency
        if (azimuthAngle !== 0) {
          // Convert corners to Turf.js format for rotation
          const cornerCoords = corners.map(([lat, lng]) => [lng, lat]); // Convert to [lng, lat]
          cornerCoords.push(cornerCoords[0]); // Close the polygon

          // Create Turf polygon
          const unrotatedPanelGeo = turfPolygonHelper([cornerCoords]);
          const tableCenter = [centerLng, centerLat]; // [lng, lat] for Turf.js

          // Use the same rotation angle calculation as manual placement
          const mapRotationAngle = (180 - azimuthAngle);

          // Rotate using Turf.js (same as manual placement)
          const rotatedPanelGeo = transformRotate(unrotatedPanelGeo, mapRotationAngle, { pivot: tableCenter });

          // Extract coordinates and convert back to [lat, lng] format
          const coordinates = rotatedPanelGeo.geometry.coordinates[0];
          const rectangleCorners = coordinates.slice(0, -1).map(([lng, lat]) => [lat, lng]);

          rectangles.push(rectangleCorners);
        } else {
          rectangles.push(corners);
        }
      }
    }

    // Create table object compatible with existing system
    const table = {
      id: tableId,
      rectangles,
      selected: false,
      position: { lat: centerLat, lng: centerLng },
      properties: {
        panelLength,
        panelWidth,
        panelTilt,
        panelOrientation,
        modulesX,
        modulesY: stackNumber, // Keep compatibility with existing system
        spacingX: spacing,
        spacingY: spacing,
        azimuth: azimuthAngle,
        modulePower: 600 // Default module power
      },
      createdAt: Date.now(),
      lastModified: Date.now(),
      source: 'grid-auto-placement',
      segmentInfo: rowInfo.segment ? {
        segmentIndex: segmentIndex,
        segmentLength: rowInfo.segment.length,
        totalSegments: 1 // Will be updated by caller if needed
      } : undefined
    };

    return table;

  } catch (error) {
    console.error('[createPVTableFromRowSegment] Error creating table:', error);
    return null;
  }
};

/**
 * Create a PV table object from grid point information (legacy function for compatibility)
 * @param {Object} rowInfo - Grid point position and direction information
 * @param {Object} areaInfo - Area information
 * @param {Object} tableParams - PV table parameters
 * @param {number} modulesX - Number of modules in X direction
 * @param {number} areaIndex - Area index for unique ID
 * @param {number} rowIndex - Point index for unique ID
 * @returns {Object} PV table object compatible with existing system
 */
export const createPVTableFromRow = (rowInfo, areaInfo, tableParams, modulesX, areaIndex, rowIndex) => {
  return createPVTableFromRowSegment(rowInfo, areaInfo, tableParams, modulesX, areaIndex, rowIndex, 0);
};

/**
 * Rotate a point around a center point
 * @param {Array} point - [lat, lng] point to rotate
 * @param {Array} center - [lat, lng] center of rotation
 * @param {number} angle - Rotation angle in radians
 * @returns {Array} Rotated [lat, lng] point
 */
const rotatePoint = (point, center, angle) => {
  const [lat, lng] = point;
  const [centerLat, centerLng] = center;

  // Convert to relative coordinates
  const relLat = lat - centerLat;
  const relLng = lng - centerLng;

  // Apply rotation
  const cos = Math.cos(angle);
  const sin = Math.sin(angle);

  const rotatedLat = relLat * cos - relLng * sin;
  const rotatedLng = relLat * sin + relLng * cos;

  // Convert back to absolute coordinates
  return [centerLat + rotatedLat, centerLng + rotatedLng];
};
