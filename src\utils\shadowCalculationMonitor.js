/**
 * Shadow Calculation Performance Monitor
 * 
 * Tracks performance metrics and compares Clipper2 vs Martinez performance
 * Provides detailed logging and analytics for shadow calculation operations
 */

export class ShadowCalculationMonitor {
  constructor() {
    this.metrics = [];
    this.isEnabled = process.env.NODE_ENV === 'development'; // Only enable in development
  }

  /**
   * Start monitoring a calculation
   * @param {string} method - The calculation method (e.g., 'clipper2', 'martinez')
   * @param {number} obstacleCount - Number of obstacles being processed
   * @param {string} operation - Type of operation (e.g., 'intersection', 'union', 'merge')
   * @returns {Object} Monitoring session object
   */
  startCalculation(method, obstacleCount, operation = 'unknown') {
    if (!this.isEnabled) return { end: () => {} };

    const session = {
      method,
      obstacleCount,
      operation,
      startTime: performance.now(),
      startMemory: this.getMemoryUsage(),
      id: `${method}_${operation}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };

    console.log(`[ShadowMonitor] Starting ${method} ${operation} with ${obstacleCount} obstacles`);
    
    return {
      ...session,
      end: (success = true, resultCount = 0, errorMessage = null) => {
        this.endCalculation(session, success, resultCount, errorMessage);
      }
    };
  }

  /**
   * End monitoring a calculation
   * @param {Object} session - The monitoring session
   * @param {boolean} success - Whether the calculation succeeded
   * @param {number} resultCount - Number of results produced
   * @param {string} errorMessage - Error message if failed
   */
  endCalculation(session, success, resultCount, errorMessage) {
    if (!this.isEnabled) return;

    const endTime = performance.now();
    const endMemory = this.getMemoryUsage();
    const duration = endTime - session.startTime;
    const memoryDelta = endMemory - session.startMemory;

    const metric = {
      ...session,
      endTime,
      duration,
      memoryDelta,
      success,
      resultCount,
      errorMessage,
      timestamp: new Date().toISOString()
    };

    this.metrics.push(metric);
    
    // Log the result
    const status = success ? '✅' : '❌';
    const memoryStr = memoryDelta > 0 ? `+${(memoryDelta / 1024 / 1024).toFixed(2)}MB` : `${(memoryDelta / 1024 / 1024).toFixed(2)}MB`;
    
    console.log(`[ShadowMonitor] ${status} ${session.method} ${session.operation}: ${duration.toFixed(2)}ms, ${memoryStr}, ${resultCount} results`);
    
    if (!success && errorMessage) {
      console.error(`[ShadowMonitor] Error: ${errorMessage}`);
    }

    // Keep only last 100 metrics to prevent memory leaks
    if (this.metrics.length > 100) {
      this.metrics = this.metrics.slice(-100);
    }
  }

  /**
   * Get current memory usage (if available)
   * @returns {number} Memory usage in bytes
   */
  getMemoryUsage() {
    if (performance.memory) {
      return performance.memory.usedJSHeapSize;
    }
    return 0;
  }

  /**
   * Get performance statistics
   * @returns {Object} Performance statistics
   */
  getStatistics() {
    if (!this.isEnabled || this.metrics.length === 0) {
      return { enabled: this.isEnabled, totalMetrics: 0 };
    }

    const clipper2Metrics = this.metrics.filter(m => m.method === 'clipper2');
    const martinezMetrics = this.metrics.filter(m => m.method === 'martinez');
    
    const calculateStats = (metrics) => {
      if (metrics.length === 0) return null;
      
      const durations = metrics.map(m => m.duration);
      const successRate = metrics.filter(m => m.success).length / metrics.length;
      
      return {
        count: metrics.length,
        averageDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
        minDuration: Math.min(...durations),
        maxDuration: Math.max(...durations),
        successRate: successRate * 100,
        totalMemoryDelta: metrics.reduce((sum, m) => sum + m.memoryDelta, 0)
      };
    };

    return {
      enabled: this.isEnabled,
      totalMetrics: this.metrics.length,
      clipper2: calculateStats(clipper2Metrics),
      martinez: calculateStats(martinezMetrics),
      recentMetrics: this.metrics.slice(-10).map(m => ({
        method: m.method,
        operation: m.operation,
        duration: m.duration,
        success: m.success,
        obstacleCount: m.obstacleCount,
        resultCount: m.resultCount
      }))
    };
  }

  /**
   * Compare Clipper2 vs Martinez performance
   * @returns {Object} Comparison results
   */
  comparePerformance() {
    const stats = this.getStatistics();
    
    if (!stats.clipper2 || !stats.martinez) {
      return {
        available: false,
        message: 'Insufficient data for comparison'
      };
    }

    const speedImprovement = ((stats.martinez.averageDuration - stats.clipper2.averageDuration) / stats.martinez.averageDuration) * 100;
    const reliabilityImprovement = stats.clipper2.successRate - stats.martinez.successRate;

    return {
      available: true,
      speedImprovement: speedImprovement.toFixed(2),
      reliabilityImprovement: reliabilityImprovement.toFixed(2),
      clipper2Faster: speedImprovement > 0,
      clipper2MoreReliable: reliabilityImprovement > 0,
      summary: `Clipper2 is ${Math.abs(speedImprovement).toFixed(1)}% ${speedImprovement > 0 ? 'faster' : 'slower'} and ${Math.abs(reliabilityImprovement).toFixed(1)}% ${reliabilityImprovement > 0 ? 'more' : 'less'} reliable than Martinez`
    };
  }

  /**
   * Log performance summary to console
   */
  logSummary() {
    if (!this.isEnabled) {
      console.log('[ShadowMonitor] Performance monitoring is disabled');
      return;
    }

    const stats = this.getStatistics();
    const comparison = this.comparePerformance();

    console.group('[ShadowMonitor] Performance Summary');
    console.log('Total calculations:', stats.totalMetrics);
    
    if (stats.clipper2) {
      console.log('Clipper2:', {
        count: stats.clipper2.count,
        avgDuration: `${stats.clipper2.averageDuration.toFixed(2)}ms`,
        successRate: `${stats.clipper2.successRate.toFixed(1)}%`
      });
    }
    
    if (stats.martinez) {
      console.log('Martinez:', {
        count: stats.martinez.count,
        avgDuration: `${stats.martinez.averageDuration.toFixed(2)}ms`,
        successRate: `${stats.martinez.successRate.toFixed(1)}%`
      });
    }
    
    if (comparison.available) {
      console.log('Comparison:', comparison.summary);
    }
    
    console.groupEnd();
  }

  /**
   * Clear all metrics
   */
  clear() {
    this.metrics = [];
    console.log('[ShadowMonitor] Metrics cleared');
  }

  /**
   * Export metrics as JSON
   * @returns {string} JSON string of all metrics
   */
  exportMetrics() {
    return JSON.stringify({
      timestamp: new Date().toISOString(),
      metrics: this.metrics,
      statistics: this.getStatistics(),
      comparison: this.comparePerformance()
    }, null, 2);
  }
}

// Create a global instance
export const shadowMonitor = new ShadowCalculationMonitor();

// Export convenience functions
export const startShadowCalculation = (method, obstacleCount, operation) => 
  shadowMonitor.startCalculation(method, obstacleCount, operation);

export const getShadowStatistics = () => shadowMonitor.getStatistics();
export const compareShadowPerformance = () => shadowMonitor.comparePerformance();
export const logShadowSummary = () => shadowMonitor.logSummary();
