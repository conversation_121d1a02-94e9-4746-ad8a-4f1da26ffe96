import React, { useEffect, useRef, useState, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { <PERSON><PERSON><PERSON><PERSON>, TileLayer, Marker, useMapEvents, FeatureGroup, Polygon as RLPolygon, Rectangle, ImageOverlay, useMap } from 'react-leaflet';
import L from 'leaflet';
import * as martinez from 'martinez-polygon-clipping';
import { smartMergeShadowPolygons } from '../utils/shadowMerging';
import { enhancedIntersection, monitoredIntersection } from '../utils/clipper2Replacement';
import { testClipper2Operations, testClipper2Basic } from '../utils/clipper2Operations';
import { isClipper2Initialized, safeInitializeClipper2 } from '../utils/wasmLoader';
import { logShadowSummary, getShadowStatistics } from '../utils/shadowCalculationMonitor';
import 'leaflet/dist/leaflet.css';
import { EditControl } from 'react-leaflet-draw';
import 'leaflet-draw/dist/leaflet.draw.css';
import { createPortal } from 'react-dom';
import { FaPen, FaSearch, FaLocationArrow, FaExpand, FaCompress, FaSatelliteDish, FaMap, FaSync, FaToggleOn, FaToggleOff } from 'react-icons/fa';
import 'leaflet-fullscreen/dist/leaflet.fullscreen.css';
// Web Worker integration for shadow calculations
import { useShadowWorker } from '../hooks/useShadowWorker';
import { createShadowCalculationParams, formatShadowResults } from '../utils/shadowCalculations';
// Optimized PV table rendering components
import PVTableRenderer from './map/pv-tables/PVTableRenderer';
// PV table performance optimizations
import '../styles/pv-table-optimizations.css';
// Centralized state management
import { useMapState } from '../hooks/useMapState';
// Grid-based PV table placement algorithm
import { placePVTablesSkeletonStyle } from '../utils/TablesAutoPlacement';
// Import domtoimage for better image capture
// import html2canvas from 'html2canvas'; // Removed, snapshot functionality disabled

// Fix Leaflet marker icon issue
import markerIcon from 'leaflet/dist/images/marker-icon.png';
import markerIcon2x from 'leaflet/dist/images/marker-icon-2x.png';
import markerShadow from 'leaflet/dist/images/marker-shadow.png';
import SunCalc from 'suncalc';
import { point as turfPoint, polygon as turfPolygonHelper, multiPolygon as turfMultiPolygonHelper, featureCollection as turfFeatureCollection } from '@turf/helpers';
import turfConvex from '@turf/convex'; // Corrected import for convex
import transformRotate from '@turf/transform-rotate';
import bbox from '@turf/bbox';
import centroid from '@turf/centroid';
import distance from '@turf/distance';
import destination from '@turf/destination';
import booleanPointInPolygon from '@turf/boolean-point-in-polygon';

// Create a custom icon to use throughout the application
const defaultIcon = new L.Icon({
  iconUrl: markerIcon,
  iconRetinaUrl: markerIcon2x,
  shadowUrl: markerShadow,
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  tooltipAnchor: [16, -28],
  shadowSize: [41, 41]
});

// Set as default icon
L.Marker.prototype.options.icon = defaultIcon;

// Helper function to calculate geodesic area of a polygon layer, accounting for holes
const calculateArea = (layer) => {
  if (!layer || !layer.getLatLngs) {
    console.error('Invalid layer passed to calculateArea:', layer);
    return 0;
  }
  const latLngsArrays = layer.getLatLngs();
  // Ensure latLngsArrays is an array and not empty. For a simple polygon, it's [LatLng[]].
  // For a polygon with holes, it's [LatLng[], LatLng[], ...].
  if (!Array.isArray(latLngsArrays) || latLngsArrays.length === 0) {
    console.warn('Cannot calculate area: layer has no LatLngs or in unexpected format.', latLngsArrays);
    return 0;
  }

  // L.GeometryUtil.geodesicArea expects a flat array of L.LatLng objects for a single ring.
  // The first element of latLngsArrays is the outer ring.
  const outerRing = latLngsArrays[0];
  if (!Array.isArray(outerRing) || outerRing.length === 0) {
    console.warn('Cannot calculate area: outer ring has no LatLngs.');
    return 0;
  }
  let area = L.GeometryUtil.geodesicArea(outerRing);

  // Subtract area of holes if any (latLngsArrays[1], latLngsArrays[2], ...)
  if (latLngsArrays.length > 1) {
    for (let i = 1; i < latLngsArrays.length; i++) {
      const hole = latLngsArrays[i];
      if (Array.isArray(hole) && hole.length > 0) {
        area -= L.GeometryUtil.geodesicArea(hole);
      } else {
        console.warn('Skipping hole in area calculation due to invalid format:', hole);
      }
    }
  }
  return area;
};

const calculateAreaFromCoords = (coords) => {
  if (!coords || coords.length < 3) {
    return 0;
  }
  // Convert [lat, lng] pairs to L.LatLng objects
  const latLngs = coords.map(c => L.latLng(c[0], c[1]));
  try {
    return L.GeometryUtil.geodesicArea(latLngs);
  } catch (e) {
    console.error("Error calculating area from coordinates:", e, coords);
    return 0;
  }
};

// Helper function to detect fullscreen change
function onFullscreenChange() {
  return document.fullscreenElement || 
         document.mozFullScreenElement || 
         document.webkitFullscreenElement || 
         document.msFullscreenElement;
}

const MODES = {
  GPS: 'gps',
  CLEAR: 'clear',
  MEASURE: 'measure',
};

// Google Satellite imagery provider - memoized constant
const SATELLITE_PROVIDER = Object.freeze({
  name: 'Google Satellite',
  url: 'https://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={z}',
  attribution: ' Google',
  maxZoom: 22
});

// Standard OSM provider - memoized constant
const OSM_PROVIDER = Object.freeze({
  url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
  attribution: '&copy; OpenStreetMap contributors',
  maxZoom: 19
});

function LocationEvents({ setLocation, setSelectedCoord, mode, MODES }) {
  useMapEvents({
    click(e) {
      if (mode === MODES.CLEAR) {
        const newLocation = { lat: e.latlng.lat, lng: e.latlng.lng };
        setLocation(newLocation);
        setSelectedCoord(newLocation);
      }
    },
  });
  return null;
}

// Wrap with memo for optimization
const MemoizedLocationEvents = React.memo(LocationEvents);

// Component to handle map pane creation using useMap hook
function MapPaneCreator({ setMapPanesReady, mapRef }) {
  const map = useMap();

  useEffect(() => {
    if (map) {
      mapRef.current = map;

      // Create custom panes with error handling
      // Z-index layering strategy:
      // - tilePane: 200 (base map tiles)
      // - shadowPane: 350 (shadows cast by obstacles/panels - should be below everything else)
      // - overlayPane: 400 (default - roof areas, obstacles, drawn shapes)
      // - obstaclePane: 450 (PV panels, both auto-placed and manually drawn - should be above shadows)
      // - markerPane: 600 (location markers)
      try {
        console.log('[MapPaneCreator] Starting pane creation...');

        if (!map.getPane('shadowPane')) {
          map.createPane('shadowPane');
          const shadowPane = map.getPane('shadowPane');
          if (shadowPane) {
            shadowPane.style.zIndex = 350; // Below overlayPane (400) but above tilePane (200)
            shadowPane.style.position = 'relative'; // Ensure proper stacking context
            console.log('[Map Panes] Created shadowPane with z-index:', shadowPane.style.zIndex);
          }
        }

        if (!map.getPane('obstaclePane')) {
          map.createPane('obstaclePane');
          const obstaclePane = map.getPane('obstaclePane');
          if (obstaclePane) {
            obstaclePane.style.zIndex = 450; // Above overlayPane (400) and shadowPane (350)
            obstaclePane.style.position = 'relative'; // Ensure proper stacking context
            console.log('[Map Panes] Created obstaclePane with z-index:', obstaclePane.style.zIndex);
          }
        }

        // Verify both panes exist before setting ready state
        const shadowPane = map.getPane('shadowPane');
        const obstaclePane = map.getPane('obstaclePane');

        if (shadowPane && obstaclePane) {
          console.log('[MapPaneCreator] Both panes created successfully');
          setMapPanesReady(true);
        } else {
          console.error('[MapPaneCreator] Failed to create panes:', { shadowPane: !!shadowPane, obstaclePane: !!obstaclePane });
          setMapPanesReady(false);
        }

      } catch (error) {
        console.error('[MapPaneCreator] Error creating custom map panes:', error);
        setMapPanesReady(false);
      }
    }
  }, [map, setMapPanesReady, mapRef]);

  return null; // This component doesn't render anything
}

// Utility functions
function formatArea(area) {
  return area > 1000000
    ? (area / 1000000).toFixed(2) + ' km²'
    : area.toFixed(2) + ' m²';
}

function formatLength(length) {
  return length > 1000
    ? (length / 1000).toFixed(2) + ' km'
    : length.toFixed(2) + ' m';
}

export default function OpenLayersMap({
  location,
  setLocation,
  panelLength,
  setPanelLength,
  panelWidth,
  setPanelWidth,
  panelTilt,
  setPanelTilt,
  panelOrientation,
  setPanelOrientation,
  panelSpacing,
  setPanelSpacing,
  stackNumber,
  setStackNumber,
  autoPlacementRequest, // Add autoPlacementRequest to destructured props
  onBuildingDataChange, // Callback to pass building data to parent
  onSelectedTableChange, // Callback to pass selected table data to parent
  onTableUpdate, // Callback to handle table property updates
  onManualPlacingActions // Callback to pass manual placing button functions to parent
}) {
  // Initialize i18n translation hook
  const { t } = useTranslation();

  // Initialize centralized state management
  const mapState = useMapState({
    location,
    mode: MODES.CLEAR,
    analysisDate: new Date().toISOString().split('T')[0],
    analysisDurationHours: 6
  });

  // Create memoized icon for this component instance
  const customIcon = useMemo(() => defaultIcon, []);

  // Local UI state (not managed by centralized state)
  const [searchValue, setSearchValue] = useState('');
  const [searchError, setSearchError] = useState('');
  const [measurement, setMeasurement] = useState('');
  const mapRef = useRef(null);
  const drawnItemsRef = useRef(null);
  const [isSatellite, setIsSatellite] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Gradually migrate state to centralized management
  // For now, keep existing state variables and sync with mapState
  const [mode, setMode] = useState(MODES.CLEAR);
  const [selectedCoord, setSelectedCoord] = useState(location);

  // Performance monitoring for state management
  const performanceMetrics = useMemo(() => {
    if (process.env.NODE_ENV === 'development') {
      const metrics = mapState.getPerformanceMetrics();
      console.log('[OpenLayersMap] State Management Performance Metrics:', metrics);
      return metrics;
    }
    return null;
  }, [mapState]);

  // Sync location changes with centralized state
  useEffect(() => {
    if (location.lat !== mapState.location.lat || location.lng !== mapState.location.lng) {
      mapState.updateLocation(location);
    }
  }, [location, mapState]);

  // Sync selected coordinate with centralized state
  useEffect(() => {
    mapState.setSelectedCoord(selectedCoord);
  }, [selectedCoord, mapState]);

  // Setup debugging functions for Clipper2 testing
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      window.debugClipper2 = {
        testBasic: testClipper2Basic,
        testOperations: testClipper2Operations,
        isInitialized: isClipper2Initialized,
        initialize: safeInitializeClipper2,
        logShadowSummary: logShadowSummary,
        getShadowStatistics: getShadowStatistics,
        testShadowMerging: async (testShadows) => {
          const testData = testShadows || [
            { id: 'test1', coordinates: [[0, 0], [1, 0], [1, 1], [0, 1]], type: 'test' },
            { id: 'test2', coordinates: [[0.5, 0.5], [1.5, 0.5], [1.5, 1.5], [0.5, 1.5]], type: 'test' }
          ];
          return await smartMergeShadowPolygons(testData);
        },
        testCoordinateIntersection: async () => {
          // Test with the actual coordinates from the logs
          const shadowCoords = [[[31.232705778279666, 30.04501839053968], [31.232697734997743, 30.045088044720817], [31.232778167817184, 30.045092688331163], [31.232705778279666, 30.04501839053968]]];
          const roofCoords = [[[31.233352383930615, 30.045288596954933], [31.232518563702115, 30.045265378947004], [31.232513201514177, 30.044796374021573], [31.233352383930615, 30.045288596954933]]];

          console.log('Testing intersection with actual coordinates:');
          console.log('Shadow:', shadowCoords);
          console.log('Roof:', roofCoords);

          const result = await enhancedIntersection(shadowCoords, roofCoords, 'coordinate test');
          console.log('Intersection result:', result);
          return result;
        }
      };
      console.log('[OpenLayersMap] Debugging functions available at window.debugClipper2');
    }
  }, []);

  // Snapshot state (mapSnapshot, snapshotBounds, isSnapshotMode) removed.

  // PV Panel/Table State - now received as props
  const [isDrawingArea, setIsDrawingArea] = useState(false);
  const [mainRoofLayer, setMainRoofLayer] = useState(null); // Leaflet polygon layer for the main roof
  const [pendingObstacle, setPendingObstacle] = useState(null); // Stores { layer, area } for a new obstacle awaiting height input
  const [mainRoofArea, setMainRoofArea] = useState(0); // Gross area of the main roof
  const [obstacles, setObstacles] = useState([]); // Array of { id: string, layer: L.Polygon, height: number | null, area: number }
  const [showHeightModalForObstacleId, setShowHeightModalForObstacleId] = useState(null); // ID of obstacle needing height
  const [currentObstacleHeightInput, setCurrentObstacleHeightInput] = useState('');

  // States for editing existing obstacle height
  const [editingObstacleId, setEditingObstacleId] = useState(null); // ID of the obstacle being edited
  const [currentEditObstacleHeightInput, setCurrentEditObstacleHeightInput] = useState('');

  // States for fence height popup when drawing building polygons
  const [showFenceHeightModal, setShowFenceHeightModal] = useState(false);
  const [currentFenceHeightInput, setCurrentFenceHeightInput] = useState('');
  const [pendingBuildingPolygon, setPendingBuildingPolygon] = useState(null); // Stores drawing state for building polygon

  // State to track polygon drawing order for obstacle classification
  const [polygonDrawingOrder, setPolygonDrawingOrder] = useState(0); // Counter to track drawing order

  // State for shadow analysis
  const [shadowLayers, setShadowLayers] = useState([]); // Array of {id, coordinates} for shadows
  const [analysisDate, setAnalysisDate] = useState(() => {
    const currentYear = new Date().getFullYear();
    return `${currentYear}-12-23`; // Default to December 23rd of the current year
  });
  const [totalShadedAreaOnRoof, setTotalShadedAreaOnRoof] = useState(0);
  const [analysisDurationHours, setAnalysisDurationHours] = useState(6); // Default to 6 hours duration
  const [isCalculatingShadows, setIsCalculatingShadows] = useState(false);
  const [showShadows, setShowShadows] = useState(true); // State to toggle shadow visibility
  const [autoPlacedPvFeatures, setAutoPlacedPvFeatures] = useState(null); // GeoJSON FeatureCollection
  const [isPlacingPanels, setIsPlacingPanels] = useState(false);
  const [mapPanesReady, setMapPanesReady] = useState(false); // Track when custom panes are ready
  const lastProcessedTimestampRef = useRef(null); // Ref to track the last processed autoPlacementRequest timestamp

  // Function to toggle shadow visibility
  const toggleShadowVisibility = () => {
    setShowShadows(!showShadows);
  };

  // Function to merge multiple shadow polygons using Clipper2 WASM, preserving both connected and isolated shadows
  const mergeShadowPolygons = async (shadowArray) => {
    if (!shadowArray || shadowArray.length === 0) return [];
    if (shadowArray.length === 1) return shadowArray;

    try {
      console.log(`[mergeShadowPolygons] Processing ${shadowArray.length} shadow polygons with Clipper2 smart merging...`);

      // Use Clipper2-based smart merging
      const mergedShadows = await smartMergeShadowPolygons(shadowArray);
      return mergedShadows;

    } catch (error) {
      console.error('[mergeShadowPolygons] Error merging shadows:', error);
      console.log('[mergeShadowPolygons] Falling back to individual shadows');
      return shadowArray; // Return original shadows as fallback
    }
  };

  // Track theme changes to ensure drawing controls are properly styled
  const [isLightTheme, setIsLightTheme] = useState(false);

  useEffect(() => {
    // Check initial theme state
    const checkTheme = () => {
      const hasLightTheme = document.documentElement.classList.contains('light-theme');
      setIsLightTheme(hasLightTheme);
    };

    // Check theme on mount
    checkTheme();

    // Create observer to watch for theme changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
          checkTheme();
        }
      });
    });

    // Observe changes to the document element's class attribute
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });

    return () => observer.disconnect();
  }, []);



  const handleModalSubmit = () => {
    console.log('[handleModalSubmit] Attempting to add obstacle. Pending obstacle data:', pendingObstacle, 'Modal ID for submit:', showHeightModalForObstacleId, 'Input height:', currentObstacleHeightInput);
    const obstacleHeight = parseFloat(currentObstacleHeightInput);

    if (isNaN(obstacleHeight) || obstacleHeight < 0) { // Allow 0, reject negative
      alert('Please enter a valid non-negative number for the obstacle height.');
      return;
    }

    if (pendingObstacle && showHeightModalForObstacleId === pendingObstacle.layer._leaflet_id) {
      console.log('[handleModalSubmit] Pending obstacle area to be added:', pendingObstacle.area);
      setObstacles(prevObstacles => {
        const newObstacleEntry = {
          id: pendingObstacle.layer._leaflet_id,
          layer: pendingObstacle.layer,
          height: obstacleHeight,
          area: pendingObstacle.area,
        };
        console.log('[handleModalSubmit] Adding new obstacle to state:', newObstacleEntry);
        const updatedObstacles = [...prevObstacles, newObstacleEntry];
        console.log('[handleModalSubmit] Updated obstacles array (after adding):', updatedObstacles);
        // Shadow calculation will be handled automatically by useEffect
        return updatedObstacles;
      });
      setPendingObstacle(null); // Clear pending obstacle after adding
    } else {
      console.error('[handleModalSubmit] Mismatch in pending obstacle data or ID during modal submit. Or pendingObstacle is null.');
      alert('There was an error submitting the obstacle height. Please try redrawing the obstacle.');
    }

    setShowHeightModalForObstacleId(null); // Close modal
    setCurrentObstacleHeightInput(''); // Reset input field
    // calculateAndDisplayShadows(); // Will be called with updated obstacles
  };

  const handleObstacleClick = (obstacle) => {
    console.log('[handleObstacleClick] Clicked obstacle:', obstacle);
    setEditingObstacleId(obstacle.id);
    setCurrentEditObstacleHeightInput(obstacle.height ? String(obstacle.height) : '');
  };

  const handleUpdateObstacleHeight = () => {
    console.log('[handleUpdateObstacleHeight] Attempting to update obstacle. Editing ID:', editingObstacleId, 'New height input:', currentEditObstacleHeightInput);
    if (!editingObstacleId) {
      console.error('[handleUpdateObstacleHeight] No obstacle ID is being edited.');
      return;
    }

    const newHeight = parseFloat(currentEditObstacleHeightInput);
    if (isNaN(newHeight) || newHeight < 0) { // Allow 0, reject negative
      alert('Please enter a valid non-negative number for the obstacle height.');
      return;
    }

    setObstacles(prevObstacles => {
      const updatedList = prevObstacles.map(obs => {
        if (obs.id === editingObstacleId) {
          console.log('[handleUpdateObstacleHeight] Updating obstacle:', obs, 'to new height:', newHeight);
          return { ...obs, height: newHeight };
        }
        return obs;
      });
      // Shadow calculation will be handled automatically by useEffect
      return updatedList; // Return for state update
    });

    setEditingObstacleId(null); // Close the modal
    setCurrentEditObstacleHeightInput(''); // Reset input
  };

  // REMOVED: Old Clipper2-based shadow system - will be replaced with new simple geometric approach

  // NEW SHADOW SYSTEM: Simple geometric shadow calculation

  // Helper function to calculate shadow point for a vertex
  const calculateShadowPoint = (vertex, shadowLength, sunAzimuth) => {
    // Use EPSG3857 projection for accurate meter-based calculations
    const projectedStartPoint = L.CRS.EPSG3857.project(L.latLng(vertex.lat, vertex.lng));

    // Calculate shadow direction (opposite to sun direction)
    // sunAzimuth is direction TO sun, shadow goes in opposite direction
    const shadowEndX = projectedStartPoint.x - shadowLength * Math.sin(sunAzimuth);
    const shadowEndY = projectedStartPoint.y + shadowLength * Math.cos(sunAzimuth);
    const shadowProjectedPoint = L.point(shadowEndX, shadowEndY);
    const shadowVertexLatLng = L.CRS.EPSG3857.unproject(shadowProjectedPoint);

    return { lng: shadowVertexLatLng.lng, lat: shadowVertexLatLng.lat };
  };

  // Helper function to create directional shadow for an obstacle
  const createDirectionalShadow = (obstacleVertices, shadowLength, sunAzimuth) => {
    // For each edge of the obstacle, create a shadow quad
    const shadowPolygons = [];

    for (let i = 0; i < obstacleVertices.length; i++) {
      const currentVertex = obstacleVertices[i];
      const nextVertex = obstacleVertices[(i + 1) % obstacleVertices.length];

      // Calculate shadow points for both vertices of this edge
      const currentShadowPoint = calculateShadowPoint(currentVertex, shadowLength, sunAzimuth);
      const nextShadowPoint = calculateShadowPoint(nextVertex, shadowLength, sunAzimuth);

      // Create shadow quad for this edge: vertex1 -> vertex2 -> shadow2 -> shadow1
      const edgeShadowCoords = [
        [currentVertex.lat, currentVertex.lng],
        [nextVertex.lat, nextVertex.lng],
        [nextShadowPoint.lat, nextShadowPoint.lng],
        [currentShadowPoint.lat, currentShadowPoint.lng],
        [currentVertex.lat, currentVertex.lng] // Close the polygon
      ];

      shadowPolygons.push(edgeShadowCoords);
    }

    return shadowPolygons;
  };

  // Helper function to clip shadow polygons to main building area
  const clipShadowsToMainArea = (shadowPolygons, mainRoofLayer) => {
    if (!mainRoofLayer || !shadowPolygons || shadowPolygons.length === 0) {
      return shadowPolygons;
    }

    try {
      // Get main building area coordinates
      const mainRoofLatLngs = mainRoofLayer.getLatLngs();
      let mainAreaCoords = [];

      if (mainRoofLatLngs && mainRoofLatLngs.length > 0) {
        if (mainRoofLatLngs[0] instanceof L.LatLng) {
          mainAreaCoords = mainRoofLatLngs.map(latlng => [latlng.lng, latlng.lat]);
        } else if (Array.isArray(mainRoofLatLngs[0]) && mainRoofLatLngs[0][0] instanceof L.LatLng) {
          mainAreaCoords = mainRoofLatLngs[0].map(latlng => [latlng.lng, latlng.lat]);
        }
      }

      if (mainAreaCoords.length < 3) {
        console.warn('[clipShadowsToMainArea] Invalid main area coordinates');
        return shadowPolygons;
      }

      // Ensure main area is closed
      if (mainAreaCoords[0][0] !== mainAreaCoords[mainAreaCoords.length - 1][0] ||
          mainAreaCoords[0][1] !== mainAreaCoords[mainAreaCoords.length - 1][1]) {
        mainAreaCoords.push([mainAreaCoords[0][0], mainAreaCoords[0][1]]);
      }

      const clippedShadows = [];

      shadowPolygons.forEach((shadowCoords, index) => {
        try {
          // Convert shadow coordinates to GeoJSON format [lng, lat]
          const shadowGeoJSON = shadowCoords.map(coord => [coord[1], coord[0]]); // [lat, lng] -> [lng, lat]

          // Ensure shadow polygon is closed
          if (shadowGeoJSON[0][0] !== shadowGeoJSON[shadowGeoJSON.length - 1][0] ||
              shadowGeoJSON[0][1] !== shadowGeoJSON[shadowGeoJSON.length - 1][1]) {
            shadowGeoJSON.push([shadowGeoJSON[0][0], shadowGeoJSON[0][1]]);
          }

          // Use Martinez clipping library to intersect shadow with main area
          const intersection = martinez.intersection([[shadowGeoJSON]], [[mainAreaCoords]]);

          if (intersection && intersection.length > 0) {
            // Convert back to [lat, lng] format for Leaflet
            intersection.forEach(polygon => {
              if (polygon && polygon[0] && polygon[0].length > 0) {
                const clippedCoords = polygon[0].map(coord => [coord[1], coord[0]]); // [lng, lat] -> [lat, lng]
                clippedShadows.push(clippedCoords);
              }
            });
          }
        } catch (clipError) {
          console.warn(`[clipShadowsToMainArea] Error clipping shadow ${index}:`, clipError);
          // On error, skip this shadow polygon
        }
      });

      console.log(`[clipShadowsToMainArea] Clipped ${shadowPolygons.length} shadows to ${clippedShadows.length} within main area`);
      return clippedShadows;

    } catch (error) {
      console.error('[clipShadowsToMainArea] Error during shadow clipping:', error);
      return shadowPolygons; // Return original shadows on error
    }
  };

  // REMOVED: Old simplified shadow fallback - will be replaced with new system

  // Helper function to create a single perimeter fence obstacle around building polygon
  const createPerimeterFence = (polygonLatLngs, fenceHeight, polygonId) => {
    const fenceWidth = 0.3; // 30cm width as specified
    console.log(`[createPerimeterFence] Creating perimeter fence with ${fenceWidth}m width and ${fenceHeight}m height`);

    // Convert fence width to degrees (approximate)
    // Use more accurate conversion based on latitude
    const avgLat = polygonLatLngs.reduce((sum, point) => sum + point.lat, 0) / polygonLatLngs.length;
    const latToMeters = 111000; // meters per degree latitude
    const lngToMeters = Math.cos(avgLat * Math.PI / 180) * 111000; // meters per degree longitude at this latitude

    const fenceWidthLatDeg = fenceWidth / latToMeters;
    const fenceWidthLngDeg = fenceWidth / lngToMeters;

    // Create outer perimeter by offsetting each vertex outward
    const outerPerimeter = [];
    const innerPerimeter = [...polygonLatLngs]; // Inner perimeter is the original building outline

    for (let i = 0; i < polygonLatLngs.length; i++) {
      const currentPoint = polygonLatLngs[i];
      const prevPoint = polygonLatLngs[(i - 1 + polygonLatLngs.length) % polygonLatLngs.length];
      const nextPoint = polygonLatLngs[(i + 1) % polygonLatLngs.length];

      // Calculate vectors from current point to previous and next points
      const prevVector = {
        lat: prevPoint.lat - currentPoint.lat,
        lng: prevPoint.lng - currentPoint.lng
      };
      const nextVector = {
        lat: nextPoint.lat - currentPoint.lat,
        lng: nextPoint.lng - currentPoint.lng
      };

      // Normalize vectors
      const prevLength = Math.sqrt(prevVector.lat * prevVector.lat + prevVector.lng * prevVector.lng);
      const nextLength = Math.sqrt(nextVector.lat * nextVector.lat + nextVector.lng * nextVector.lng);

      if (prevLength > 0) {
        prevVector.lat /= prevLength;
        prevVector.lng /= prevLength;
      }
      if (nextLength > 0) {
        nextVector.lat /= nextLength;
        nextVector.lng /= nextLength;
      }

      // Calculate bisector (average of normalized vectors)
      const bisector = {
        lat: -(prevVector.lat + nextVector.lat) / 2,
        lng: -(prevVector.lng + nextVector.lng) / 2
      };

      // Normalize bisector
      const bisectorLength = Math.sqrt(bisector.lat * bisector.lat + bisector.lng * bisector.lng);
      if (bisectorLength > 0) {
        bisector.lat /= bisectorLength;
        bisector.lng /= bisectorLength;
      }

      // Calculate offset distance (adjust for angle to maintain consistent width)
      const angle = Math.acos(Math.max(-1, Math.min(1, -(prevVector.lat * nextVector.lat + prevVector.lng * nextVector.lng))));
      const offsetDistance = Math.abs(Math.sin(angle / 2)) > 0.1 ? fenceWidth / Math.sin(angle / 2) : fenceWidth;

      // Apply offset to create outer point
      const outerPoint = L.latLng(
        currentPoint.lat + bisector.lat * offsetDistance / latToMeters,
        currentPoint.lng + bisector.lng * offsetDistance / lngToMeters
      );

      outerPerimeter.push(outerPoint);
    }

    // Create fence polygon with hole (outer perimeter with inner perimeter as hole)
    const fencePolygonLatLngs = [
      outerPerimeter,  // Outer ring
      [...innerPerimeter].reverse()  // Inner ring (hole) - reversed for proper winding
    ];

    console.log(`[createPerimeterFence] Created fence polygon with ${outerPerimeter.length} outer vertices and ${innerPerimeter.length} inner vertices`);

    // Create a proper Leaflet polygon for the perimeter fence
    const fencePolygon = L.polygon(fencePolygonLatLngs, {
      color: '#654321', // Dark brown color for fence border
      weight: 3,
      fillColor: '#8B4513', // Brown fill for fence
      fillOpacity: 0.8,
      dashArray: '8, 4' // Dashed line to distinguish from solid obstacles
    });

    // Note: Fence polygon will be added to map by the calling function

    // Create a fence obstacle that behaves like a normal obstacle
    const fenceObstacle = {
      id: `${polygonId}-perimeter-fence`,
      layer: fencePolygon, // Use the actual Leaflet polygon
      height: fenceHeight,
      type: 'perimeter-fence',
      parentPolygonId: polygonId,
      area: 0 // Will be calculated if needed
    };

    console.log(`[createPerimeterFence] ✅ Created perimeter fence obstacle: ID=${fenceObstacle.id}, height=${fenceHeight}m, type=${fenceObstacle.type}`);

    return [fenceObstacle]; // Return as array for compatibility with existing code
  };

  // Handler for fence height modal submit
  const handleFenceHeightSubmit = () => {
    console.log('[handleFenceHeightSubmit] Attempting to complete building polygon with fence height:', currentFenceHeightInput);
    const fenceHeight = parseFloat(currentFenceHeightInput);

    if (isNaN(fenceHeight) || fenceHeight < 0) {
      alert('Please enter a valid non-negative number for the fence height.');
      return;
    }

    if (pendingBuildingPolygon) {
      // Complete the polygon drawing process and store the fence height
      const { layer } = pendingBuildingPolygon;

      // Store fence height as a property of the layer for shadow calculations
      layer.fenceHeight = fenceHeight;

      // Process the completed building polygon (similar to existing onDrawCreated logic)
      const newPolygonLeafletLatLngs = layer.getLatLngs()[0];
      const newPolygonGeoJSONCoords = newPolygonLeafletLatLngs.map(latlng => [latlng.lng, latlng.lat]);
      newPolygonGeoJSONCoords.push(newPolygonGeoJSONCoords[0]); // Close the loop for GeoJSON

      // Use drawing order to determine if this is main roof or obstacle
      const layerDrawingOrder = layer.drawingOrder || 1; // Default to 1 if not set

      if (layerDrawingOrder === 1) {
        // This is the first polygon (drawing order 1), becomes the main roof area
        setMainRoofLayer(layer);
        setMainRoofArea(calculateArea(layer));
        layer.setStyle({ color: 'black', weight: 3, fillColor: 'yellow', fillOpacity: 0.1, dashArray: null });
        console.log('[handleFenceHeightSubmit] First polygon (main roof) completed with fence height:', fenceHeight);

        // Create perimeter fence obstacle around the main roof
        if (fenceHeight > 0) {
          const fenceObstacles = createPerimeterFence(newPolygonLeafletLatLngs, fenceHeight, layer._leaflet_id);
          console.log(`[handleFenceHeightSubmit] Created perimeter fence obstacle for main roof (height: ${fenceHeight}m, width: 30cm)`);

          // Add fence polygon to the map
          fenceObstacles.forEach(fenceObstacle => {
            if (fenceObstacle.layer && drawnItemsRef.current) {
              drawnItemsRef.current.addLayer(fenceObstacle.layer);
              console.log(`[handleFenceHeightSubmit] Added fence polygon to map with ID: ${fenceObstacle.layer._leaflet_id}`);
            }
          });

          setObstacles(prevObstacles => {
            const updatedObstacles = [...prevObstacles, ...fenceObstacles];
            return updatedObstacles;
          });
        }
      } else {
        // This is the second or subsequent polygon - always treat as obstacle regardless of position
        layer.setStyle({ color: 'black', weight: 2, fillColor: '#A7841A', fillOpacity: 0.9, dashArray: null });
        const newObstacleArea = calculateArea(layer);
        console.log(`[handleFenceHeightSubmit] Obstacle polygon (drawing order: ${layerDrawingOrder}) completed. Area:`, newObstacleArea, 'Fence Height:', fenceHeight);

        if (fenceHeight > 0) {
          const fenceObstacles = createPerimeterFence(newPolygonLeafletLatLngs, fenceHeight, layer._leaflet_id);
          console.log(`[handleFenceHeightSubmit] Created perimeter fence obstacle for obstacle polygon (height: ${fenceHeight}m, width: 30cm)`);

          // Add fence polygon to the map
          fenceObstacles.forEach(fenceObstacle => {
            if (fenceObstacle.layer && drawnItemsRef.current) {
              drawnItemsRef.current.addLayer(fenceObstacle.layer);
              console.log(`[handleFenceHeightSubmit] Added fence polygon to map with ID: ${fenceObstacle.layer._leaflet_id}`);
            }
          });

          setObstacles(prevObstacles => {
            const updatedObstacles = [...prevObstacles, ...fenceObstacles];
            return updatedObstacles;
          });
        }
      }

      // Clear pending state
      setPendingBuildingPolygon(null);
    }

    // Close modal and reset input
    setShowFenceHeightModal(false);
    setCurrentFenceHeightInput('');
  };

  // calculateAndDisplayShadows function moved to after state declarations

  // Legacy shadow calculation function (kept as fallback)
  const calculateAndDisplayShadowsLegacy = async (currentObstaclesFromArgument) => {
    setIsCalculatingShadows(true);
    try {
    console.log('[calculateAndDisplayShadowsLegacy] Starting interval shadow analysis...');
    console.log('[calculateAndDisplayShadowsLegacy] Received currentObstaclesFromArgument:', currentObstaclesFromArgument, 'Type:', typeof currentObstaclesFromArgument, 'IsArray:', Array.isArray(currentObstaclesFromArgument));

    // Determine the obstacle list: use provided array if valid, otherwise fall back to the latest `obstacles` state.
    const currentObstacles = Array.isArray(currentObstaclesFromArgument) ? currentObstaclesFromArgument : obstacles;

    // 1. Create allShadowCasters array, starting with currentObstacles.
    let allShadowCasters = [...currentObstacles];

    // 2. Add autoPlacedPvFeatures if they exist (keep individual shadows for auto-placed panels)
    if (autoPlacedPvFeatures && autoPlacedPvFeatures.features && autoPlacedPvFeatures.features.length > 0) {
      const pvPanelShadowCasters = autoPlacedPvFeatures.features.map((feature, index) => {
        if (feature.geometry && feature.geometry.type === 'Polygon' && feature.geometry.coordinates && feature.geometry.coordinates.length > 0) {
          const geoJsonOuterRing = feature.geometry.coordinates[0]; // LngLat
          // Ensure the feature has an ID, default to index if not present in properties
          const featureId = feature.properties?.id || feature.id || `auto-pv-${index}`;
          return {
            id: featureId,
            layer: {
              // Convert GeoJSON LngLat coordinates to Leaflet LatLng objects
              getLatLngs: () => geoJsonOuterRing.map(coord => L.latLng(coord[1], coord[0]))
            },
            height: feature.properties.height || 0, // Use stored height, default to 0
            type: 'auto-pv-panel' // Mark as auto-placed for individual shadow processing
          };
        }
        return null; // In case of invalid feature geometry
      }).filter(caster => caster !== null); // Remove any nulls from malformed features

      allShadowCasters = allShadowCasters.concat(pvPanelShadowCasters);
      console.log(`[calculateAndDisplayShadows] Added ${pvPanelShadowCasters.length} auto-placed PV panels to shadow casters.`);
    }

    // 3. Add manual PV tables (group panels by table for unified shadows)
    if (pvTables && pvTables.length > 0) {
      const manualPvTableShadowCasters = pvTables.map(table => {
        // Convert each rectangle in the table to a shadow caster
        const tablePanels = table.rectangles.map((rect, panelIndex) => {
          // Convert rectangle bounds to LatLng coordinates
          // rect is an array of [lat, lng] pairs defining the rectangle corners
          const latLngs = rect.map(corner => L.latLng(corner[0], corner[1]));

          // Use table-specific properties for height calculation
          const tableProps = table.properties || {};
          const tablePanelTilt = tableProps.panelTilt || 25;
          const tablePanelLength = tableProps.panelLength || 2.278;
          const modulesY = tableProps.modulesY || 1;

          // Calculate height based on tilt angle and Y-direction module length
          // Height = sin(tilt) * (panel_length * modules_in_Y_direction)
          const effectiveLength = tablePanelLength * modulesY;
          const calculatedHeight = Math.sin(tablePanelTilt * Math.PI / 180) * effectiveLength;

          return {
            id: `${table.id}-panel-${panelIndex}`,
            layer: {
              getLatLngs: () => latLngs
            },
            height: calculatedHeight, // Height based on tilt and Y-direction stacking
            type: 'manual-pv-panel',
            tableId: table.id, // Group identifier for merging shadows
            panelIndex: panelIndex,
            tableProperties: tableProps // Store table properties for reference
          };
        });

        return tablePanels;
      }).flat(); // Flatten array of arrays

      allShadowCasters = allShadowCasters.concat(manualPvTableShadowCasters);
      console.log(`[calculateAndDisplayShadows] Added ${manualPvTableShadowCasters.length} manual PV panels from ${pvTables.length} tables to shadow casters.`);
    }

    console.log(`[calculateAndDisplayShadows] Total shadow casters: ${allShadowCasters.length}`);

    if (allShadowCasters.length === 0) {
    console.log('[calculateAndDisplayShadows] No obstacles or PV panels to cast shadows.');
    setShadowLayers([]);
    setTotalShadedAreaOnRoof(0); // Reset here
    setIsCalculatingShadows(false); // Ensure loading state is reset
    return;
  }

    const lat = location.lat;
    const lng = location.lng;
    const dateForSunCalc = new Date(analysisDate + 'T12:00:00');

    if (isNaN(dateForSunCalc.getTime())) {
      console.error('[calculateAndDisplayShadows] Invalid date for SunCalc:', analysisDate);
      alert('Invalid date selected for shadow analysis.');
      setShadowLayers([]);
      setTotalShadedAreaOnRoof(0); // Reset here
      return;
    }

    const sunTimes = SunCalc.getTimes(dateForSunCalc, lat, lng);
    const solarNoon = sunTimes.solarNoon;

    if (!solarNoon || isNaN(solarNoon.getTime())) {
        console.error('[calculateAndDisplayShadows] Could not calculate solar noon.');
        alert('Could not calculate solar noon for the selected date and location.');
        setShadowLayers([]);
        setTotalShadedAreaOnRoof(0); // Reset here
        return;
    }
    console.log(`[calculateAndDisplayShadows] Solar Noon: ${solarNoon.toISOString()}`);

    const duration = parseFloat(analysisDurationHours);
    if (isNaN(duration) || duration <= 0) {
      alert('Please enter a valid positive duration (in hours) for the analysis.');
      setShadowLayers([]);
      setTotalShadedAreaOnRoof(0); // Reset here
      return;
    }

    const halfDurationMs = (duration / 2) * 60 * 60 * 1000;
    const startTime = new Date(solarNoon.getTime() - halfDurationMs);
    const endTime = new Date(solarNoon.getTime() + halfDurationMs);
    
    console.log(`[calculateAndDisplayShadows] Analysis Interval: ${startTime.toISOString()} to ${endTime.toISOString()}`);

    const keyTimes = [startTime, solarNoon, endTime];
    const newLeafletShadows = []; // Will store one aggregated shadow per obstacle/table

    // Group manual PV panels by table for unified shadow processing
    const manualPvTables = new Map(); // tableId -> array of panels
    const individualShadowCasters = []; // Non-table shadow casters
    const fenceSegments = []; // Fence segments that need special clipping

    // Separate manual PV panels by table, fence segments, and individual shadow casters
    for (const obstacle of allShadowCasters) {
      if (obstacle.type === 'manual-pv-panel' && obstacle.tableId) {
        if (!manualPvTables.has(obstacle.tableId)) {
          manualPvTables.set(obstacle.tableId, []);
        }
        manualPvTables.get(obstacle.tableId).push(obstacle);
      } else if (obstacle.type === 'fence-segment') {
        fenceSegments.push(obstacle);
      } else {
        // This includes solid obstacles (no type field) and auto-placed PV panels
        individualShadowCasters.push(obstacle);
        console.log(`[Shadow Debug] Added obstacle ${obstacle.id} to individual shadow casters (type: ${obstacle.type || 'solid obstacle'})`);
      }
    }

    console.log(`[calculateAndDisplayShadows] Processing ${individualShadowCasters.length} individual shadow casters and ${manualPvTables.size} PV tables`);

    // Process individual shadow casters (obstacles, auto-placed PV panels)
    for (const obstacle of individualShadowCasters) {
      if (!obstacle.layer || typeof obstacle.layer.getLatLngs !== 'function') {
        console.warn('[calculateAndDisplayShadows] Skipping obstacle with invalid layer:', obstacle);
        continue;
      }
      let simpleObstacleLatLngs = [];
      const rawLatLngs = obstacle.layer.getLatLngs();

      if (rawLatLngs && rawLatLngs.length > 0) {
        if (rawLatLngs[0] instanceof L.LatLng) {
          simpleObstacleLatLngs = rawLatLngs;
        } else if (Array.isArray(rawLatLngs[0]) && rawLatLngs[0][0] instanceof L.LatLng) {
          simpleObstacleLatLngs = rawLatLngs[0];
        }
      }

      if (simpleObstacleLatLngs.length === 0) { // Check if empty after extraction
        console.warn('[calculateAndDisplayShadows] Skipping obstacle with no valid LatLngs:', obstacle);
        continue;
      }

      const obstacleHeight = obstacle.height;
      if (typeof obstacleHeight !== 'number' || obstacleHeight <= 0) {
        console.warn('[calculateAndDisplayShadows] Skipping obstacle with invalid height:', obstacle);
        continue;
      }

      const pointsForThisObstacleAggregatedHull = [];
      // Add current obstacle's own vertices to its specific hull points
      simpleObstacleLatLngs.forEach(p => {
        pointsForThisObstacleAggregatedHull.push(turfPoint([p.lng, p.lat]));
      });

      keyTimes.forEach((time, keyTimeIndex) => {
        const sunPos = SunCalc.getPosition(time, lat, lng);
        console.log(`[Shadow Debug] Time: ${time.toISOString()}, Sun altitude: ${sunPos.altitude * 180/Math.PI}°, azimuth: ${sunPos.azimuth * 180/Math.PI}°`);
        if (sunPos.altitude <= 0) {
          console.log(`[Shadow Debug] Skipping time ${time.toISOString()} - sun below horizon`);
          return;
        }
        const tanSunAltitude = Math.tan(sunPos.altitude);
        if (tanSunAltitude <= 0) return;

        const pointsForThisObstacleAtThisKeyTime = [];
        // Add obstacle's own vertices to this specific shadow's points
        // No need for pointsForThisObstacleAtThisKeyTime anymore for individual key time shadows
        // We directly add shadow points to pointsForThisObstacleAggregatedHull

        console.log(`[Shadow Debug] Processing obstacle ${obstacle.id} with ${simpleObstacleLatLngs.length} vertices, height: ${obstacleHeight}m`);
        simpleObstacleLatLngs.forEach((p, vertexIndex) => {
          const shadowLength = obstacleHeight / tanSunAltitude;
          const projectedStartPoint = L.CRS.EPSG3857.project(L.latLng(p.lat, p.lng));
          // Adjusted shadow direction based on observed visual output.
          // Standard transformations suggested the opposite, but this aligns with user feedback.
          const shadowEndX = projectedStartPoint.x - shadowLength * Math.sin(sunPos.azimuth);
          const shadowEndY = projectedStartPoint.y + shadowLength * Math.cos(sunPos.azimuth);
          const shadowProjectedPoint = L.point(shadowEndX, shadowEndY);
          const shadowVertexLatLng = L.CRS.EPSG3857.unproject(shadowProjectedPoint);
          const shadowTurfPoint = turfPoint([shadowVertexLatLng.lng, shadowVertexLatLng.lat]);

          if (vertexIndex === 0) {
            console.log(`[Shadow Debug] Vertex ${vertexIndex}: Original (${p.lat}, ${p.lng}) -> Shadow (${shadowVertexLatLng.lat}, ${shadowVertexLatLng.lng}), shadowLength: ${shadowLength}m`);
          }

          pointsForThisObstacleAggregatedHull.push(shadowTurfPoint);
        });

        // The block for individualKeyTimeShadowsForLeaflet is removed as we only want one aggregated shadow per obstacle
        // if (pointsForThisObstacleAtThisKeyTime.length >= 3) { ...
      }); // End of keyTimes.forEach

      // NEW SHADOW SYSTEM: Simple geometric shadow calculation
      console.log(`[Shadow Debug] Creating new simple directional shadow for obstacle ${obstacle.id}`);

      try {
        // Calculate shadow for each key time and merge results
        const allShadowPolygons = [];

        keyTimes.forEach((time, timeIndex) => {
          const sunPos = SunCalc.getPosition(time, lat, lng);
          if (sunPos.altitude <= 0) return; // Skip nighttime

          const tanSunAltitude = Math.tan(sunPos.altitude);
          if (tanSunAltitude <= 0) return;

          const shadowLength = obstacleHeight / tanSunAltitude;

          // Create directional shadow polygons for this time
          const timeShadowPolygons = createDirectionalShadow(simpleObstacleLatLngs, shadowLength, sunPos.azimuth);
          allShadowPolygons.push(...timeShadowPolygons);

          if (timeIndex === 0) {
            console.log(`[Shadow Debug] Time ${timeIndex}: shadowLength=${shadowLength.toFixed(1)}m, sunAzimuth=${(sunPos.azimuth * 180/Math.PI).toFixed(1)}°`);
            console.log(`[Shadow Debug] Created ${timeShadowPolygons.length} shadow polygons for time ${timeIndex}`);
          }
        });

        // Clip all shadow polygons to main building area
        const clippedShadowPolygons = clipShadowsToMainArea(allShadowPolygons, mainRoofLayer);

        // Add clipped shadow polygons to the results
        clippedShadowPolygons.forEach((shadowCoords, index) => {
          newLeafletShadows.push({
            id: `clipped_shadow_${obstacle.id}_${Date.now()}_${index}`,
            coordinates: shadowCoords,
            type: 'clipped_directional'
          });
        });

        console.log(`[Shadow Debug] ✅ NEW SYSTEM: Created ${allShadowPolygons.length} shadow polygons, clipped to ${clippedShadowPolygons.length} within main area for obstacle ${obstacle.id}`);

      } catch (shadowError) {
        console.error(`[Shadow Debug] ❌ NEW SYSTEM: Error creating shadow for obstacle ${obstacle.id}:`, shadowError);
      } // End of new shadow system
    } // End of individual shadow casters loop

    // Process fence segments with clipping to building polygon
    for (const fenceSegment of fenceSegments) {
      if (!fenceSegment.layer || typeof fenceSegment.layer.getLatLngs !== 'function') {
        console.warn('[calculateAndDisplayShadows] Skipping fence segment with invalid layer:', fenceSegment);
        continue;
      }

      let simpleFenceLatLngs = [];
      const rawLatLngs = fenceSegment.layer.getLatLngs();

      if (rawLatLngs && rawLatLngs.length > 0) {
        if (rawLatLngs[0] instanceof L.LatLng) {
          simpleFenceLatLngs = rawLatLngs;
        } else if (Array.isArray(rawLatLngs[0]) && rawLatLngs[0][0] instanceof L.LatLng) {
          simpleFenceLatLngs = rawLatLngs[0];
        }
      }

      if (simpleFenceLatLngs.length === 0) {
        console.warn('[calculateAndDisplayShadows] Skipping fence segment with no valid LatLngs:', fenceSegment);
        continue;
      }

      const fenceHeight = fenceSegment.height;
      if (typeof fenceHeight !== 'number' || fenceHeight <= 0) {
        console.warn('[calculateAndDisplayShadows] Skipping fence segment with invalid height:', fenceSegment);
        continue;
      }

      const pointsForThisFenceAggregatedHull = [];
      // Add fence segment's own vertices to its specific hull points
      simpleFenceLatLngs.forEach(p => {
        pointsForThisFenceAggregatedHull.push(turfPoint([p.lng, p.lat]));
      });

      keyTimes.forEach((time) => {
        const sunPos = SunCalc.getPosition(time, lat, lng);
        if (sunPos.altitude <= 0) return;
        const tanSunAltitude = Math.tan(sunPos.altitude);
        if (tanSunAltitude <= 0) return;

        simpleFenceLatLngs.forEach(p => {
          const shadowLength = fenceHeight / tanSunAltitude;
          const projectedStartPoint = L.CRS.EPSG3857.project(L.latLng(p.lat, p.lng));
          const shadowEndX = projectedStartPoint.x - shadowLength * Math.sin(sunPos.azimuth);
          const shadowEndY = projectedStartPoint.y + shadowLength * Math.cos(sunPos.azimuth);
          const shadowProjectedPoint = L.point(shadowEndX, shadowEndY);
          const shadowVertexLatLng = L.CRS.EPSG3857.unproject(shadowProjectedPoint);
          const shadowTurfPoint = turfPoint([shadowVertexLatLng.lng, shadowVertexLatLng.lat]);

          pointsForThisFenceAggregatedHull.push(shadowTurfPoint);
        });
      });

      // After processing all key times for this fence segment, calculate its aggregated shadow
      if (pointsForThisFenceAggregatedHull.length >= 3) {
        try {
          const featureCollectionForThisFence = { type: 'FeatureCollection', features: pointsForThisFenceAggregatedHull };
          const aggregatedShadowForThisFence = turfConvex(featureCollectionForThisFence);
          if (aggregatedShadowForThisFence && aggregatedShadowForThisFence.geometry && aggregatedShadowForThisFence.geometry.type === 'Polygon') {
            const originalShadowGeoJsonCoords = aggregatedShadowForThisFence.geometry.coordinates;

            // Find the building polygon that this fence belongs to for clipping
            let buildingPolygonForClipping = null;

            // Check if this fence belongs to the main roof
            if (mainRoofLayer && fenceSegment.parentPolygonId === mainRoofLayer._leaflet_id) {
              buildingPolygonForClipping = mainRoofLayer;
              console.log(`[calculateAndDisplayShadows] Found main roof as parent for fence ${fenceSegment.id}`);
            } else {
              // Check if it belongs to any obstacle building polygon
              const parentObstacle = obstacles.find(obs => obs.id === fenceSegment.parentPolygonId);
              if (parentObstacle && parentObstacle.layer) {
                buildingPolygonForClipping = parentObstacle.layer;
                console.log(`[calculateAndDisplayShadows] Found obstacle as parent for fence ${fenceSegment.id}`);
              } else {
                // Additional check: look for any layer with matching _leaflet_id in drawnItemsRef
                if (drawnItemsRef.current) {
                  drawnItemsRef.current.eachLayer((layer) => {
                    if (layer._leaflet_id === fenceSegment.parentPolygonId) {
                      buildingPolygonForClipping = layer;
                      console.log(`[calculateAndDisplayShadows] Found drawn layer as parent for fence ${fenceSegment.id}`);
                    }
                  });
                }
              }
            }

            if (buildingPolygonForClipping && typeof buildingPolygonForClipping.getLatLngs === 'function') {
              const buildingLatLngs = buildingPolygonForClipping.getLatLngs();
              let simpleBuildingLatLngs = [];

              if (buildingLatLngs && buildingLatLngs.length > 0) {
                if (buildingLatLngs[0] instanceof L.LatLng) {
                  simpleBuildingLatLngs = buildingLatLngs;
                } else if (Array.isArray(buildingLatLngs[0]) && buildingLatLngs[0][0] instanceof L.LatLng) {
                  simpleBuildingLatLngs = buildingLatLngs[0];
                }
              }

              if (simpleBuildingLatLngs.length >= 3) {
                try {
                  const buildingGeoJsonCoords = simpleBuildingLatLngs.map(latlng => [latlng.lng, latlng.lat]);
                  buildingGeoJsonCoords.push(buildingGeoJsonCoords[0]); // Close the loop
                  const buildingForClipping = [buildingGeoJsonCoords];

                  // Clip fence shadow to building polygon
                  const clippedResult = await enhancedIntersection(originalShadowGeoJsonCoords, buildingForClipping, `fence segment ${fenceSegment.id}`);
                  if (clippedResult && clippedResult.length > 0) {
                    const addShadowsFromCoords = (coords) => {
                      coords.forEach((polygon, polygonIndex) => {
                        if (polygon && polygon.length > 0 && polygon[0].length >= 3) {
                          const leafletCoords = polygon[0].map(coord => [coord[1], coord[0]]);
                          newLeafletShadows.push({
                            id: `clipped_fence_shadow_${fenceSegment.id}_${Date.now()}_${polygonIndex}`,
                            coordinates: leafletCoords,
                            type: 'aggregated'
                          });
                        }
                      });
                    };

                    if (Array.isArray(clippedResult[0][0][0])) { // MultiPolygon
                      addShadowsFromCoords(clippedResult);
                    } else if (Array.isArray(clippedResult[0][0])) { // Polygon
                      addShadowsFromCoords(clippedResult);
                    }
                  } else {
                    console.log(`[calculateAndDisplayShadows] Fence shadow for ${fenceSegment.id} is entirely outside the building polygon.`);
                  }
                } catch (clippingError) {
                  console.error(`[calculateAndDisplayShadows] Error during Martinez clipping for fence ${fenceSegment.id}:`, clippingError);
                  // Fallback: Add unclipped shadow
                  const leafletCoords = originalShadowGeoJsonCoords[0].map(coord => [coord[1], coord[0]]);
                  newLeafletShadows.push({
                    id: `fence_shadow_${fenceSegment.id}_${Date.now()}_unclipped_fallback`,
                    coordinates: leafletCoords,
                    type: 'aggregated'
                  });
                }
              } else {
                console.warn(`[calculateAndDisplayShadows] Building polygon for fence ${fenceSegment.id} not suitable for clipping.`);
              }
            } else {
              console.warn(`[calculateAndDisplayShadows] No building polygon found for fence ${fenceSegment.id} clipping.`);
            }
          } else {
            console.warn(`[calculateAndDisplayShadows] Aggregated convex hull for fence ${fenceSegment.id} resulted in null or invalid geometry.`);
          }
        } catch (hullError) {
          console.error(`[calculateAndDisplayShadows] Error during convex hull calculation for fence ${fenceSegment.id}:`, hullError);
        }
      } else {
        console.log(`[calculateAndDisplayShadows] Not enough points to form aggregated convex hull for fence ${fenceSegment.id}.`);
      }
    } // End of fence segments processing loop

    // Process manual PV tables as unified shadows
    for (const [tableId, tablePanels] of manualPvTables) {
      console.log(`[calculateAndDisplayShadows] Processing PV table ${tableId} with ${tablePanels.length} panels`);

      const pointsForThisTableAggregatedHull = [];

      // Add all panel vertices from this table to the aggregated hull
      for (const panel of tablePanels) {
        if (!panel.layer || typeof panel.layer.getLatLngs !== 'function') {
          console.warn('[calculateAndDisplayShadows] Skipping panel with invalid layer:', panel);
          continue;
        }

        let simplePanelLatLngs = [];
        const rawLatLngs = panel.layer.getLatLngs();

        if (rawLatLngs && rawLatLngs.length > 0) {
          if (rawLatLngs[0] instanceof L.LatLng) {
            simplePanelLatLngs = rawLatLngs;
          } else if (Array.isArray(rawLatLngs[0]) && rawLatLngs[0][0] instanceof L.LatLng) {
            simplePanelLatLngs = rawLatLngs[0];
          }
        }

        if (simplePanelLatLngs.length === 0) {
          console.warn('[calculateAndDisplayShadows] Skipping panel with no valid LatLngs:', panel);
          continue;
        }

        const panelHeight = panel.height;
        if (typeof panelHeight !== 'number' || panelHeight <= 0) {
          console.warn('[calculateAndDisplayShadows] Skipping panel with invalid height:', panel);
          continue;
        }

        // Add panel's own vertices to the aggregated hull
        simplePanelLatLngs.forEach(p => {
          pointsForThisTableAggregatedHull.push(turfPoint([p.lng, p.lat]));
        });

        // Calculate shadow points for this panel at key times
        keyTimes.forEach((time) => {
          const sunPos = SunCalc.getPosition(time, lat, lng);
          if (sunPos.altitude <= 0) return;
          const tanSunAltitude = Math.tan(sunPos.altitude);
          if (tanSunAltitude <= 0) return;

          simplePanelLatLngs.forEach(p => {
            const shadowLength = panelHeight / tanSunAltitude;
            const projectedStartPoint = L.CRS.EPSG3857.project(L.latLng(p.lat, p.lng));
            const shadowEndX = projectedStartPoint.x - shadowLength * Math.sin(sunPos.azimuth);
            const shadowEndY = projectedStartPoint.y + shadowLength * Math.cos(sunPos.azimuth);
            const shadowProjectedPoint = L.point(shadowEndX, shadowEndY);
            const shadowVertexLatLng = L.CRS.EPSG3857.unproject(shadowProjectedPoint);
            const shadowTurfPoint = turfPoint([shadowVertexLatLng.lng, shadowVertexLatLng.lat]);

            pointsForThisTableAggregatedHull.push(shadowTurfPoint);
          });
        });
      }

      // Create unified shadow for this table
      if (pointsForThisTableAggregatedHull.length >= 3) {
        try {
          const aggregatedHull = turfConvex(turfFeatureCollection(pointsForThisTableAggregatedHull));
          if (aggregatedHull && aggregatedHull.geometry && aggregatedHull.geometry.coordinates && aggregatedHull.geometry.coordinates.length > 0) {
            const originalShadowGeoJsonCoords = aggregatedHull.geometry.coordinates;

            // Apply clipping logic similar to individual shadows
            if (mainRoofLayer && typeof mainRoofLayer.getLatLngs === 'function') {
              const mainRoofLatLngs = mainRoofLayer.getLatLngs();
              let simpleMainRoofLatLngs = [];

              if (mainRoofLatLngs && mainRoofLatLngs.length > 0) {
                if (mainRoofLatLngs[0] instanceof L.LatLng) {
                  simpleMainRoofLatLngs = mainRoofLatLngs;
                } else if (Array.isArray(mainRoofLatLngs[0]) && mainRoofLatLngs[0][0] instanceof L.LatLng) {
                  simpleMainRoofLatLngs = mainRoofLatLngs[0];
                }
              }

              if (simpleMainRoofLatLngs.length >= 3) {
                try {
                  const mainRoofGeoJsonCoords = simpleMainRoofLatLngs.map(latlng => [latlng.lng, latlng.lat]);
                  mainRoofGeoJsonCoords.push(mainRoofGeoJsonCoords[0]); // Close the loop
                  const mainRoofForClipping = [mainRoofGeoJsonCoords];

                  const clippedResult = await enhancedIntersection(originalShadowGeoJsonCoords, mainRoofForClipping, `PV table ${tableId}`);
                  if (clippedResult && clippedResult.length > 0) {
                    const addShadowsFromCoords = (coords) => {
                      coords.forEach((polygon, polygonIndex) => {
                        if (polygon && polygon.length > 0 && polygon[0].length >= 3) {
                          const leafletCoords = polygon[0].map(coord => [coord[1], coord[0]]);
                          newLeafletShadows.push({
                            id: `aggregated_shadow_for_table_${tableId}_${Date.now()}_${polygonIndex}`,
                            coordinates: leafletCoords,
                            type: 'aggregated'
                          });
                        }
                      });
                    };

                    if (Array.isArray(clippedResult[0][0][0])) { // MultiPolygon
                      addShadowsFromCoords(clippedResult);
                    } else if (Array.isArray(clippedResult[0][0])) { // Polygon
                      addShadowsFromCoords(clippedResult);
                    }
                  } else {
                    console.log(`[calculateAndDisplayShadows] Shadow for table ${tableId} is entirely outside the main roof or intersection is empty.`);
                  }
                } catch (clippingError) {
                  console.error(`[calculateAndDisplayShadows] Error during Martinez clipping for table ${tableId}:`, clippingError);
                  // Fallback: Add unclipped shadow
                  const leafletCoords = originalShadowGeoJsonCoords[0].map(coord => [coord[1], coord[0]]);
                  newLeafletShadows.push({
                    id: `aggregated_shadow_for_table_${tableId}_${Date.now()}_unclipped_fallback`,
                    coordinates: leafletCoords,
                    type: 'aggregated'
                  });
                }
              } else {
                console.warn(`[calculateAndDisplayShadows] Main roof or original shadow for table ${tableId} not suitable for clipping. Adding unclipped shadow.`);
                const leafletCoords = originalShadowGeoJsonCoords[0].map(coord => [coord[1], coord[0]]);
                newLeafletShadows.push({
                  id: `aggregated_shadow_for_table_${tableId}_${Date.now()}_unclipped_fallback`,
                  coordinates: leafletCoords,
                  type: 'aggregated'
                });
              }
            } else {
              console.warn(`[calculateAndDisplayShadows] Main roof layer not found. Adding unclipped shadow for table ${tableId}.`);
              const leafletCoords = originalShadowGeoJsonCoords[0].map(coord => [coord[1], coord[0]]);
              newLeafletShadows.push({
                id: `aggregated_shadow_for_table_${tableId}_${Date.now()}_unclipped_noroof`,
                coordinates: leafletCoords,
                type: 'aggregated'
              });
            }
          } else {
            console.warn(`[calculateAndDisplayShadows] Aggregated convex hull for table ${tableId} resulted in null or invalid geometry.`);
          }
        } catch (hullError) {
          console.error(`[calculateAndDisplayShadows] Error during convex hull calculation for table ${tableId}:`, hullError);
        }
      } else {
        console.log(`[calculateAndDisplayShadows] Not enough points to form aggregated convex hull for table ${tableId}.`);
      }
    } // End of manual PV tables processing loop

    let calculatedTotalShadedAreaOnRoof = 0; // Initialize here
    if (newLeafletShadows.length > 0) {
      // Try to merge shadows intelligently (preserving both connected and isolated shadows)
      const processedShadows = await mergeShadowPolygons(newLeafletShadows);

      if (processedShadows && processedShadows.length > 0) {
        // Use processed shadows (merged + isolated)
        const mergedCount = newLeafletShadows.length - processedShadows.length;
        if (mergedCount > 0) {
          console.log(`[calculateAndDisplayShadows] Smart merging: ${newLeafletShadows.length} input shadows → ${processedShadows.length} output shadows (${mergedCount} shadows merged)`);
        } else {
          console.log(`[calculateAndDisplayShadows] No merging possible: ${processedShadows.length} isolated shadows preserved`);
        }

        // Calculate total area from all processed shadows with validation
        console.log(`[calculateAndDisplayShadows] Calculating areas for ${processedShadows.length} processed shadows...`);
        processedShadows.forEach((shadow, index) => {
          const shadowArea = calculateAreaFromCoords(shadow.coordinates);
          console.log(`[calculateAndDisplayShadows] Shadow ${index} (${shadow.id}): ${shadowArea.toFixed(2)} m²`);
          calculatedTotalShadedAreaOnRoof += shadowArea;
        });

        // Validate total against main roof area (warning only, don't block display)
        if (mainRoofArea > 0 && calculatedTotalShadedAreaOnRoof > mainRoofArea * 1.1) { // Allow 10% tolerance for overlapping shadows
          console.warn(`[calculateAndDisplayShadows] WARNING: Total shaded area (${calculatedTotalShadedAreaOnRoof.toFixed(2)} m²) exceeds main roof area (${mainRoofArea.toFixed(2)} m²)`);
          console.warn('[calculateAndDisplayShadows] This may indicate overlapping shadows or calculation precision issues');
          console.log('[calculateAndDisplayShadows] Shadow details:', processedShadows.map(s => ({
            id: s.id,
            coordCount: s.coordinates.length,
            area: calculateAreaFromCoords(s.coordinates).toFixed(2)
          })));
        } else {
          console.log(`[calculateAndDisplayShadows] Total shaded area: ${calculatedTotalShadedAreaOnRoof.toFixed(2)} m² (${((calculatedTotalShadedAreaOnRoof / mainRoofArea) * 100).toFixed(1)}% of roof)`);
        }
        setShadowLayers(processedShadows);
      } else {
        // Fall back to individual shadows if processing fails
        console.log(`[calculateAndDisplayShadows] Shadow processing failed, using ${newLeafletShadows.length} individual shadows`);
        console.log(`[calculateAndDisplayShadows] Calculating areas for ${newLeafletShadows.length} individual shadows...`);
        newLeafletShadows.forEach((shadow, index) => {
          const shadowArea = calculateAreaFromCoords(shadow.coordinates);
          console.log(`[calculateAndDisplayShadows] Individual Shadow ${index} (${shadow.id}): ${shadowArea.toFixed(2)} m²`);
          calculatedTotalShadedAreaOnRoof += shadowArea;
        });

        // Validate total against main roof area (warning only, don't block display)
        if (mainRoofArea > 0 && calculatedTotalShadedAreaOnRoof > mainRoofArea * 1.1) { // Allow 10% tolerance for overlapping shadows
          console.warn(`[calculateAndDisplayShadows] WARNING: Total shaded area (${calculatedTotalShadedAreaOnRoof.toFixed(2)} m²) exceeds main roof area (${mainRoofArea.toFixed(2)} m²)`);
          console.warn('[calculateAndDisplayShadows] This may indicate overlapping shadows or calculation precision issues');
        } else {
          console.log(`[calculateAndDisplayShadows] Total shaded area: ${calculatedTotalShadedAreaOnRoof.toFixed(2)} m² (${((calculatedTotalShadedAreaOnRoof / mainRoofArea) * 100).toFixed(1)}% of roof)`);
        }
        setShadowLayers(newLeafletShadows);
      }
    } else {
      console.log('[calculateAndDisplayShadows] No shadows were generated.');
      setShadowLayers([]);
    }
    setTotalShadedAreaOnRoof(calculatedTotalShadedAreaOnRoof); // Set the state
  } catch (error) {
      console.error("[calculateAndDisplayShadows] Error calculating shadows: ", error);
      alert("An error occurred while calculating shadows. Please check the console for details.");
      setShadowLayers([]); // Clear shadows on error
      setTotalShadedAreaOnRoof(0); // Reset on error
    } finally {
      setIsCalculatingShadows(false);
    }
  };

  const [pvTables, setPvTables] = useState([]); // Array of table objects
  const [drawTableMode, setDrawTableMode] = useState(false);
  const [drawTablePoints, setDrawTablePoints] = useState([]); // For line drawing
  const [selectedTableId, setSelectedTableId] = useState(null);

  // Table movement state
  const [draggedTableId, setDraggedTableId] = useState(null);
  const [isDragStarted, setIsDragStarted] = useState(false); // Track if drag has actually started

  // Web Worker integration for shadow calculations (after all state declarations)
  const {
    isCalculating: isWorkerCalculating,
    progress: shadowProgress,
    progressMessage,
    error: shadowError,
    calculateShadows: calculateShadowsWithWorker,
    cancelCalculation: cancelShadowCalculation,
    isWorkerSupported
  } = useShadowWorker();

  // Shadow calculation function with Web Worker infrastructure and smart fallback
  const calculateAndDisplayShadows = useCallback(async (currentObstaclesFromArgument) => {
    console.log('🎯 [calculateAndDisplayShadows] SHADOW SYSTEM DEBUG - Starting shadow analysis...');
    console.log('📊 [calculateAndDisplayShadows] Current state:', {
      obstaclesCount: obstacles.length,
      pvTablesCount: pvTables.length,
      autoPlacedPvFeaturesCount: autoPlacedPvFeatures?.features?.length || 0,
      workerSupported: isWorkerSupported,
      currentShadowLayersCount: mapState.shadowLayers.length
    });
    console.log('[calculateAndDisplayShadows] Worker supported:', isWorkerSupported);

    // Try Web Worker first if supported, with intelligent fallback
    if (!isWorkerSupported) {
      console.log('[calculateAndDisplayShadows] Web Workers not supported, using legacy calculation');
      try {
        await calculateAndDisplayShadowsLegacy(currentObstaclesFromArgument);
        console.log('[calculateAndDisplayShadows] Legacy calculation completed successfully');
        return;
      } catch (error) {
        console.error('[calculateAndDisplayShadows] Legacy calculation failed:', error);
        alert('An error occurred while calculating shadows. Please check the console for details.');
        setShadowLayers([]);
        setTotalShadedAreaOnRoof(0);
        return;
      }
    }

    try {
      // Create calculation parameters
      const params = createShadowCalculationParams({
        obstacles: Array.isArray(currentObstaclesFromArgument) ? currentObstaclesFromArgument : obstacles,
        pvTables,
        location,
        analysisDate,
        analysisDurationHours,
        mainRoofLayer,
        autoPlacedPvFeatures
      });

      console.log('[calculateAndDisplayShadows] Attempting Web Worker calculation...');

      // Use Web Worker with legacy fallback function
      const legacyFallback = async () => {
        console.log('[calculateAndDisplayShadows] Executing legacy fallback calculation');
        await calculateAndDisplayShadowsLegacy(currentObstaclesFromArgument);
        return {
          shadows: [],
          totalShadedArea: 0,
          usedLegacyFallback: true
        };
      };

      const results = await calculateShadowsWithWorker(params, legacyFallback);

      console.log('[calculateAndDisplayShadows] Web Worker process completed:', results);

      // Check if we used fallback (either from worker recommendation or hook fallback)
      if (results.usedFallback || results.usedLegacyFallback) {
        console.log('[calculateAndDisplayShadows] Used fallback calculation - shadows already applied');
        return;
      }

      // If we get here, Web Worker provided actual results (future enhancement)
      const formattedResults = formatShadowResults(results);
      setShadowLayers(formattedResults.shadows);
      setTotalShadedAreaOnRoof(formattedResults.totalShadedArea);
      console.log(`[calculateAndDisplayShadows] Applied ${formattedResults.shadows.length} shadows from Web Worker`);

    } catch (error) {
      console.error('[calculateAndDisplayShadows] Web Worker process failed:', error);
      // Final fallback to legacy calculation
      console.log('[calculateAndDisplayShadows] Final fallback to legacy calculation');
      try {
        await calculateAndDisplayShadowsLegacy(currentObstaclesFromArgument);
        console.log('[calculateAndDisplayShadows] Final fallback completed successfully');
      } catch (legacyError) {
        console.error('[calculateAndDisplayShadows] All calculation methods failed:', legacyError);
        alert('An error occurred while calculating shadows. Please check the console for details.');
        setShadowLayers([]);
        setTotalShadedAreaOnRoof(0);
      }
    }
  }, [
    obstacles,
    pvTables,
    location,
    analysisDate,
    analysisDurationHours,
    mainRoofLayer,
    autoPlacedPvFeatures,
    calculateShadowsWithWorker,
    isWorkerSupported
  ]);

  // Effect to notify parent about building data changes
  useEffect(() => {
    if (onBuildingDataChange) {
      onBuildingDataChange({
        mainRoofArea,
        totalShadedAreaOnRoof
      });
    }
  }, [mainRoofArea, totalShadedAreaOnRoof, onBuildingDataChange]);

  // Effect to notify parent about selected table changes
  useEffect(() => {
    if (onSelectedTableChange) {
      const selectedTable = pvTables.find(table => table.selected);
      onSelectedTableChange(selectedTable || null);
    }
  }, [pvTables, onSelectedTableChange]);

  // Effect to trigger shadow analysis when new tables are added or updated
  useEffect(() => {
    const tableWithTrigger = pvTables.find(table => table.triggerShadowAnalysis);
    if (tableWithTrigger) {
      console.log('[Auto Shadow Analysis] Triggering shadow analysis for table:', tableWithTrigger.id);

      // Remove the trigger flag from all tables
      setPvTables(tables => tables.map(table => ({
        ...table,
        triggerShadowAnalysis: false
      })));

      // Trigger shadow analysis
      setTimeout(() => {
        calculateAndDisplayShadows();
      }, 50);
    }
  }, [pvTables, calculateAndDisplayShadows]);

  const [dragStartPosition, setDragStartPosition] = useState(null);
  const [isDragging, setIsDragging] = useState(false);

  // Area Drawing Logic
  const [drawingArea, setDrawingArea] = useState(false);

  // Event handler for when a shape is created
  const onDrawCreated = (e) => {
    const { layerType, layer } = e;
    // Note: layer is automatically added to drawnItemsRef.current by EditControl

    if (layerType === 'polygon') {
      // Increment polygon drawing order counter
      const currentDrawingOrder = polygonDrawingOrder + 1;
      setPolygonDrawingOrder(currentDrawingOrder);

      // Store drawing order on the layer for reference
      layer.drawingOrder = currentDrawingOrder;

      const newPolygonLeafletLatLngs = layer.getLatLngs()[0];
      const newPolygonGeoJSONCoords = newPolygonLeafletLatLngs.map(latlng => [latlng.lng, latlng.lat]);
      newPolygonGeoJSONCoords.push(newPolygonGeoJSONCoords[0]); // Close the loop for GeoJSON

      if (currentDrawingOrder === 1) {
        // This is the first polygon, becomes the main roof area - show fence height popup
        setPendingBuildingPolygon({ layer });
        setCurrentFenceHeightInput(''); // Reset input field
        setShowFenceHeightModal(true);
        console.log('[onDrawCreated] First polygon created (drawing order: 1), showing fence height modal for building area');
      } else {
        // This is the second or subsequent polygon - always treat as obstacle regardless of position
        layer.setStyle({ color: 'black', weight: 2, fillColor: '#A7841A', fillOpacity: 0.9, dashArray: null });
        const newObstacleArea = calculateArea(layer);
        console.log(`[onDrawCreated] Obstacle polygon created (drawing order: ${currentDrawingOrder}). Area calculated:`, newObstacleArea, 'Layer ID:', layer._leaflet_id);
        setPendingObstacle({ layer, area: newObstacleArea });
        setCurrentObstacleHeightInput(''); // Reset input field
        setShowHeightModalForObstacleId(layer._leaflet_id); // Show modal for this new obstacle
        // Main roof area does not change
      }
    } else if (layerType === 'polyline') {
      // Existing polyline logic for measurement
      const length = L.GeometryUtil.length(layer.getLatLngs());
      setMeasurement(`Length: ${formatLength(length)}`);
      layer.setStyle({ dashArray: '5, 5', color: 'var(--color-text)' });
      // drawnItemsRef.current.addLayer(layer); // No longer needed
    }
  };

  // Event handler for when shapes are edited
  const onDrawEdited = (e) => {
    e.layers.eachLayer(layer => {
      if (mainRoofLayer && layer._leaflet_id === mainRoofLayer._leaflet_id) { // Check if the edited layer is our main roof
        const latLngsArrays = mainRoofLayer.getLatLngs();
        if (latLngsArrays && latLngsArrays.length > 0) {
          const outerRing = latLngsArrays[0];
          let newArea = L.GeometryUtil.geodesicArea(outerRing);

          if (latLngsArrays.length > 1) { // Has holes
            const innerRings = latLngsArrays.slice(1);
            innerRings.forEach(hole => {
              newArea -= L.GeometryUtil.geodesicArea(hole);
            });
          }
          setMainRoofArea(newArea); // Update main roof area (gross area)
        }
      } else {
        // Check if an obstacle was edited
        const obstacleIndex = obstacles.findIndex(obs => obs.id === layer._leaflet_id);
        if (obstacleIndex !== -1) {
          const updatedArea = calculateArea(layer);
          console.log(`Obstacle ${layer._leaflet_id} edited. New area: ${updatedArea}`);
          setObstacles(prevObstacles => {
            const newObstacles = [...prevObstacles];
            newObstacles[obstacleIndex] = {
              ...newObstacles[obstacleIndex],
              layer: layer, // Layer object might be new or mutated
              area: updatedArea,
            };
            console.log('[onDrawEdited] Updated obstacles array:', newObstacles);
            return newObstacles;
          });
        } else {
          console.warn('Edited layer not found in obstacles state during edit:', layer._leaflet_id);
        }
      }
      if (layer instanceof L.Polyline && !(layer instanceof L.Polygon)) {
          const length = L.GeometryUtil.length(layer.getLatLngs());
          setMeasurement(`Length: ${formatLength(length)}`);
      }
    });
  };

  // Event handler for when shapes are deleted
  const onDrawDeleted = (e) => {
    let mainPolygonWasDeleted = false;
    e.layers.eachLayer(layer => {
      if (mainRoofLayer && layer._leaflet_id === mainRoofLayer._leaflet_id) {
        setMainRoofLayer(null);
        setMainRoofArea(0);
        // Reset drawing order counter when main roof is deleted
        setPolygonDrawingOrder(0);
        // When main roof is deleted, also remove all its obstacles from map and state
        obstacles.forEach(obs => {
          if (drawnItemsRef.current.hasLayer(obs.layer)) {
            drawnItemsRef.current.removeLayer(obs.layer);
          }
        });
        setObstacles([]);
        calculateAndDisplayShadows([]); // Recalculate with empty obstacles
        mainPolygonWasDeleted = true;
        console.log('[onDrawDeleted] Main roof polygon deleted, drawing order counter reset');
      } else {
        // Check if a building polygon was deleted - remove associated fence segments
        const deletedLayerId = layer._leaflet_id;
        const newObstacles = obstacles.filter(obs => {
          // Remove the obstacle itself and any fence segments associated with it
          return obs.id !== deletedLayerId && obs.parentPolygonId !== deletedLayerId;
        });
        console.log('[onDrawDeleted] Obstacles before filtering:', obstacles);
        console.log('[onDrawDeleted] New obstacles after filtering (removed fence segments):', newObstacles);
        setObstacles(newObstacles);
        calculateAndDisplayShadows(newObstacles); // Recalculate with updated obstacles
      }
    });

    // If all layers are cleared from the map (e.g. using the 'Clear all layers' button)
    // or if the main polygon was deleted and no other polygons are left to become the main one.
    setMeasurement(''); // Clear measurement text on any deletion
  };

  // Effect to add click listeners to obstacles for editing height
  useEffect(() => {
    if (!drawnItemsRef.current) return;

    const layers = []; // To keep track of layers we've added listeners to for cleanup

    obstacles.forEach(obstacle => {
      if (obstacle.layer && drawnItemsRef.current.hasLayer(obstacle.layer)) {
        const layer = obstacle.layer;
        // Define the click handler for this specific obstacle
        const clickHandler = () => handleObstacleClick(obstacle);

        // Remove any old listener before adding a new one to prevent duplicates
        layer.off('click', layer._clickHandler); // Use a stored reference if possible, or manage more robustly
        layer.on('click', clickHandler);
        layer._clickHandler = clickHandler; // Store for specific removal if needed
        layers.push(layer); 
        console.log(`[useEffect Obstacles] Added click listener to obstacle ID: ${obstacle.id}`);
      }
    });

    return () => {
      // Cleanup: remove click listeners when component unmounts or obstacles change
      layers.forEach(layer => {
        if (layer && layer._clickHandler) {
          layer.off('click', layer._clickHandler);
          delete layer._clickHandler; // Clean up the stored handler
          console.log(`[useEffect Obstacles Cleanup] Removed click listener from a layer`);
        }
      });
    };
  }, [obstacles, handleObstacleClick]); // Rerun if obstacles array or the handler function changes

  // Auto-calculate shadows when obstacles change
  useEffect(() => {
    if (obstacles.length > 0 && mainRoofLayer) {
      console.log(`[useEffect Auto-Shadow] Obstacles changed, auto-calculating shadows for ${obstacles.length} obstacles`);
      // Use a small delay to ensure all state updates are complete
      const timeoutId = setTimeout(() => {
        calculateAndDisplayShadows(obstacles);
      }, 50);

      return () => clearTimeout(timeoutId);
    }
  }, [obstacles.length, mainRoofLayer]); // Only depend on obstacles count and main roof to avoid infinite loops

  // Listen for fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      const isInFullscreen = !!onFullscreenChange();
      setIsFullscreen(isInFullscreen);
      
      // Snapshot-related logic removed from fullscreen handler.
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, [isFullscreen]);



  // GPS mode: locate and mark - memoized with useCallback
  const handleLocateMe = useCallback(() => {
    if (!navigator.geolocation) {
      alert('Geolocation is not supported by your browser.');
      return;
    }
    
    // Use a memoized success handler
    const handleSuccess = (pos) => {
      const newLocation = { lat: pos.coords.latitude, lng: pos.coords.longitude };
      setLocation(newLocation);
      setSelectedCoord(newLocation);
      
      // Make the map follow and move to the new marker location
      if (mapRef.current) {
        mapRef.current.setView([newLocation.lat, newLocation.lng], 18, {
          animate: true,
          duration: 1
        });
      }
    };
    
    // Use a memoized error handler
    const handleError = () => {
      alert('Unable to retrieve your location.');
    };
    
    navigator.geolocation.getCurrentPosition(
      handleSuccess,
      handleError,
      { enableHighAccuracy: true, timeout: 10000 }
    );
  }, [mapRef, setLocation, setSelectedCoord]);

  // Search handler - memoized with useCallback
  const handleSearch = useCallback(async (e) => {
    e.preventDefault();
    setSearchError('');
    
    // Use regex patterns as constants to avoid recompilation
    const coordRegex = /(-?\d+\.?\d*)[, ]+(-?\d+\.?\d*)/;
    const gmapsRegex = /@(-?\d+\.\d+),(-?\d+\.\d+)/;
    
    let lat, lng;
    // Try to parse as coordinates
    const coordMatch = searchValue.match(coordRegex);
    if (coordMatch) {
      lat = parseFloat(coordMatch[1]);
      lng = parseFloat(coordMatch[2]);
    } else {
      // Try to parse Google Maps link
      const gmapsMatch = searchValue.match(gmapsRegex);
      if (gmapsMatch) {
        lat = parseFloat(gmapsMatch[1]);
        lng = parseFloat(gmapsMatch[2]);
      }
    }
    
    if (typeof lat === 'number' && typeof lng === 'number' && !isNaN(lat) && !isNaN(lng)) {
      const newLocation = { lat, lng };
      setLocation(newLocation);
      setSelectedCoord(newLocation);
      
      // Move the map to the new location
      if (mapRef.current) {
        mapRef.current.flyTo([lat, lng], mapRef.current.getZoom());
      }
    } else {
      setSearchError('Invalid coordinates or Google Maps link.');
    }
  }, [searchValue, mapRef, setLocation, setSelectedCoord, setSearchError]);

  // Update selectedCoord when location changes externally
  useEffect(() => {
    setSelectedCoord(location);
  }, [location]);

  // Drawing/Measuring handlers
  const onCreated = (e) => {
    const { layerType, layer } = e;
    let value = '';
    if (layerType === 'polygon') {
      const area = L.GeometryUtil.geodesicArea(layer.getLatLngs()[0]);
      value = 'Area: ' + formatArea(area);
    } else if (layerType === 'polyline') {
      const latlngs = layer.getLatLngs();
      let length = 0;
      for (let i = 1; i < latlngs.length; i++) {
        length += latlngs[i - 1].distanceTo(latlngs[i]);
      }
      value = 'Length: ' + formatLength(length);
    }
    setMeasurement(value);
  };
  
  // captureMapSnapshot function and related logic removed as snapshot functionality is disabled.

  const onEdited = (e) => {
    // Only show measurement for the first edited layer
    const layers = e.layers;
    let value = '';
    layers.eachLayer((layer) => {
      if (layer instanceof L.Polygon) {
        const area = L.GeometryUtil.geodesicArea(layer.getLatLngs()[0]);
        value = 'Area: ' + formatArea(area);
      } else if (layer instanceof L.Polyline) {
        const latlngs = layer.getLatLngs();
        let length = 0;
        for (let i = 1; i < latlngs.length; i++) {
          length += latlngs[i - 1].distanceTo(latlngs[i]);
        }
        value = 'Length: ' + formatLength(length);
      }
      // Only show for the first layer
      return false;
    });
    setMeasurement(value);
  };

  const onDeleted = () => {
    setMeasurement('');
  };

  // Satellite/OSM toggle handler - memoized with useCallback
  const handleToggleMap = useCallback(() => {
    setIsSatellite((prev) => {
      // When switching to satellite, adjust zoom level for better detail if needed
      if (!prev && mapRef.current) {
        const currentZoom = mapRef.current.getZoom();
        if (currentZoom < 16) {
          mapRef.current.setZoom(16);
        }
      }
      return !prev;
    });
  }, [mapRef]);

  // Handler for Draw Area button
  const handleDrawArea = () => {
    setDrawingArea(true);
    setCurrentObstacleLayer(null);
    setMainRoofArea(0);
    setObstacles([]);
    setMeasurement('');
  };

  // Handler for when a polygon is created (roof area)
  const onAreaCreated = (e) => {
    if (drawingArea) {
      const { layerType, layer } = e;
      if (layerType === 'polygon') {
        setMainRoofLayer(layer);
        setMainRoofArea(calculateArea(layer));
        setMeasurement('Area: ' + formatArea(calculateArea(layer)));
        setDrawingArea(false);
      }
    } else {
      onCreated(e); // fallback to normal measuring logic
    }
  };

  // Only allow one roof polygon at a time
  const handleEdit = (e) => {
    if (drawingArea) return; // Don't allow edit during drawing
    onEdited(e);
    // If roof polygon is edited, update area
    e.layers.eachLayer((layer) => {
      if (mainRoofLayer && layer.getLatLngs && mainRoofLayer.getLatLngs() &&
        JSON.stringify(layer.getLatLngs()[0]) === JSON.stringify(mainRoofLayer.getLatLngs()[0])) {
        const area = calculateArea(layer);
        setMainRoofArea(area);
        setMeasurement('Area: ' + formatArea(area));
      }
    });
  };

  // Remove roof polygon if deleted
  const handleDelete = (e) => {
    if (drawingArea) return;
    onDeleted(e);
    e.layers.eachLayer((layer) => {
      if (mainRoofLayer && layer.getLatLngs && mainRoofLayer.getLatLngs() &&
        JSON.stringify(layer.getLatLngs()[0]) === JSON.stringify(mainRoofLayer.getLatLngs()[0])) {
        setMainRoofLayer(null);
        setMainRoofArea(0);
        setObstacles([]);
        setMeasurement('');
      }
    });
  };

  // Clear All handler
  const handleClearAll = () => {
    setCurrentObstacleLayer(null);
    setMainRoofArea(0);
    setObstacles([]);
    setPvTables([]);
    setMeasurement('');
    setDrawingArea(false);
    setDrawTableMode(false);
  };

  // Clear Tables handler
  const handleClearTables = () => {
    console.log('[Clear All Tables] Clearing all tables');
    setPvTables([]);

    // Trigger shadow analysis after clearing all tables
    setTimeout(() => {
      calculateAndDisplayShadows();
    }, 50);
  };

  // Delete selected table handler
  const handleDeleteSelectedTable = useCallback(() => {
    if (selectedTableId) {
      console.log('[Table Deletion] Deleting table:', selectedTableId);
      setPvTables(tables => tables.filter(t => t.id !== selectedTableId));
      setSelectedTableId(null);

      // Trigger shadow analysis after deletion
      setTimeout(() => {
        calculateAndDisplayShadows();
      }, 50);
    }
  }, [selectedTableId]);



  // Table drawing logic
  useEffect(() => {
    if (!drawTableMode || !mainRoofLayer || !mapRef.current) return;

    // Handler for map click to define table
    function handleMapClick(e) {
      const map = e.target;
      const latlng = map.mouseEventToLatLng(e.originalEvent);
      // Check if click is inside roof polygon
      if (!L.polygon(mainRoofLayer.getLatLngs()[0]).getBounds().contains(latlng)) return;
      setDrawTablePoints(points => {
        if (points.length === 0) {
          return [latlng];
        } else if (points.length === 1) {
          // Second click: define table
          const start = points[0];
          const end = latlng;
          // Calculate angle and dimensions
          const dx = end.lng - start.lng;
          const dy = end.lat - start.lat;
          const angle = Math.atan2(dy, dx);
          // Panel size
          const panelW = panelOrientation === 'portrait' ? panelWidth : panelLength;
          const panelH = panelOrientation === 'portrait' ? panelLength : panelWidth;
          // Table size (smaller default for better visibility)
          const modulesX = 4;
          const modulesY = 2;
          const spacingX = 0.02;
          const spacingY = 0.02;

          // Calculate table center position first
          const centerLat = (start.lat + end.lat) / 2;
          const centerLng = (start.lng + end.lng) / 2;

          // Build rectangles for each panel using the same approach as automatic placement
          const rectangles = [];
          const tableCenter = [centerLng, centerLat]; // [lng, lat] for Turf.js

          // Use the same rotation angle calculation as automatic placement
          const mapRotationAngle = (180 - ((angle * 180 / Math.PI) % 360));

          for (let ix = 0; ix < modulesX; ix++) {
            for (let iy = 0; iy < modulesY; iy++) {
              // Calculate panel position relative to table center (in meters)
              const localXMeters = (ix * (panelW + spacingX)) - ((modulesX * (panelW + spacingX) - spacingX) / 2) + (panelW / 2);
              const localYMeters = (iy * (panelH + spacingY)) - ((modulesY * (panelH + spacingY) - spacingY) / 2) + (panelH / 2);

              // Convert meter offsets to degree offsets
              const panelOffsetX_deg = localXMeters / (111000 * Math.cos(centerLat * Math.PI / 180));
              const panelOffsetY_deg = localYMeters / 111000;

              // Unrotated center of this individual panel
              const unrotatedPanelCenterLng = centerLng + panelOffsetX_deg;
              const unrotatedPanelCenterLat = centerLat + panelOffsetY_deg;

              // Create panel dimensions in degrees
              const halfPanelLengthDeg = (panelH / 2) / 111000;
              const halfPanelWidthDeg = (panelW / 2) / (111000 * Math.cos(unrotatedPanelCenterLat * Math.PI / 180));

              // Create unrotated polygon for this individual panel
              const unrotatedPanelGeo = turfPolygonHelper([
                [
                  [unrotatedPanelCenterLng - halfPanelWidthDeg, unrotatedPanelCenterLat - halfPanelLengthDeg],
                  [unrotatedPanelCenterLng + halfPanelWidthDeg, unrotatedPanelCenterLat - halfPanelLengthDeg],
                  [unrotatedPanelCenterLng + halfPanelWidthDeg, unrotatedPanelCenterLat + halfPanelLengthDeg],
                  [unrotatedPanelCenterLng - halfPanelWidthDeg, unrotatedPanelCenterLat + halfPanelLengthDeg],
                  [unrotatedPanelCenterLng - halfPanelWidthDeg, unrotatedPanelCenterLat - halfPanelLengthDeg],
                ]
              ]);

              // Rotate this individual panel, PIVOTING AROUND THE ENTIRE TABLE'S CENTER
              const rotatedPanelGeo = transformRotate(unrotatedPanelGeo, mapRotationAngle, { pivot: tableCenter });

              // Extract coordinates and convert to the format expected by React-Leaflet
              const coordinates = rotatedPanelGeo.geometry.coordinates[0];
              // Remove the last coordinate (which is the same as the first) and convert to [lat, lng] format
              const rectangleCorners = coordinates.slice(0, -1).map(([lng, lat]) => [lat, lng]);

              rectangles.push(rectangleCorners);
            }
          }

          // Add table to state with enhanced properties
          const newTable = {
            id: Date.now() + Math.random(),
            rectangles,
            selected: false,

            // Position for movement
            position: { lat: centerLat, lng: centerLng },

            // Individual table properties
            properties: {
              panelLength: panelLength || 2.278,
              panelWidth: panelWidth || 1.134,
              panelTilt: panelTilt || 25,
              panelOrientation: panelOrientation || 'portrait',
              modulesX: modulesX, // Use the calculated values
              modulesY: modulesY, // Use the calculated values
              spacingX: spacingX, // Use the calculated spacing
              spacingY: spacingY, // Use the calculated spacing
              azimuth: (angle * 180 / Math.PI) % 360, // Convert angle to degrees
              modulePower: 600, // Add the new module power default
            },

            // Movement state
            isDragging: false,
            dragOffset: { lat: 0, lng: 0 },

            // Metadata
            createdAt: Date.now(),
            lastModified: Date.now(),
            // Flag to trigger shadow analysis after this table is added
            triggerShadowAnalysis: true
          };

          setPvTables(tables => [...tables, newTable]);
          setDrawTableMode(false);
          return [];
        }
        return points;
      });
    }

    // Attach event to the map reference
    const map = mapRef.current;
    if (map) {
      map.on('click', handleMapClick);
    }

    return () => {
      if (map) {
        map.off('click', handleMapClick);
      }
      setDrawTablePoints([]);
    };
  }, [drawTableMode, mainRoofLayer, panelWidth, panelLength, panelOrientation]);

  // Function to calculate table rectangles based on position and properties
  const calculateTableRectangles = useCallback((table) => {
    const { position, properties } = table;
    const {
      panelLength,
      panelWidth,
      panelOrientation,
      modulesX,
      modulesY,
      spacingX,
      spacingY,
      azimuth,
      panelTilt
    } = properties;

    // Calculate panel dimensions based on orientation
    const panelW = panelOrientation === 'portrait' ? panelWidth : panelLength;
    const panelH = panelOrientation === 'portrait' ? panelLength : panelWidth;

    // Calculate tilt effects
    const tiltRadians = (panelTilt || 0) * Math.PI / 180;

    // Projected length of panel when tilted (affects Y-direction spacing)
    const projectedPanelH = panelH * Math.cos(tiltRadians);

    // Calculate total grid dimensions (accounting for tilt in Y-direction)
    const totalWidth = (panelW * modulesX) + (spacingX * (modulesX - 1));
    const totalHeight = (projectedPanelH * modulesY) + (spacingY * (modulesY - 1));

    // Generate rectangles using the same approach as automatic placement
    const rectangles = [];
    const tableCenter = [position.lng, position.lat]; // [lng, lat] for Turf.js

    // Use the same rotation angle calculation as automatic placement
    const mapRotationAngle = (180 - (azimuth || 0));

    // Create unrotated panels first, then rotate the entire table
    for (let x = 0; x < modulesX; x++) {
      for (let y = 0; y < modulesY; y++) {
        // Calculate panel position relative to table center (in meters)
        const localXMeters = (x * (panelW + spacingX)) - (totalWidth / 2) + (panelW / 2);
        const localYMeters = (y * (projectedPanelH + spacingY)) - (totalHeight / 2) + (projectedPanelH / 2);

        // Convert meter offsets to degree offsets
        const panelOffsetX_deg = localXMeters / (111000 * Math.cos(position.lat * Math.PI / 180));
        const panelOffsetY_deg = localYMeters / 111000;

        // Unrotated center of this individual panel
        const unrotatedPanelCenterLng = position.lng + panelOffsetX_deg;
        const unrotatedPanelCenterLat = position.lat + panelOffsetY_deg;

        // Create panel dimensions in degrees
        const halfPanelLengthDeg = (projectedPanelH / 2) / 111000;
        const halfPanelWidthDeg = (panelW / 2) / (111000 * Math.cos(unrotatedPanelCenterLat * Math.PI / 180));

        // Create unrotated polygon for this individual panel
        const unrotatedPanelGeo = turfPolygonHelper([
          [
            [unrotatedPanelCenterLng - halfPanelWidthDeg, unrotatedPanelCenterLat - halfPanelLengthDeg],
            [unrotatedPanelCenterLng + halfPanelWidthDeg, unrotatedPanelCenterLat - halfPanelLengthDeg],
            [unrotatedPanelCenterLng + halfPanelWidthDeg, unrotatedPanelCenterLat + halfPanelLengthDeg],
            [unrotatedPanelCenterLng - halfPanelWidthDeg, unrotatedPanelCenterLat + halfPanelLengthDeg],
            [unrotatedPanelCenterLng - halfPanelWidthDeg, unrotatedPanelCenterLat - halfPanelLengthDeg],
          ]
        ]);

        // Rotate this individual panel, PIVOTING AROUND THE ENTIRE TABLE'S CENTER
        const rotatedPanelGeo = transformRotate(unrotatedPanelGeo, mapRotationAngle, { pivot: tableCenter });

        // Extract coordinates and convert to the format expected by React-Leaflet
        const coordinates = rotatedPanelGeo.geometry.coordinates[0];
        // Remove the last coordinate (which is the same as the first) and convert to [lat, lng] format
        const rectangleCorners = coordinates.slice(0, -1).map(([lng, lat]) => [lat, lng]);

        rectangles.push(rectangleCorners);
      }
    }

    return rectangles;
  }, []);

  // Function to handle table property updates from external components
  const handleTableUpdate = useCallback((tableId, newProperties) => {
    console.log('[Table Property Update] Updating table:', tableId, 'with properties:', newProperties);

    setPvTables(tables => {
      const updatedTables = tables.map(table => {
        if (table.id !== tableId) return table;

        const updatedTable = {
          ...table,
          properties: { ...table.properties, ...newProperties },
          lastModified: Date.now(),
          triggerShadowAnalysis: true // Flag to trigger shadow analysis
        };

        // Recalculate rectangles based on new properties
        return {
          ...updatedTable,
          rectangles: calculateTableRectangles(updatedTable)
        };
      });

      return updatedTables;
    });
  }, [calculateTableRectangles]);

  // Expose table update function to parent component
  useEffect(() => {
    if (onTableUpdate) {
      onTableUpdate(handleTableUpdate);
    }
  }, [onTableUpdate, handleTableUpdate]);

  // Expose manual placing functions to parent component
  useEffect(() => {
    if (onManualPlacingActions) {
      onManualPlacingActions({
        setDrawTableMode,
        handleClearTables,
        handleDeleteSelectedTable,
        handleDuplicateTable,
        handleDeselectAllTables,
        drawTableMode,
        pvTablesLength: pvTables.length,
        selectedTableId,
        mainRoofLayer: !!mainRoofLayer,
        drawTablePoints,
        setDrawTablePoints,
        pvTables // Pass the actual pvTables data for summary calculations
      });
    }
  }, [onManualPlacingActions, drawTableMode, pvTables.length, selectedTableId, mainRoofLayer, drawTablePoints, pvTables, handleDeleteSelectedTable]);



  // Handle table drag start (supports both mouse and touch)
  const handleTableDragStart = useCallback((tableId, event) => {
    // Prevent default behavior and stop propagation
    event.preventDefault();
    event.stopPropagation();

    setDraggedTableId(tableId);
    setIsDragStarted(true);

    // Handle both mouse and touch events
    const clientX = event.touches ? event.touches[0].clientX : event.clientX;
    const clientY = event.touches ? event.touches[0].clientY : event.clientY;

    setDragStartPosition({
      x: clientX,
      y: clientY,
      timestamp: Date.now()
    });

    // Don't set isDragging or disable map yet - wait for actual movement
    // Don't change table selection here - let click handler manage it
  }, []);

  // Handle table drag (supports both mouse and touch)
  const handleTableDrag = useCallback((event) => {
    if (!isDragStarted || !draggedTableId || !dragStartPosition) return;

    event.preventDefault();
    event.stopPropagation();

    // Handle both mouse and touch events
    const clientX = event.touches ? event.touches[0].clientX : event.clientX;
    const clientY = event.touches ? event.touches[0].clientY : event.clientY;

    // Calculate the movement delta
    const deltaX = clientX - dragStartPosition.x;
    const deltaY = clientY - dragStartPosition.y;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

    // Only start actual dragging if mouse moved more than 5 pixels
    const DRAG_THRESHOLD = 5;
    if (!isDragging && distance > DRAG_THRESHOLD) {
      setIsDragging(true);

      // Disable map dragging only when actual drag starts
      if (mapRef.current) {
        mapRef.current.dragging.disable();
        mapRef.current.touchZoom.disable();
        mapRef.current.doubleClickZoom.disable();
        mapRef.current.scrollWheelZoom.disable();
        mapRef.current.boxZoom.disable();
        mapRef.current.keyboard.disable();
      }

      // Set visual feedback for dragging
      setPvTables(tables => tables.map(t =>
        t.id === draggedTableId ? { ...t, isDragging: true } : t
      ));
    }

    // Only move table if we're actually dragging
    if (isDragging) {
      // Convert pixel movement to lat/lng movement (more accurate)
      const map = mapRef.current;
      if (!map) return;

      // Get map bounds to calculate proper scale
      const bounds = map.getBounds();
      const mapSize = map.getSize();

      const latDelta = -deltaY * (bounds.getNorth() - bounds.getSouth()) / mapSize.y;
      const lngDelta = deltaX * (bounds.getEast() - bounds.getWest()) / mapSize.x;

      // Update table position with smooth movement
      setPvTables(tables => tables.map(table => {
        if (table.id !== draggedTableId) return table;

        const newPosition = {
          lat: table.position.lat + latDelta,
          lng: table.position.lng + lngDelta
        };

        // Recalculate rectangles for new position
        const updatedTable = { ...table, position: newPosition };
        return {
          ...updatedTable,
          rectangles: calculateTableRectangles(updatedTable)
        };
      }));

      // Update drag start position for smooth continuous movement
      setDragStartPosition({
        x: clientX,
        y: clientY,
        timestamp: Date.now()
      });
    }
  }, [isDragStarted, isDragging, draggedTableId, dragStartPosition, calculateTableRectangles]);

  // Handle table drag end
  const handleTableDragEnd = useCallback(() => {
    if (!isDragStarted) return;

    // Re-enable map interactions only if we actually started dragging
    if (isDragging && mapRef.current) {
      mapRef.current.dragging.enable();
      mapRef.current.touchZoom.enable();
      mapRef.current.doubleClickZoom.enable();
      mapRef.current.scrollWheelZoom.enable();
      mapRef.current.boxZoom.enable();
      mapRef.current.keyboard.enable();
    }

    const wasActuallyDragging = isDragging && draggedTableId;

    setIsDragging(false);
    setIsDragStarted(false);
    setDraggedTableId(null);
    setDragStartPosition(null);

    // Clear dragging state from all tables
    setPvTables(tables => tables.map(t => ({ ...t, isDragging: false })));

    // Trigger shadow analysis after table movement if a table was actually moved
    if (wasActuallyDragging) {
      console.log('[Table Movement] Triggering shadow analysis after table movement');
      setTimeout(() => {
        calculateAndDisplayShadows();
      }, 50);
    }
  }, [isDragStarted, isDragging, draggedTableId]);



  // Handle table selection - memoized with useCallback
  const handleTableClick = useCallback((tableId, event) => {
    // Don't select if we're actually dragging (moved beyond threshold)
    if (isDragging) return;

    // Stop propagation if available
    if (event && event.originalEvent && event.originalEvent.stopPropagation) {
      event.originalEvent.stopPropagation();
    }

    // Toggle selection if clicking the same table, otherwise select new table
    const currentTable = pvTables.find(t => t.id === tableId);
    const shouldSelect = !currentTable?.selected;

    setPvTables(tables => tables.map(t =>
      t.id === tableId ? { ...t, selected: shouldSelect } : { ...t, selected: false }
    ));

    setSelectedTableId(shouldSelect ? tableId : null);
  }, [isDragging, pvTables]);

  // Handle deselect all tables
  const handleDeselectAllTables = useCallback(() => {
    setPvTables(tables => tables.map(t => ({ ...t, selected: false })));
    setSelectedTableId(null);
  }, []);

  // Handle table duplication
  const handleDuplicateTable = useCallback((tableId) => {
    const tableToClone = pvTables.find(t => t.id === tableId);
    if (!tableToClone) return;

    // Create a new table with offset position
    const offsetLat = 0.0001; // Small offset to avoid overlap
    const offsetLng = 0.0001;

    const newTable = {
      ...tableToClone,
      id: Date.now() + Math.random(),
      position: {
        lat: tableToClone.position.lat + offsetLat,
        lng: tableToClone.position.lng + offsetLng
      },
      selected: false,
      isDragging: false,
      createdAt: Date.now(),
      lastModified: Date.now()
    };

    // Recalculate rectangles for new position
    newTable.rectangles = calculateTableRectangles(newTable);

    // Add trigger flag for shadow analysis
    newTable.triggerShadowAnalysis = true;

    console.log('[Table Duplication] Duplicating table:', newTable.id);
    setPvTables(tables => [...tables, newTable]);
  }, [pvTables, calculateTableRectangles]);

  // Add global mouse and touch event listeners for dragging
  useEffect(() => {
    const handleMouseMove = (e) => handleTableDrag(e);
    const handleMouseUp = () => handleTableDragEnd();
    const handleTouchMove = (e) => {
      e.preventDefault(); // Prevent scrolling while dragging
      handleTableDrag(e);
    };
    const handleTouchEnd = () => handleTableDragEnd();

    if (isDragStarted) {
      // Mouse events
      document.addEventListener('mousemove', handleMouseMove, { passive: false });
      document.addEventListener('mouseup', handleMouseUp);

      // Touch events
      document.addEventListener('touchmove', handleTouchMove, { passive: false });
      document.addEventListener('touchend', handleTouchEnd);
      document.addEventListener('touchcancel', handleTouchEnd);

      // Visual feedback only when actually dragging
      if (isDragging) {
        document.body.style.cursor = 'grabbing';
        document.body.style.userSelect = 'none'; // Prevent text selection
      }
    }

    return () => {
      // Clean up mouse events
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);

      // Clean up touch events
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);
      document.removeEventListener('touchcancel', handleTouchEnd);

      // Reset styles
      document.body.style.cursor = 'default';
      document.body.style.userSelect = 'auto';
    };
  }, [isDragStarted, isDragging, handleTableDrag, handleTableDragEnd]);

  // Effect to manage map keyboard navigation based on table selection
  useEffect(() => {
    if (!mapRef.current) return;

    if (selectedTableId) {
      // Disable map keyboard navigation when a table is selected
      // Arrow keys will be used for module count adjustment instead
      mapRef.current.keyboard.disable();
    } else {
      // Re-enable map keyboard navigation when no table is selected
      mapRef.current.keyboard.enable();
    }
  }, [selectedTableId]);

  // Add keyboard shortcuts for table management
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Delete selected table with Delete key only (not Backspace)
      if (e.key === 'Delete' && selectedTableId) {
        e.preventDefault();
        setPvTables(tables => tables.filter(t => t.id !== selectedTableId));
        setSelectedTableId(null);

        // Trigger shadow analysis after deletion
        setTimeout(() => {
          calculateAndDisplayShadows();
        }, 50);
      }

      // WASD movement for selected table (slower movement for precise control)
      if (selectedTableId && ['w', 'W', 'a', 'A', 's', 'S', 'd', 'D'].includes(e.key)) {
        e.preventDefault(); // Prevent any default behavior
        e.stopPropagation(); // Stop event from bubbling up

        // Define movement step in degrees (even smaller for more precise control)
        const moveStep = 0.000002; // Reduced from 0.000005 for even more precise movement

        let latDelta = 0;
        let lngDelta = 0;

        switch (e.key) {
          case 'w':
          case 'W':
            latDelta = moveStep;
            break;
          case 's':
          case 'S':
            latDelta = -moveStep;
            break;
          case 'a':
          case 'A':
            lngDelta = -moveStep;
            break;
          case 'd':
          case 'D':
            lngDelta = moveStep;
            break;
        }

        // Update selected table position
        setPvTables(tables => tables.map(table => {
          if (table.id !== selectedTableId) return table;

          const newPosition = {
            lat: table.position.lat + latDelta,
            lng: table.position.lng + lngDelta
          };

          // Recalculate rectangles for new position
          const updatedTable = {
            ...table,
            position: newPosition,
            triggerShadowAnalysis: true // Flag to trigger shadow analysis
          };
          return {
            ...updatedTable,
            rectangles: calculateTableRectangles(updatedTable)
          };
        }));
      }

      // Arrow keys for adjusting module count of selected table
      if (selectedTableId && ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(e.key)) {
        e.preventDefault(); // Prevent any default behavior
        e.stopPropagation(); // Stop event from bubbling up

        // Update selected table module count
        setPvTables(tables => tables.map(table => {
          if (table.id !== selectedTableId) return table;

          const currentModulesX = table.properties?.modulesX || 5;
          const currentModulesY = table.properties?.modulesY || 1;
          let newModulesX = currentModulesX;
          let newModulesY = currentModulesY;

          switch (e.key) {
            case 'ArrowUp':
              // Increase modules in Y direction
              newModulesY = currentModulesY + 1;
              break;
            case 'ArrowDown':
              // Decrease modules in Y direction (minimum 1)
              newModulesY = Math.max(1, currentModulesY - 1);
              break;
            case 'ArrowRight':
              // Increase modules in X direction
              newModulesX = currentModulesX + 1;
              break;
            case 'ArrowLeft':
              // Decrease modules in X direction (minimum 1)
              newModulesX = Math.max(1, currentModulesX - 1);
              break;
          }

          // Update table properties with new module counts
          const updatedTable = {
            ...table,
            properties: {
              ...table.properties,
              modulesX: newModulesX,
              modulesY: newModulesY
            },
            triggerShadowAnalysis: true // Flag to trigger shadow analysis
          };

          // Recalculate rectangles for new module configuration
          return {
            ...updatedTable,
            rectangles: calculateTableRectangles(updatedTable)
          };
        }));
      }

      // E and Q keys for rotating selected table
      if (selectedTableId && ['e', 'E', 'q', 'Q'].includes(e.key)) {
        e.preventDefault(); // Prevent any default behavior
        e.stopPropagation(); // Stop event from bubbling up

        // Update selected table azimuth
        setPvTables(tables => tables.map(table => {
          if (table.id !== selectedTableId) return table;

          const currentAzimuth = table.properties?.azimuth || 0;
          let newAzimuth;

          switch (e.key) {
            case 'e':
            case 'E':
              // Rotate clockwise (decrease azimuth by 1 degree)
              newAzimuth = currentAzimuth - 1;
              break;
            case 'q':
            case 'Q':
              // Rotate counter-clockwise (increase azimuth by 1 degree)
              newAzimuth = currentAzimuth + 1;
              break;
          }

          // Normalize azimuth to -180 to 180 range
          while (newAzimuth > 180) newAzimuth -= 360;
          while (newAzimuth <= -180) newAzimuth += 360;

          // Update table properties with new azimuth
          const updatedTable = {
            ...table,
            properties: {
              ...table.properties,
              azimuth: newAzimuth
            },
            lastModified: Date.now(), // Update timestamp to trigger re-render
            triggerShadowAnalysis: true // Flag to trigger shadow analysis
          };

          // Recalculate rectangles for new azimuth
          return {
            ...updatedTable,
            rectangles: calculateTableRectangles(updatedTable)
          };
        }));
      }

      // T key to place PV table (manual placing mode)
      if ((e.key === 't' || e.key === 'T') && !selectedTableId) {
        e.preventDefault();
        e.stopPropagation();

        // Only activate if main roof layer exists and not already in draw mode
        if (mainRoofLayer && !drawTableMode) {
          setDrawTableMode(true);
        }
      }

      // R key to refresh shadow calculations (same as clicking refresh button)
      if (e.key === 'r' || e.key === 'R') {
        e.preventDefault();
        e.stopPropagation();

        // Only trigger if not already calculating (same logic as button)
        if (!isCalculatingShadows && !isWorkerCalculating) {
          calculateAndDisplayShadows();
        }
      }

      // F key to toggle shadow visibility (same as clicking toggle button)
      if (e.key === 'f' || e.key === 'F') {
        e.preventDefault();
        e.stopPropagation();

        // Toggle shadow visibility (works regardless of current state, same as button)
        toggleShadowVisibility();
      }

      // C key to duplicate selected table (same as clicking duplicate button)
      if (e.key === 'c' || e.key === 'C') {
        e.preventDefault();
        e.stopPropagation();

        if (selectedTableId) {
          console.log('[Keyboard] C key pressed - duplicating selected table:', selectedTableId);
          handleDuplicateTable(selectedTableId);
        }
      }

      // Escape key to deselect all tables or exit drawing mode
      if (e.key === 'Escape') {
        if (drawTableMode) {
          setDrawTableMode(false);
          setDrawTablePoints([]);
        } else if (selectedTableId) {
          handleDeselectAllTables();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [selectedTableId, drawTableMode, handleDeselectAllTables, calculateTableRectangles, toggleShadowVisibility, calculateAndDisplayShadows, isCalculatingShadows, isWorkerCalculating, mainRoofLayer, handleDuplicateTable]);

  // Button styles
  const buttonStyle = {
    padding: '8px 16px',
    border: 'none',
    borderRadius: '5px',
    background: 'var(--color-surface)',
    color: 'var(--color-text)',
    fontWeight: 600,
    cursor: 'pointer',
    fontSize: '15px',
    marginRight: '6px',
    transition: 'background 0.2s',
  };

  const buttonDanger = {
    ...buttonStyle,
    background: 'var(--color-surface)',
    color: 'var(--color-text)',
  };

  // Responsive styles based on screen size
  const getMapHeight = () => {
    if (window.innerWidth <= 480) {
      return 350;
    } else if (window.innerWidth <= 768) {
      return 400;
    } else {
      return 450;
    }
  };

  // --- Auto PV Placement Logic (Skelion-Style) ---
  const handleAutoPlacePanels = useCallback(async (panelParams) => {
    console.log('[handleAutoPlacePanels] Skelion-style placement triggered. Params:', panelParams, 'Current obstacles count:', obstacles.length, 'Current shadowLayers count:', mapState.shadowLayers.length);
    if (!panelParams) {
      console.warn('Auto-placement called without panel parameters.');
      return;
    }
    console.log('Attempting Skelion-style auto-placement with parameters:', panelParams);
    setIsPlacingPanels(true);

    // Clear existing auto-placed features and manual tables for fresh placement
    setAutoPlacedPvFeatures(null);

    if (!mainRoofLayer) {
      alert("Please define the main roof area first.");
      setIsPlacingPanels(false);
      return;
    }

    try {
      // Use the new Skelion-style placement algorithm
      console.log('[handleAutoPlacePanels] Using Skelion-style placement algorithm');

      // Call the Skelion-style placement function
      const pvTables = await placePVTablesSkeletonStyle(
        mainRoofLayer,
        obstacles,
        mapState.shadowLayers,
        panelParams
      );

      if (pvTables.length === 0) {
        alert("No suitable areas found for PV table placement. Try adjusting parameters or clearing obstacles/shadows.");
        setIsPlacingPanels(false);
        return;
      }

      console.log(`[handleAutoPlacePanels] Skelion algorithm generated ${pvTables.length} PV tables`);

      // Add the generated tables to the manual PV tables (they use the same structure)
      // This allows them to be edited, moved, and managed like manual tables
      setPvTables(existingTables => [...existingTables, ...pvTables]);

      // Clear auto-placed features since we're using manual tables now
      setAutoPlacedPvFeatures(null);

      console.log(`[handleAutoPlacePanels] Successfully placed ${pvTables.length} PV tables using Skelion-style algorithm`);



    } catch (error) {
      console.error("[handleAutoPlacePanels] Error:", error);
      alert("An error occurred during panel auto-placement. Check console.");
    } finally {
      setIsPlacingPanels(false);
    }
  }, [mainRoofLayer, obstacles, mapState.shadowLayers]);

  // useEffect to trigger auto-placement when a request comes via props
  useEffect(() => {
    console.log('[useEffect autoPlacement] Hook triggered. Request Timestamp:', autoPlacementRequest?.timestamp, 'Last processed:', lastProcessedTimestampRef.current);
    if (autoPlacementRequest && autoPlacementRequest.timestamp !== lastProcessedTimestampRef.current) {
      if (autoPlacementRequest.params) {
        console.log('[useEffect autoPlacement] Processing autoPlacementRequest with params:', autoPlacementRequest.params);
        handleAutoPlacePanels(autoPlacementRequest.params);
        lastProcessedTimestampRef.current = autoPlacementRequest.timestamp;
      } else {
        console.warn('[useEffect autoPlacement] autoPlacementRequest missing params.');
      }
    } else if (autoPlacementRequest && autoPlacementRequest.timestamp === lastProcessedTimestampRef.current) {
      console.log('[useEffect autoPlacement] autoPlacementRequest timestamp already processed.');
    }
  }, [autoPlacementRequest, handleAutoPlacePanels]);
  
  // State for responsive map height
  const [mapHeight, setMapHeight] = useState(getMapHeight());

  // Update map height on window resize
  useEffect(() => {
    const handleResize = () => {
      setMapHeight(getMapHeight());
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Note: Pane creation is now handled by MapPaneCreator component using useMap hook

  // Debug function to verify DOM structure and pane assignments
  const debugPaneStructure = useCallback(() => {
    if (mapRef.current) {
      const shadowPane = mapRef.current.getPane('shadowPane');
      const obstaclePane = mapRef.current.getPane('obstaclePane');
      const overlayPane = mapRef.current.getPane('overlayPane');

      console.log('[DOM Debug] Pane structure:');
      console.log('- shadowPane:', shadowPane, 'z-index:', shadowPane?.style.zIndex, 'children:', shadowPane?.children.length);
      console.log('- obstaclePane:', obstaclePane, 'z-index:', obstaclePane?.style.zIndex, 'children:', obstaclePane?.children.length);
      console.log('- overlayPane:', overlayPane, 'z-index:', overlayPane?.style.zIndex, 'children:', overlayPane?.children.length);

      // Check actual DOM elements
      if (shadowPane && shadowPane.children.length > 0) {
        console.log('[DOM Debug] Shadow elements in shadowPane:', Array.from(shadowPane.children).map(el => el.tagName));
      }
      if (obstaclePane && obstaclePane.children.length > 0) {
        console.log('[DOM Debug] PV elements in obstaclePane:', Array.from(obstaclePane.children).map(el => el.tagName));
      }
    }
  }, [mapRef]);

  // Trigger debug when shadows or PV features change
  useEffect(() => {
    if (shadowLayers.length > 0 || (autoPlacedPvFeatures && autoPlacedPvFeatures.features && autoPlacedPvFeatures.features.length > 0)) {
      setTimeout(debugPaneStructure, 100); // Small delay to ensure DOM is updated
    }
  }, [shadowLayers, autoPlacedPvFeatures, debugPaneStructure]);

  // Expose debug function globally for manual testing
  useEffect(() => {
    if (typeof window !== 'undefined') {
      window.debugMapPanes = debugPaneStructure;
      console.log('[Debug] Added window.debugMapPanes() function for manual testing');
    }
  }, [debugPaneStructure]);

  // Effect to enforce crosshair cursor when in draw table mode
  useEffect(() => {
    if (mapRef.current) {
      const mapContainer = mapRef.current.getContainer();
      if (drawTableMode) {
        console.log('[Crosshair] Applying crosshair cursor for manual placement mode');
        mapContainer.style.cursor = 'crosshair';
        mapContainer.classList.add('manual-placement-mode');

        // Also apply to all child elements
        const allElements = mapContainer.querySelectorAll('*');
        allElements.forEach(el => {
          el.style.cursor = 'crosshair';
        });
      } else {
        console.log('[Crosshair] Removing crosshair cursor');
        mapContainer.style.cursor = '';
        mapContainer.classList.remove('manual-placement-mode');

        // Reset cursor for all child elements
        const allElements = mapContainer.querySelectorAll('*');
        allElements.forEach(el => {
          el.style.cursor = '';
        });
      }
    }
  }, [drawTableMode]);

  return (
    <div style={{ width: '100%' }}>
      {/* Add CSS styles for PV table interaction */}
      <style dangerouslySetInnerHTML={{
        __html: `
          .pv-table-panel {
            cursor: grab !important;
            transition: opacity 0.2s ease;
          }
          .pv-table-panel:hover {
            opacity: 0.8;
          }
          .pv-table-panel:active {
            cursor: grabbing !important;
          }
          .leaflet-container {
            cursor: default;
          }
          .leaflet-container.leaflet-drag-target {
            cursor: grabbing !important;
          }
          /* Improve touch interaction on mobile */
          .leaflet-touch .pv-table-panel {
            cursor: pointer;
          }
          /* Spin animation for refresh icon */
          @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }

          /* Pulse animation for placement mode indicators */
          @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
          }

          /* Crosshair cursor for manual placement mode */
          .leaflet-container.manual-placement-mode {
            cursor: crosshair !important;
          }

          .leaflet-container.manual-placement-mode * {
            cursor: crosshair !important;
          }

          /* Additional crosshair enforcement for all map elements */
          .manual-placement-mode .leaflet-zoom-box,
          .manual-placement-mode .leaflet-interactive,
          .manual-placement-mode .leaflet-marker-icon,
          .manual-placement-mode .leaflet-popup,
          .manual-placement-mode .leaflet-control {
            cursor: crosshair !important;
          }

          /* First point marker styling */
          .first-point-marker {
            background: transparent !important;
            border: none !important;
          }
        `
      }} />

      {/* PV Panel Controls moved to Manual Placing tab */}
      {/* Panel parameter controls moved to popup */}
      <form onSubmit={handleSearch} className="map-controls">
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          flex: '1 1 200px',
          minWidth: window.innerWidth <= 480 ? '100%' : '200px',
          maxWidth: '100%',
          marginBottom: window.innerWidth <= 480 ? '8px' : '0'
        }}>
          <input
            type="text"
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            placeholder={window.innerWidth <= 480 ? "Search location" : "Search by address or coordinates"}
            style={{ 
              padding: window.innerWidth <= 480 ? '6px 10px' : '8px 12px', 
              borderRadius: '4px 0 0 4px', 
              border: '1px solid #ccc', 
              width: '100%',
              flex: '1',
              fontSize: window.innerWidth <= 480 ? '0.9rem' : '1rem'
            }}
          />
          <button 
            type="submit" 
            style={{
              padding: window.innerWidth <= 480 ? '6px 10px' : '8px 12px',
              background: 'var(--color-surface)',
              color: 'var(--color-text)',
              border: 'none',
              borderRadius: '0 4px 4px 0',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              flexShrink: 0
            }}
            title="Search"
          >
            <FaSearch />
          </button>
        </div>
        <button 
          type="button" 
          onClick={handleLocateMe} 
          className="map-control-button"
          style={{
            background: mode === MODES.GPS ? 'var(--color-surface)' : 'var(--color-surface)',
            color: mode === MODES.GPS ? 'var(--color-text)' : 'var(--color-text)',
            flexShrink: 0,
            whiteSpace: 'nowrap',
            padding: window.innerWidth <= 480 ? '6px 10px' : '8px 12px',
            fontSize: window.innerWidth <= 480 ? '0.9rem' : '1rem'
          }}
          title="Locate Me (GPS)"
        >
          <FaLocationArrow />
        </button>
      </form>
      {searchError && <div style={{ color: 'red', marginBottom: 8, fontSize: window.innerWidth <= 480 ? '0.9rem' : '1rem' }}>{searchError}</div>}
      <div
        style={{
          cursor: drawTableMode ? 'crosshair' : 'default',
          borderRadius: 8
        }}
        className={drawTableMode ? 'manual-placement-mode' : ''}
      >
        <MapContainer
          center={selectedCoord || { lat: 30.0444, lng: 31.2357 }}
          zoom={15}
          maxZoom={23}
          style={{
            height: mapHeight,
            width: '100%',
            borderRadius: 8,
            position: 'relative',
            minWidth: '100%',
            boxShadow: '0 2px 6px rgba(0, 0, 0, 0.1)',
            cursor: drawTableMode ? 'crosshair !important' : 'default'
          }}
          ref={mapRef}
          attributionControl={true} // Show attribution for proper credits
        >
        {/* Component to create custom map panes */}
        <MapPaneCreator setMapPanesReady={setMapPanesReady} mapRef={mapRef} />

        {/* TileLayer is now always visible as snapshot mode is removed */}
        <TileLayer
          url={isSatellite ? SATELLITE_PROVIDER.url : OSM_PROVIDER.url}
          attribution={isSatellite ? SATELLITE_PROVIDER.attribution : OSM_PROVIDER.attribution}
          maxNativeZoom={isSatellite ? SATELLITE_PROVIDER.maxZoom : OSM_PROVIDER.maxZoom}
          maxZoom={23} // Corresponds to MapContainer's maxZoom, enables overzooming
          // Add performance optimizations
          updateWhenIdle={true}
          updateWhenZooming={false}
          keepBuffer={2}
        />
        {/* ImageOverlay for snapshot removed */}

        {/* Render Shadow Layers FIRST - to ensure they appear behind everything else */}
        {showShadows && shadowLayers
          .filter(shadow => shadow.type === 'aggregated') // Only render aggregated shadows
          .map((shadow, index) => {
            // Determine the correct pane for shadows
            const shadowPaneName = mapPanesReady ? "shadowPane" : "overlayPane";

            if (index === 0) { // Log only for the first shadow to avoid excessive logging
              console.log(`[Render Shadows] 🎯 SHADOW RENDERING DEBUG:`);
              console.log(`  - Total shadowLayers: ${shadowLayers.length}`);
              console.log(`  - Pane: ${shadowPaneName} (z-index: ${mapPanesReady ? '350' : '400'})`);
              console.log(`  - First shadow coordinates length: ${shadow.coordinates ? shadow.coordinates.length : 'undefined'}`);
              console.log(`  - First shadow sample coords:`, shadow.coordinates ? shadow.coordinates.slice(0, 3) : 'none');

              // Verify pane exists
              if (mapRef.current) {
                const pane = mapRef.current.getPane(shadowPaneName);
                console.log(`  - Pane '${shadowPaneName}' exists:`, !!pane, 'z-index:', pane?.style.zIndex);
              }
            }
            return (
              <RLPolygon
                key={`shadow-${shadow.id}`} // Ensure unique key
                positions={shadow.coordinates}
                pane={shadowPaneName} // Render into the custom pane if ready, otherwise default
                interactive={false} // Make shadow non-interactive
                pathOptions={{
                  color: 'rgba(0, 0, 0, 0.3)',
                  weight: 1,
                  fillColor: '#000000',
                  fillOpacity: 0.4,
                  // Add explicit z-index style as fallback
                  zIndex: mapPanesReady ? 350 : 400
                }} // Style for aggregated shadow
              />
            );
          })}

        {/* Auto-placed PV Panels - rendered AFTER shadows to ensure they appear on top */}
        {isPlacingPanels && (
          <div style={{ position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)', background: 'var(--color-surface)', padding: '20px', borderRadius: '8px', zIndex: 2000 }}>
            Calculating Panel Placement...
          </div>
        )}

        {/* Table dragging indicator */}
        {isDragging && draggedTableId && (
          <div style={{
            position: 'absolute',
            top: '20px',
            left: '50%',
            transform: 'translateX(-50%)',
            background: 'rgba(251, 191, 36, 0.95)',
            color: 'white',
            padding: '8px 16px',
            borderRadius: '20px',
            fontSize: '14px',
            fontWeight: '600',
            zIndex: 2000,
            boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
            pointerEvents: 'none'
          }}>
            🔄 Moving PV Table - Release to place
          </div>
        )}
        {autoPlacedPvFeatures && autoPlacedPvFeatures.features && autoPlacedPvFeatures.features.length > 0 && (() => {
          try {
            return (
              <>
                {/* Log for FeatureGroup - Note: console.log in JSX runs on every render where this condition is met */}
                {false && console.log('[Render AutoPV] FeatureGroup details:', { pane: mapPanesReady ? 'obstaclePane' : 'overlayPane', key: 'auto-pv-feature-group' })}
                <FeatureGroup pane={mapPanesReady ? "obstaclePane" : "overlayPane"} key="auto-pv-feature-group">
                  {autoPlacedPvFeatures.features.map((feature, index) => {
                    try {
                      if (feature.geometry && feature.geometry.type === 'Polygon' && feature.geometry.coordinates && feature.geometry.coordinates[0]) {
                        const positions = feature.geometry.coordinates[0].map(coord => [coord[1], coord[0]]); // GeoJSON LngLat to Leaflet LatLng
                        const pvPaneName = mapPanesReady ? "obstaclePane" : "overlayPane";

                        /* Log for RLPolygon */
                        if (index === 0) { // Log only for the first panel to avoid excessive logging
                          console.log(`[Render AutoPV] Rendering ${autoPlacedPvFeatures.features.length} PV panel(s) in pane: ${pvPaneName} (z-index: ${mapPanesReady ? '450' : '400'})`);
                          // Verify pane exists
                          if (mapRef.current) {
                            const pane = mapRef.current.getPane(pvPaneName);
                            console.log(`[Render AutoPV] Pane '${pvPaneName}' exists:`, !!pane, 'z-index:', pane?.style.zIndex);
                          }
                        }
                        return (
                          <RLPolygon
                          key={`auto-pv-${index}`}
                          positions={positions}
                          pane={pvPaneName}
                          interactive={false} // Make non-interactive like shadows
                          pathOptions={{
                            color: 'darkblue',
                            weight: 1,
                            fillColor: 'blue',
                            fillOpacity: 1,
                            // Add explicit z-index style as fallback
                            zIndex: mapPanesReady ? 450 : 400
                          }}
                        />
                      );
                    }
                    } catch (featureError) {
                      console.error(`[Render AutoPV] Error rendering feature ${index}:`, featureError);
                      return null;
                    }
                    return null;
                  })}
                </FeatureGroup>
              </>
            );
          } catch (error) {
            console.error('[Render AutoPV] Error rendering auto-placed PV features:', error);
            return null;
          }
        })()}
        
        {/* Map Controls - Toggle button for Clear/Measure with pen icon */}
        <div className="leaflet-top leaflet-right" style={{ marginTop: '10px', marginRight: '10px' }}>
          <div className="leaflet-control leaflet-bar">
            <button 
              type="button" 
              onClick={() => {
                // Toggle between drawing (MEASURE) and normal (CLEAR) mode
                setMode(prevMode => prevMode === MODES.MEASURE ? MODES.CLEAR : MODES.MEASURE);
              }}
              style={{
                width: '36px',
                height: '36px',
                background: 'var(--color-surface)',
                border: '2px solid rgba(0,0,0,0.2)',
                borderRadius: '4px',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '26px',
                color: 'var(--color-text)'
              }}
              title={mode === MODES.MEASURE ? 'Exit Drawing Mode' : 'Enter Drawing Mode'}
            >
              <FaPen size={30} />
            </button>
          </div>
        </div>
        
        {/* Map controls container */}
        <div className="leaflet-bottom leaflet-left" style={{ marginBottom: '50px', marginLeft: '10px' }}>
          <div className="leaflet-control leaflet-bar" style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
            {/* Satellite provider selector removed - using only Google Satellite */}
            
            {/* Satellite view toggle button */}
            <button 
              type="button" 
              onClick={handleToggleMap}
              style={{
                width: '36px',
                height: '36px',
                background: isSatellite ? 'var(--color-surface)' : 'var(--color-surface)',
                border: '2px solid rgba(0,0,0,0.2)',
                borderRadius: '4px',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '26px',
                color: isSatellite ? 'var(--color-text)' : 'var(--color-text)'
              }}
              title={isSatellite ? 'Switch to Map View' : 'Switch to Satellite View'}
            >
              {isSatellite ? <FaMap size={30} /> : <FaSatelliteDish size={30} />}
            </button>
          </div>
        </div>
        
        {/* Fullscreen toggle button under zoom controls */}
        <div className="leaflet-bottom leaflet-left" style={{ marginBottom: '10px', marginLeft: '10px' }}>
          <div className="leaflet-control leaflet-bar">
            <button 
              type="button" 
              onClick={() => {
                if (mapRef.current) {
                  if (!isFullscreen) {
                    // Enter fullscreen
                    const container = mapRef.current.getContainer();
                    if (container.requestFullscreen) {
                      container.requestFullscreen();
                    } else if (container.mozRequestFullScreen) {
                      container.mozRequestFullScreen();
                    } else if (container.webkitRequestFullscreen) {
                      container.webkitRequestFullscreen();
                    } else if (container.msRequestFullscreen) {
                      container.msRequestFullscreen();
                    }
                    
                    // We'll let the fullscreen change event handler update the state
                  } else {
                    // Exit fullscreen
                    if (document.exitFullscreen) {
                      document.exitFullscreen();
                    } else if (document.mozCancelFullScreen) {
                      document.mozCancelFullScreen();
                    } else if (document.webkitExitFullscreen) {
                      document.webkitExitFullscreen();
                    } else if (document.msExitFullscreen) {
                      document.msExitFullscreen();
                    }
                    
                    // We'll let the fullscreen change event handler update the state
                  }
                }
              }}
              style={{
                width: '36px',
                height: '36px',
                background: 'var(--color-surface)',
                border: '2px solid rgba(0,0,0,0.2)',
                borderRadius: '4px',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '26px',
                color: 'var(--color-text)'
              }}
              title={isFullscreen ? 'Exit Fullscreen' : 'Enter Fullscreen'}
            >
              {isFullscreen ? <FaCompress size={30} /> : <FaExpand size={30} />}
            </button>
          </div>
        </div>
        <Marker position={[selectedCoord.lat, selectedCoord.lng]} icon={customIcon} />
        <MemoizedLocationEvents setLocation={setLocation} setSelectedCoord={setSelectedCoord} mode={mode} MODES={MODES} />
        <FeatureGroup ref={drawnItemsRef}>
          {/* roofPolygon is now rendered via FeatureGroup ref={drawnItemsRef} if it's added to drawnItemsRef.current */}
          {/* Render PV tables with optimized component */}
          <PVTableRenderer
            pvTables={pvTables}
            selectedTableId={selectedTableId}
            draggedTableId={draggedTableId}
            mapRef={mapRef}
            onTableClick={handleTableClick}
            onTableMouseDown={(tableId, e) => {
              if (e.originalEvent) {
                handleTableDragStart(tableId, e.originalEvent);
              }
            }}
            onTableMouseEnter={(tableId, e) => {
              // Change cursor to indicate draggable
              if (e.target && e.target._path) {
                e.target._path.style.cursor = 'grab';
              }
            }}
            onTableMouseLeave={(tableId, e) => {
              // Reset cursor
              if (e.target && e.target._path) {
                e.target._path.style.cursor = 'default';
              }
            }}
            enableViewportOptimization={true}
            maxTablesPerFrame={100}
          />

          {(mode === MODES.MEASURE || drawingArea) && (
            <EditControl
            key={`edit-control-${isLightTheme ? 'light' : 'dark'}`} // Force re-render on theme change
            position="bottomright"
            onCreated={onDrawCreated}
            onEdited={onDrawEdited}
            onDeleted={onDrawDeleted}
              draw={{
                rectangle: false,
                circle: false,
                circlemarker: false,
                marker: false,
                polyline: !drawingArea,
                polygon: {
                shapeOptions: {
                  color: 'black',
                  weight: 3,
                  fillColor: 'yellow',
                  fillOpacity: 0.1
                },
                allowIntersection: false,
                showArea: true,
                metric: ['km', 'm']
              },
              }}
              edit={{
                remove: true,
              }}
            />
          )}
        </FeatureGroup>

        {/* Visual indicator for first point in manual placement mode */}
        {drawTableMode && drawTablePoints.length === 1 && (
          <Marker
            position={drawTablePoints[0]}
            icon={new L.DivIcon({
              html: `
                <div style="
                  width: 20px;
                  height: 20px;
                  background: #3b82f6;
                  border: 3px solid #ffffff;
                  border-radius: 50%;
                  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  color: white;
                  font-weight: bold;
                  font-size: 10px;
                  animation: pulse 2s infinite;
                ">1</div>
              `,
              className: 'first-point-marker',
              iconSize: [26, 26],
              iconAnchor: [13, 13]
            })}
          />
        )}

      </MapContainer>
      </div>
      <div style={{ marginTop: 8 }}>
        {(() => {
          let displayArea = 0;
          if (mainRoofLayer) {
            const totalObstacleArea = obstacles.reduce((sum, obs) => {
              return sum + (obs.area || 0);
            }, 0);
            displayArea = mainRoofArea - totalObstacleArea;
          }
          return (
            <div style={{ color: 'green', fontWeight: 600, marginBottom: 4 }}>
              <span>{t('Usable Roof Area')}: {displayArea.toFixed(2)} m²</span>
            </div>
          );
        })()}
        <div style={{ marginTop: '10px', marginBottom: '10px', display: 'flex', alignItems: 'center', gap: '10px' }}>
          <div>
            <label htmlFor="analysisDate" style={{ marginRight: '5px', fontSize: '0.9em' }}>{t('Date')}:</label>
            <input 
              type="date" 
              id="analysisDate" 
              value={analysisDate} 
              onChange={e => setAnalysisDate(e.target.value)} 
              style={{ padding: '6px', border: '1px solid #ccc', borderRadius: '4px' }}
            />
          </div>
          <div>
            <label htmlFor="analysisDuration" style={{ marginRight: '5px', fontSize: '0.9em' }}>{t('Duration (hours)')}:</label>
            <input 
              type="number" 
              id="analysisDuration" 
              value={analysisDurationHours} 
              onChange={e => setAnalysisDurationHours(parseFloat(e.target.value))} 
              min="1"
              step="0.5"
              style={{ padding: '6px', border: '1px solid #ccc', borderRadius: '4px', width: '80px' }}
            />
          </div>
          <button
            onClick={() => calculateAndDisplayShadows()}
            disabled={isCalculatingShadows || isWorkerCalculating}
            style={{
              padding: '12px',
              background: 'var(--color-surface)',
              color: 'var(--color-text)',
              border: 'none',
              borderRadius: '6px',
              cursor: (isCalculatingShadows || isWorkerCalculating) ? 'not-allowed' : 'pointer',
              fontWeight: '600',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              minWidth: '48px',
              minHeight: '48px',
              opacity: (isCalculatingShadows || isWorkerCalculating) ? 0.6 : 1,
              transition: 'all 0.2s ease'
            }}
            title="Refresh Shading"
          >
            <FaSync
              size={18}
              style={{
                animation: (isCalculatingShadows || isWorkerCalculating) ? 'spin 1s linear infinite' : 'none'
              }}
            />
          </button>
          <button
            onClick={toggleShadowVisibility}
            style={{
              padding: '12px',
              background: 'var(--color-surface)',
              color: 'var(--color-text)',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontWeight: '600',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              minWidth: '48px',
              minHeight: '48px',
              transition: 'all 0.2s ease',
              position: 'relative'
            }}
            title={showShadows ? 'Hide Shadows' : 'Show Shadows'}
          >
            <div style={{
              width: '32px',
              height: '18px',
              borderRadius: '9px',
              background: showShadows ? '#10b981' : '#6b7280',
              position: 'relative',
              transition: 'background 0.2s ease'
            }}>
              <div style={{
                width: '14px',
                height: '14px',
                borderRadius: '50%',
                background: 'white',
                position: 'absolute',
                top: '2px',
                left: showShadows ? '16px' : '2px',
                transition: 'left 0.2s ease',
                boxShadow: '0 1px 3px rgba(0,0,0,0.3)'
              }} />
            </div>
          </button>
        </div>

        {/* Web Worker Progress Indicator */}
        {isWorkerCalculating && (
          <div style={{
            marginTop: '8px',
            padding: '12px',
            background: '#f0f9ff',
            borderRadius: '6px',
            border: '1px solid #3b82f6'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '8px' }}>
              <div style={{ fontSize: '14px', color: '#0369a1', fontWeight: '600' }}>
                {progressMessage} ({shadowProgress}%)
              </div>
              <button
                onClick={cancelShadowCalculation}
                style={{
                  padding: '4px 8px',
                  fontSize: '12px',
                  background: '#dc2626',
                  color: 'white',
                  border: 'none',
                  borderRadius: '3px',
                  cursor: 'pointer'
                }}
                title="Cancel shadow calculation"
              >
                Cancel
              </button>
            </div>
            <div style={{
              width: '100%',
              height: '6px',
              background: '#e5e7eb',
              borderRadius: '3px',
              overflow: 'hidden'
            }}>
              <div
                style={{
                  width: `${shadowProgress}%`,
                  height: '100%',
                  background: 'linear-gradient(90deg, #3b82f6, #1d4ed8)',
                  borderRadius: '3px',
                  transition: 'width 0.3s ease'
                }}
              />
            </div>
            {!isWorkerSupported && (
              <div style={{
                fontSize: '12px',
                color: '#f59e0b',
                marginTop: '4px',
                fontStyle: 'italic'
              }}>
                ⚠️ Using fallback calculation (Web Workers not supported)
              </div>
            )}
          </div>
        )}

        <p>{t('Obstacles')}: {obstacles.length} ({t('Heights')}: {obstacles.filter(o => o.height !== null && o.height !== '').length} {t('set')})</p>

        {/* PV Tables Information */}
        <div style={{ marginTop: '10px', padding: '8px', background: 'var(--color-surface)', borderRadius: '6px', border: '1px solid #e5e7eb' }}>
          <p style={{ margin: '0 0 8px 0', fontWeight: '600' }}>
            PV Tables: {pvTables.length}
            {selectedTableId && (
              <span style={{ color: '#10b981', marginLeft: '8px' }}>
                (1 selected)
              </span>
            )}
          </p>

          {selectedTableId && (() => {
            const selectedTable = pvTables.find(t => t.id === selectedTableId);
            if (!selectedTable) return null;

            const props = selectedTable.properties;
            const totalPanels = (props?.modulesX || 5) * (props?.modulesY || 1);

            // Calculate height for display
            const tiltRadians = (props?.panelTilt || 25) * Math.PI / 180;
            const effectiveLength = (props?.panelLength || 2.278) * (props?.modulesY || 1);
            const calculatedHeight = Math.sin(tiltRadians) * effectiveLength;

            return (
              <div style={{ fontSize: '14px', color: '#6b7280' }}>
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '4px', marginBottom: '4px' }}>
                  <span>Panels: {totalPanels} ({props?.modulesX || 5}×{props?.modulesY || 1})</span>
                  <span>Size: {props?.panelLength || 2.278}m × {props?.panelWidth || 1.134}m</span>
                </div>
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '4px', marginBottom: '4px' }}>
                  <span>Orientation: {props?.panelOrientation || 'portrait'}</span>
                  <span>Tilt: {props?.panelTilt || 25}°</span>
                </div>
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '4px' }}>
                  <span>Azimuth: {props?.azimuth || 0}°</span>
                  <span>Height: {calculatedHeight.toFixed(2)}m</span>
                </div>
              </div>
            );
          })()}
        </div>
        {mainRoofArea > 0 && (
          <p style={{ margin: '5px 0' }}>
            {t('Total Main Roof Area')}: <strong>{formatArea(mainRoofArea)}</strong>
          </p>
        )}
        {mainRoofArea > 0 && totalShadedAreaOnRoof > 0 && (
          <p style={{ margin: '5px 0' }}>
            {t('Total Shaded Area on Roof')}: <strong>{formatArea(totalShadedAreaOnRoof)}</strong>
          </p>
        )}
        {mainRoofArea > 0 && (
          <p style={{ margin: '5px 0', color: '#059669', fontWeight: 'bold' }}>
            {t('Non-Shaded Roof Area')}: <strong>{formatArea(Math.max(0, mainRoofArea - totalShadedAreaOnRoof))}</strong>
          </p>
        )}
        <b>{t('Selected Location')}:</b> {selectedCoord.lat.toFixed(4)}, {selectedCoord.lng.toFixed(4)}
        <br />
        <small>
          {mode === MODES.GPS && t('GPS will auto-locate if allowed and show a marker.')}
          {mode === MODES.CLEAR && t('Click map to select location or clear all.')}
          {mode === MODES.MEASURE && t('Draw polygons to measure area or lines to measure distance. Edit or delete as needed.')}
        </small>
        {measurement && (
          <div style={{ marginTop: 6, color: 'var(--color-text)', fontWeight: 600 }}>{measurement}</div>
        )}

        {/* Enhanced Table drawing mode indicator */}
        {drawTableMode && (
          <div style={{
            marginTop: 8,
            padding: '10px 14px',
            background: drawTablePoints.length === 0 ? '#10b981' : '#3b82f6',
            color: 'white',
            borderRadius: '8px',
            fontSize: '14px',
            fontWeight: '600',
            border: '2px solid rgba(255, 255, 255, 0.3)',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            animation: 'pulse 2s infinite'
          }}>
            {drawTablePoints.length === 0 ? (
              <>
                🎯 <strong>Manual Placement Active</strong>
                <span style={{ display: 'block', fontSize: '12px', marginTop: '4px', opacity: 0.9 }}>
                  Click first point on the roof to start placing PV table
                </span>
              </>
            ) : (
              <>
                🔵 <strong>First Point Set</strong>
                <span style={{ display: 'block', fontSize: '12px', marginTop: '4px', opacity: 0.9 }}>
                  Click second point to complete table placement
                </span>
              </>
            )}
            <div style={{
              fontSize: '11px',
              marginTop: '6px',
              opacity: 0.8,
              borderTop: '1px solid rgba(255, 255, 255, 0.2)',
              paddingTop: '4px'
            }}>
              Press <kbd style={{
                background: 'rgba(255, 255, 255, 0.2)',
                padding: '2px 4px',
                borderRadius: '3px',
                fontSize: '10px'
              }}>ESC</kbd> to cancel
            </div>
          </div>
        )}

        {/* Table interaction help */}
        {pvTables.length > 0 && !drawTableMode && (
          <div style={{
            marginTop: 8,
            padding: '6px 10px',
            background: '#f3f4f6',
            color: '#374151',
            borderRadius: '4px',
            fontSize: '12px',
            border: '1px solid #d1d5db'
          }}>
            💡 <strong>Controls:</strong> Click to select • Drag to move • Double-click to edit • Delete to remove • Esc to deselect<br/>
            <strong>Movement:</strong> Hover over any panel to see grab cursor • Arrow keys move selected table • Works on desktop (mouse) and mobile (touch)<br/>
            <strong>Physics:</strong> Tilt affects table length & height • Azimuth rotates entire table • Height = sin(tilt) × (panel_length × Y_modules)
          </div>
        )}
      </div>


      {/* Height Input Modal for Obstacles */}
      {showHeightModalForObstacleId && (
        createPortal(
          <div style={{
            position: 'fixed', left: '50%', top: '50%', transform: 'translate(-50%, -50%)',
            background: 'var(--color-surface)', padding: '20px', borderRadius: '8px', boxShadow: '0 4px 12px rgba(0,0,0,0.2)',
            zIndex: 2000, display: 'flex', flexDirection: 'column', gap: '10px', minWidth: '250px'
          }}>
            <h4>Enter Obstacle Height</h4>
            <label htmlFor="obstacleHeight">Height (meters):</label>
            <input 
              type="number" 
              id="obstacleHeight"
              value={currentObstacleHeightInput} 
              onChange={e => setCurrentObstacleHeightInput(e.target.value)} 
              placeholder="e.g., 3.5"
              style={{ padding: '8px', borderRadius: '4px', border: '1px solid #ccc' }}
            />
            <div style={{ display: 'flex', justifyContent: 'space-around', marginTop: '10px' }}>
              <button 
                onClick={handleModalSubmit}
                style={{ padding: '8px 15px', background: '#2563eb', color: 'var(--color-text)', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
              >
                Save Height
              </button>
              <button 
                onClick={() => setShowHeightModalForObstacleId(null)} 
                style={{ padding: '8px 15px', background: '#ccc', color: 'black', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
              >
                Cancel
              </button>
            </div>
          </div>,
          document.body
        )
      )}

      {/* Edit Obstacle Height Modal */}
      {editingObstacleId && (
        createPortal(
          <div style={{
            position: 'fixed', left: '50%', top: '50%', transform: 'translate(-50%, -50%)',
            background: 'var(--color-surface)', padding: '20px', borderRadius: '8px', boxShadow: '0 4px 12px rgba(0,0,0,0.2)',
            zIndex: 2001, display: 'flex', flexDirection: 'column', gap: '10px', minWidth: '250px'
          }}>
            <h4>Edit Obstacle Height</h4>
            <label htmlFor="editObstacleHeight">Height (meters):</label>
            <input
              type="number"
              id="editObstacleHeight"
              value={currentEditObstacleHeightInput}
              onChange={e => setCurrentEditObstacleHeightInput(e.target.value)}
              placeholder="e.g., 3.5"
              style={{ padding: '8px', borderRadius: '4px', border: '1px solid #ccc' }}
            />
            <div style={{ display: 'flex', justifyContent: 'space-around', marginTop: '10px' }}>
              <button
                onClick={handleUpdateObstacleHeight}
                style={{ padding: '8px 15px', background: '#2563eb', color: 'var(--color-text)', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
              >
                Save Changes
              </button>
              <button
                onClick={() => {
                  setEditingObstacleId(null);
                  setCurrentEditObstacleHeightInput('');
                }}
                style={{ padding: '8px 15px', background: '#ccc', color: 'black', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
              >
                Cancel
              </button>
            </div>
          </div>,
          document.body
        )
      )}

      {/* Fence Height Input Modal for Building Polygons */}
      {showFenceHeightModal && (
        createPortal(
          <div style={{
            position: 'fixed', left: '50%', top: '50%', transform: 'translate(-50%, -50%)',
            background: 'var(--color-surface)', padding: '20px', borderRadius: '8px', boxShadow: '0 4px 12px rgba(0,0,0,0.2)',
            zIndex: 2002, display: 'flex', flexDirection: 'column', gap: '10px', minWidth: '250px'
          }}>
            <label htmlFor="fenceHeight">Fence Height:</label>
            <input
              type="number"
              id="fenceHeight"
              value={currentFenceHeightInput}
              onChange={e => setCurrentFenceHeightInput(e.target.value)}
              placeholder="e.g., 2.0"
              style={{ padding: '8px', borderRadius: '4px', border: '1px solid #ccc' }}
              autoFocus
            />
            <div style={{ display: 'flex', justifyContent: 'space-around', marginTop: '10px' }}>
              <button
                onClick={handleFenceHeightSubmit}
                style={{ padding: '8px 15px', background: '#16a34a', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
              >
                Save
              </button>
              <button
                onClick={() => {
                  setShowFenceHeightModal(false);
                  setCurrentFenceHeightInput('');
                  setPendingBuildingPolygon(null);
                  // Remove the incomplete polygon from the map if it exists
                  if (pendingBuildingPolygon && pendingBuildingPolygon.layer && drawnItemsRef.current) {
                    drawnItemsRef.current.removeLayer(pendingBuildingPolygon.layer);
                  }
                }}
                style={{ padding: '8px 15px', background: '#dc2626', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
              >
                Cancel
              </button>
            </div>
          </div>,
          document.body
        )
      )}
    </div>
  );
}
