import { useState, useCallback, useMemo, useRef } from 'react';
import { validateObject, sanitizeObject, createSafeUpdater } from '../utils/stateValidation';

/**
 * PV Table State Management Hook
 * Centralized state management for PV tables with validation and optimization
 */
export const usePVTableState = (initialTables = []) => {
  const [pvTables, setPvTables] = useState(initialTables);
  const [selectedTableId, setSelectedTableId] = useState(null);
  const [draggedTableId, setDraggedTableId] = useState(null);
  
  // Performance optimization: track last update to prevent unnecessary recalculations
  const lastUpdateRef = useRef(Date.now());
  
  // Create safe updater for PV table validation
  const safeTableUpdater = useMemo(() => 
    createSafeUpdater('pvTable', (message, errors) => {
      console.warn('[usePVTableState]', message, errors);
    }), []
  );

  // Memoized selectors for performance
  const selectedTable = useMemo(() => 
    pvTables.find(table => table.id === selectedTableId) || null,
    [pvTables, selectedTableId]
  );

  const draggedTable = useMemo(() => 
    pvTables.find(table => table.id === draggedTableId) || null,
    [pvTables, draggedTableId]
  );

  const tableCount = useMemo(() => pvTables.length, [pvTables]);

  const totalPanelCount = useMemo(() => 
    pvTables.reduce((total, table) => {
      const modulesX = table.properties?.modulesX || 1;
      const modulesY = table.properties?.modulesY || 1;
      return total + (modulesX * modulesY);
    }, 0),
    [pvTables]
  );

  // Table management actions
  const addTable = useCallback((tableData) => {
    const newTable = {
      id: tableData.id || `table_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: Date.now(),
      lastModified: Date.now(),
      selected: false,
      ...tableData
    };

    // Validate new table
    const validation = validateObject(newTable, 'pvTable');
    if (!validation.isValid) {
      console.error('[usePVTableState] Invalid table data:', validation.errors);
      return false;
    }

    const sanitizedTable = sanitizeObject(newTable, 'pvTable');
    
    setPvTables(prev => [...prev, sanitizedTable]);
    lastUpdateRef.current = Date.now();
    return true;
  }, []);

  const updateTable = useCallback((tableId, updates) => {
    setPvTables(prev => prev.map(table => {
      if (table.id !== tableId) return table;
      
      const updatedTable = safeTableUpdater(table, {
        ...updates,
        lastModified: Date.now()
      });
      
      return updatedTable;
    }));
    lastUpdateRef.current = Date.now();
  }, [safeTableUpdater]);

  const removeTable = useCallback((tableId) => {
    setPvTables(prev => prev.filter(table => table.id !== tableId));
    
    // Clear selection if removed table was selected
    if (selectedTableId === tableId) {
      setSelectedTableId(null);
    }
    
    // Clear drag state if removed table was being dragged
    if (draggedTableId === tableId) {
      setDraggedTableId(null);
    }
    
    lastUpdateRef.current = Date.now();
  }, [selectedTableId, draggedTableId]);

  const selectTable = useCallback((tableId) => {
    // Deselect all tables first
    setPvTables(prev => prev.map(table => ({
      ...table,
      selected: table.id === tableId
    })));
    
    setSelectedTableId(tableId);
    lastUpdateRef.current = Date.now();
  }, []);

  const deselectAllTables = useCallback(() => {
    setPvTables(prev => prev.map(table => ({
      ...table,
      selected: false
    })));
    
    setSelectedTableId(null);
    lastUpdateRef.current = Date.now();
  }, []);

  const duplicateTable = useCallback((tableId, newPosition) => {
    const originalTable = pvTables.find(t => t.id === tableId);
    if (!originalTable) return false;

    const duplicatedTable = {
      ...originalTable,
      id: `table_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      position: newPosition || {
        lat: originalTable.position.lat + 0.0001,
        lng: originalTable.position.lng + 0.0001
      },
      selected: false,
      createdAt: Date.now(),
      lastModified: Date.now(),
      triggerShadowAnalysis: true
    };

    return addTable(duplicatedTable);
  }, [pvTables, addTable]);

  const moveTable = useCallback((tableId, newPosition) => {
    updateTable(tableId, {
      position: newPosition,
      triggerShadowAnalysis: true
    });
  }, [updateTable]);

  const rotateTable = useCallback((tableId, azimuthDelta) => {
    const table = pvTables.find(t => t.id === tableId);
    if (!table) return;

    const currentAzimuth = table.properties?.azimuth || 0;
    let newAzimuth = currentAzimuth + azimuthDelta;

    // Normalize azimuth to -180 to 180 range
    while (newAzimuth > 180) newAzimuth -= 360;
    while (newAzimuth <= -180) newAzimuth += 360;

    updateTable(tableId, {
      properties: {
        ...table.properties,
        azimuth: newAzimuth
      },
      triggerShadowAnalysis: true
    });
  }, [pvTables, updateTable]);

  const updateTableModules = useCallback((tableId, modulesX, modulesY) => {
    updateTable(tableId, {
      properties: {
        ...pvTables.find(t => t.id === tableId)?.properties,
        modulesX: Math.max(1, modulesX),
        modulesY: Math.max(1, modulesY)
      },
      triggerShadowAnalysis: true
    });
  }, [pvTables, updateTable]);

  // Drag state management
  const startDrag = useCallback((tableId) => {
    setDraggedTableId(tableId);
  }, []);

  const endDrag = useCallback(() => {
    setDraggedTableId(null);
  }, []);

  // Bulk operations
  const clearAllTables = useCallback(() => {
    setPvTables([]);
    setSelectedTableId(null);
    setDraggedTableId(null);
    lastUpdateRef.current = Date.now();
  }, []);

  const importTables = useCallback((tablesData) => {
    const validTables = tablesData.filter(table => {
      const validation = validateObject(table, 'pvTable');
      if (!validation.isValid) {
        console.warn('[usePVTableState] Skipping invalid table:', validation.errors);
        return false;
      }
      return true;
    });

    const sanitizedTables = validTables.map(table => 
      sanitizeObject(table, 'pvTable')
    );

    setPvTables(sanitizedTables);
    setSelectedTableId(null);
    setDraggedTableId(null);
    lastUpdateRef.current = Date.now();
    
    return sanitizedTables.length;
  }, []);

  // Performance metrics
  const getPerformanceMetrics = useCallback(() => ({
    tableCount,
    totalPanelCount,
    lastUpdate: lastUpdateRef.current,
    selectedTableId,
    draggedTableId
  }), [tableCount, totalPanelCount, selectedTableId, draggedTableId]);

  return {
    // State
    pvTables,
    selectedTableId,
    draggedTableId,
    selectedTable,
    draggedTable,
    
    // Computed values
    tableCount,
    totalPanelCount,
    
    // Actions
    addTable,
    updateTable,
    removeTable,
    selectTable,
    deselectAllTables,
    duplicateTable,
    moveTable,
    rotateTable,
    updateTableModules,
    
    // Drag operations
    startDrag,
    endDrag,
    
    // Bulk operations
    clearAllTables,
    importTables,
    
    // Utilities
    getPerformanceMetrics,
    
    // Direct state setters (for advanced use cases)
    setPvTables,
    setSelectedTableId,
    setDraggedTableId
  };
};

export default usePVTableState;
