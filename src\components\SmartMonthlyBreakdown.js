import React, { useState } from 'react';

const MONTHS = [
  'January', 'February', 'March', 'April', 'May', 'June',
  'July', 'August', 'September', 'October', 'November', 'December'
];

const TABS = [
  { key: 'energy', label: 'Energy Savings' },
  { key: 'bill', label: 'Bill Comparison' },
  { key: 'tier', label: 'Tariff Tiers' },
];

function getRelativeDecreases(beforeArr, afterArr) {
  const decreases = beforeArr.map((before, i) => Math.max(0, before - afterArr[i]));
  const max = Math.max(...decreases, 1);
  return decreases.map(d => d / max);
}

export default function SmartMonthlyBreakdown({ results }) {
  // Default to 'bill' tab
  const [tab, setTab] = useState('bill');
  if (!results) return null;

  // For visual-only indicators
  const energyRel = getRelativeDecreases(results.yearlyConsumptions, results.newConsumptions);
  const billRel = getRelativeDecreases(results.bills, results.newBills);

  return (
    <div className="tabular-results" style={{ marginTop: 24 }}>
      <div style={{ display: 'flex', gap: 8, marginBottom: 16 }}>
        {TABS.map(t => (
          <button
            key={t.key}
            onClick={() => setTab(t.key)}
            style={{
              padding: '8px 18px',
              borderRadius: 20,
              border: 'none',
              background: tab === t.key ? 'var(--color-text)' : 'var(--color-surface)',
              color: tab === t.key ? 'var(--color-surface)' : 'var(--color-text)',
              fontWeight: 600,
              fontSize: 15,
              cursor: 'pointer',
              boxShadow: tab === t.key ? '0 2px 8px rgba(37,99,235,0.08)' : 'none',
              transition: 'all 0.15s',
            }}
          >
            {t.label}
          </button>
        ))}
      </div>
      <div style={{ overflowX: 'auto' }}>
        {tab === 'energy' && (
          <table style={{ minWidth: 600, borderRadius: 12, overflow: 'hidden', boxShadow: '0 2px 12px rgba(0,0,0,0.04)' }}>
            <thead>
              <tr style={{ background: 'var(--color-surface)' }}>
                <th style={{ textAlign: 'center', color: 'var(--color-text)' }}>Month</th>
                <th style={{ textAlign: 'center', color: 'var(--color-text)' }}>Before</th>
                <th style={{ textAlign: 'center', color: 'var(--color-text)' }}>PV (kWh)</th>
                <th style={{ textAlign: 'center', color: 'var(--color-text)' }}>After</th>
              </tr>
            </thead>
            <tbody>
              {MONTHS.map((month, i) => {
                const before = results.yearlyConsumptions[i] || 0;
                const after = results.newConsumptions[i] || 0;
                const solar = results.monthlyProduction[i] || 0;
                const rel = energyRel[i];
                return (
                  <tr key={month} style={{ background: i % 2 ? 'var(--color-surface)' : 'var(--color-surface)' }}>
                    <td style={{ fontWeight: 600 }}>{month}</td>
                    <td>{before.toFixed(2)}</td>
                    <td style={{ textAlign: 'center' }}>
                      {rel > 0 && (
                        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: 24 }}>
                          <div style={{
                            width: 30,
                            height: 8,
                            background: `linear-gradient(90deg, var(--color-text) ${rel * 100}%, var(--color-surface) ${rel * 100}%)`,
                            borderRadius: 4,
                            marginRight: 6,
                          }} />
                          <span style={{ color: 'var(--color-text)', fontWeight: 700, fontSize: 15, marginRight: 6 }}>{solar.toFixed(2)}</span>
                          <span style={{ color: 'var(--color-text)', fontWeight: 700, fontSize: 18 }}>↓</span>
                        </div>
                      )}
                    </td>
                    <td style={{ fontWeight: 600 }}>{after.toFixed(2)}</td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        )}
        {tab === 'bill' && (
          <table style={{ minWidth: 600, borderRadius: 12, overflow: 'hidden', boxShadow: '0 2px 12px rgba(0,0,0,0.04)' }}>
            <thead>
              <tr style={{ background: 'var(--color-surface)' }}>
                <th style={{ textAlign: 'center', color: 'var(--color-text)' }}>Month</th>
                <th style={{ textAlign: 'center', color: 'var(--color-text)' }}>Before</th>
                <th style={{ textAlign: 'center', color: 'var(--color-text)' }}>Savings (EGP)</th>
                <th style={{ textAlign: 'center', color: 'var(--color-text)' }}>After</th>
              </tr>
            </thead>
            <tbody>
              {MONTHS.map((month, i) => {
                const before = results.bills[i] || 0;
                const after = results.newBills[i] || 0;
                const savings = results.savings[i] || 0;
                const rel = billRel[i];
                return (
                  <tr key={month} style={{ background: i % 2 ? 'var(--color-surface)' : 'var(--color-surface)' }}>
                    <td style={{ fontWeight: 600 }}>{month}</td>
                    <td>{before.toFixed(2)}</td>
                    <td style={{ textAlign: 'center' }}>
                      {rel > 0 && (
                        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: 24 }}>
                          <div style={{
                            width: 30,
                            height: 8,
                            background: `linear-gradient(90deg, var(--color-text) ${rel * 100}%, var(--color-surface) ${rel * 100}%)`,
                            borderRadius: 4,
                            marginRight: 6,
                          }} />
                          <span style={{ color: 'var(--color-text)', fontWeight: 700, fontSize: 15, marginRight: 6 }}>{savings.toFixed(2)}</span>
                          <span style={{ color: 'var(--color-text)', fontWeight: 700, fontSize: 18 }}>↓</span>
                        </div>
                      )}
                    </td>
                    <td style={{ fontWeight: 600 }}>{after.toFixed(2)}</td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        )}
        {tab === 'tier' && (
          <table style={{ minWidth: 500, borderRadius: 12, overflow: 'hidden', boxShadow: '0 2px 12px rgba(0,0,0,0.04)' }}>
            <thead>
              <tr style={{ background: 'var(--color-surface)' }}>
                <th style={{ textAlign: 'center', color: 'var(--color-text)' }}>Month</th>
                <th style={{ textAlign: 'center', color: 'var(--color-text)' }}>Tariff Tier Change</th>
              </tr>
            </thead>
            <tbody>
              {MONTHS.map((month, i) => {
                const before = results.billTiers && results.billTiers[i];
                const after = results.newBillTiers && results.newBillTiers[i];
                const changed = before !== after;
                let indicator = null;
                if (changed && before > after) {
                  indicator = <span style={{ color: '#10b981', fontWeight: 700, fontSize: 18 }}>↓</span>;
                } else if (changed && before < after) {
                  indicator = <span style={{ color: '#eab308', fontWeight: 700, fontSize: 18 }}>↑</span>;
                } else {
                  indicator = <span style={{ color: '#888', fontWeight: 700, fontSize: 18 }}>•</span>;
                }
                return (
                  <tr key={month} style={{ background: i % 2 ? 'var(--color-surface)' : 'var(--color-surface)' }}>
                    <td style={{ fontWeight: 600 }}>{month}</td>
                    <td style={{ textAlign: 'center' }}>{indicator}</td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        )}
      </div>
    </div>
  );
}
