import React, { useState } from 'react';
import { FaSolarPanel, FaTimes } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';

export default function PanelParametersPopup({
  onApplyParameters, // New prop for auto-placement
  onClearTables, // New prop to clear existing tables
  panelLength,
  setPanelLength,
  panelWidth,
  setPanelWidth,
  panelTilt,
  setPanelTilt,
  panelOrientation,
  setPanelOrientation,
  panelSpacing,
  setPanelSpacing,
  stackNumber,
  setStackNumber,
  rowSpacing,
  setRowSpacing,
  pvTablesCount = 0 // New prop to show if tables exist
}) {
  const [isOpen, setIsOpen] = useState(false);
  const { t } = useTranslation();
  const [azimuthAngle, setAzimuthAngle] = useState(0); // 0=South, -90=East, +90=West

  const labelStyle = { fontWeight: '600', fontSize: '0.9rem', color: '#4b5563' };
  const inputStyle = {
    padding: '10px',
    borderRadius: '8px',
    border: '1px solid #d1d5db',
    fontSize: '1rem',
    width: '100%',
    boxSizing: 'border-box'
  };

  const openPopup = () => setIsOpen(true);
  const closePopup = () => setIsOpen(false);

  return (
    <div>
      {/* Button to open the popup */}
      <button
        onClick={openPopup}
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '8px',
          padding: '10px 16px',
          background: '#10b981',
          color: 'var(--color-text)',
          border: 'none',
          borderRadius: '8px',
          fontWeight: '600',
          fontSize: '0.95rem',
          cursor: 'pointer',
          transition: 'background 0.2s',
          width: '100%',
          marginTop: '12px',
          boxShadow: '0 2px 4px rgba(16, 185, 129, 0.2)'
        }}
      >
        <FaSolarPanel size={18} />
        <span>{t('Panel Parameters')}</span>
      </button>

      {/* Popup overlay */}
      {isOpen && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1000
        }}>
          {/* Popup content */}
          <div style={{
            background: 'var(--color-surface)',
            borderRadius: '12px',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
            padding: '24px',
            width: '90%',
            maxWidth: '500px',
            maxHeight: '90vh',
            overflowY: 'auto',
            position: 'relative'
          }}>
            {/* Close button */}
            <button
              onClick={closePopup}
              style={{
                position: 'absolute',
                top: '12px',
                right: '12px',
                background: 'none',
                border: 'none',
                fontSize: '24px',
                cursor: 'pointer',
                color: 'var(--color-text)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                padding: '4px'
              }}
            >
              <FaTimes />
            </button>

            {/* Popup header */}
            <h2 style={{
              margin: '0 0 20px 0',
              color: '#10b981',
              fontSize: '1.5rem',
              fontWeight: '700',
              borderBottom: '2px solid var(--color-accent)',
              paddingBottom: '10px'
            }}>
              Panel Parameters
            </h2>

            {/* Panel parameters form - Two Column Layout */}
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '16px'
            }}>
              {/* Row 1: Panel Length and Panel Width */}
              <div style={{ display: 'flex', gap: '12px' }}>
                <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '6px' }}>
                  <label style={labelStyle}>{t('Panel Length (m)')}:</label>
                  <input
                    type="number"
                    min="0.1"
                    step="0.01"
                    value={panelLength}
                    onChange={e => setPanelLength(Number(e.target.value))}
                    style={inputStyle}
                  />
                </div>
                <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '6px' }}>
                  <label style={labelStyle}>{t('Panel Width (m)')}:</label>
                  <input
                    type="number"
                    min="0.1"
                    step="0.01"
                    value={panelWidth}
                    onChange={e => setPanelWidth(Number(e.target.value))}
                    style={inputStyle}
                  />
                </div>
              </div>

              {/* Row 2: Panel Tilt and Azimuth Angle */}
              <div style={{ display: 'flex', gap: '12px' }}>
                <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '6px' }}>
                  <label style={labelStyle}>{t('Panel Tilt (°)')}:</label>
                  <input
                    type="number"
                    min="0"
                    max="90"
                    value={panelTilt}
                    onChange={e => setPanelTilt(Number(e.target.value))}
                    style={inputStyle}
                  />
                </div>
                <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '6px' }}>
                  <label style={labelStyle}>{t('Azimuth Angle (°)')}:</label>
                  <input
                    type="number"
                    min="-180"
                    max="180"
                    step="1"
                    value={azimuthAngle}
                    onChange={e => setAzimuthAngle(Number(e.target.value))}
                    style={inputStyle}
                    title="0° = South, -90° = East, 90° = West, 180° = North"
                  />
                </div>
              </div>

              {/* Row 3: Orientation and Stack Number */}
              <div style={{ display: 'flex', gap: '12px' }}>
                <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '6px' }}>
                  <label style={labelStyle}>{t('Orientation')}:</label>
                  <select
                    value={panelOrientation}
                    onChange={e => setPanelOrientation(e.target.value)}
                    style={inputStyle}
                  >
                    <option value="portrait">{t('Portrait')}</option>
                    <option value="landscape">{t('Landscape')}</option>
                  </select>
                </div>
                <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '6px' }}>
                  <label style={labelStyle}>{t('Stack Number')}:</label>
                  <input
                    type="number"
                    min="1"
                    value={stackNumber}
                    onChange={e => setStackNumber(Number(e.target.value))}
                    style={inputStyle}
                    title="Number of modules stacked in Y direction (perpendicular to azimuth)"
                  />
                </div>
              </div>

              {/* Row 4: Row Spacing and Panels Spacing */}
              <div style={{ display: 'flex', gap: '12px' }}>
                <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '6px' }}>
                  <label style={labelStyle}>{t('Row Spacing (m)')}:</label>
                  <input
                    type="number"
                    min="0.1"
                    step="0.1"
                    value={rowSpacing}
                    onChange={e => setRowSpacing(Number(e.target.value))}
                    style={inputStyle}
                    title="Spacing between rows of PV tables"
                  />
                </div>
                <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '6px' }}>
                  <label style={labelStyle}>{t('Panels Spacing')}:</label>
                  <input
                    type="text"
                    value={panelSpacing}
                    onChange={e => setPanelSpacing(e.target.value)}
                    placeholder="auto"
                    style={inputStyle}
                  />
                </div>
              </div>

              {/* Info text about auto-placement mode */}
              <div style={{
                padding: '12px',
                backgroundColor: '#f0f9ff',
                border: '1px solid #0ea5e9',
                borderRadius: '8px',
                fontSize: '0.9rem',
                color: '#0369a1'
              }}>
                <strong>{t('Auto-Placement Mode')}:</strong> {t('Automatically fills all available non-shaded areas with optimally spaced PV tables.')}
                {pvTablesCount > 0 && (
                  <div style={{ marginTop: '8px', fontWeight: '600', color: '#dc2626' }}>
                    ⚠️ {t('Warning: This will replace all existing')} {pvTablesCount} {t('PV tables on the map.')}
                  </div>
                )}
              </div>
            </div>

            {/* Action Buttons */}
            <div style={{
              display: 'flex',
              justifyContent: 'flex-end',
              gap: '12px',
              marginTop: '24px',
              paddingTop: '16px',
              borderTop: '1px solid #e5e7eb'
            }}>
              <button
                onClick={closePopup}
                style={{
                  padding: '10px 16px',
                  background: '#6b7280', // A neutral color for cancel/close
                  color: 'var(--color-text)',
                  border: 'none',
                  borderRadius: '8px',
                  fontWeight: '600',
                  fontSize: '0.9rem',
                  cursor: 'pointer',
                  transition: 'background 0.2s'
                }}
              >
                {t('Cancel')}
              </button>
              <button
                onClick={() => {
                  if (onApplyParameters) {
                    // Clear existing tables first if any exist
                    if (pvTablesCount > 0 && onClearTables) {
                      onClearTables();
                    }

                    const actualPanelLength = panelOrientation === 'portrait' ? panelLength : panelWidth;
                    const tiltAngleInRadians = panelTilt * (Math.PI / 180);
                    const totalHeight = stackNumber * actualPanelLength * Math.sin(tiltAngleInRadians);

                    onApplyParameters({
                      panelLength,
                      panelWidth,
                      panelTilt,
                      panelOrientation,
                      azimuthAngle,
                      panelSpacing,
                      stackNumber,
                      rowSpacing,
                      placementMode: 'fill', // Always use fill mode
                      totalHeight
                    });
                  }
                  closePopup();
                }}
                style={{
                  padding: '12px 20px',
                  background: '#10b981',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  fontWeight: '600',
                  fontSize: '1rem',
                  cursor: 'pointer',
                  transition: 'background 0.2s',
                  width: '100%'
                }}
              >
                {t('Auto Place')}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
