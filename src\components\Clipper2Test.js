/**
 * Clipper2 Test Component
 * 
 * Simple component to test Clipper2 WASM initialization and basic operations
 */

import React, { useState, useEffect } from 'react';
import { testClipper2Operations } from '../utils/clipper2Operations';
import { safeInitializeClipper2 } from '../utils/wasmLoader';

const Clipper2Test = () => {
  const [initStatus, setInitStatus] = useState('Not initialized');
  const [testStatus, setTestStatus] = useState('Not tested');
  const [isLoading, setIsLoading] = useState(false);

  const handleInitialize = async () => {
    setIsLoading(true);
    setInitStatus('Initializing...');
    
    try {
      const result = await safeInitializeClipper2();
      
      if (result.success) {
        setInitStatus('✅ Initialized successfully');
      } else {
        setInitStatus(`❌ Initialization failed: ${result.error.message}`);
      }
    } catch (error) {
      setInitStatus(`❌ Initialization error: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRunTests = async () => {
    setIsLoading(true);
    setTestStatus('Running tests...');
    
    try {
      const success = await testClipper2Operations();
      
      if (success) {
        setTestStatus('✅ All tests passed');
      } else {
        setTestStatus('❌ Tests failed');
      }
    } catch (error) {
      setTestStatus(`❌ Test error: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div style={{ 
      padding: '20px', 
      border: '1px solid #ccc', 
      borderRadius: '8px', 
      margin: '20px',
      backgroundColor: '#f9f9f9'
    }}>
      <h3>Clipper2 WASM Test</h3>
      
      <div style={{ marginBottom: '15px' }}>
        <strong>Initialization Status:</strong> {initStatus}
      </div>
      
      <div style={{ marginBottom: '15px' }}>
        <strong>Test Status:</strong> {testStatus}
      </div>
      
      <div style={{ display: 'flex', gap: '10px' }}>
        <button 
          onClick={handleInitialize}
          disabled={isLoading}
          style={{
            padding: '8px 16px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: isLoading ? 'not-allowed' : 'pointer'
          }}
        >
          {isLoading ? 'Loading...' : 'Initialize Clipper2'}
        </button>
        
        <button 
          onClick={handleRunTests}
          disabled={isLoading}
          style={{
            padding: '8px 16px',
            backgroundColor: '#28a745',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: isLoading ? 'not-allowed' : 'pointer'
          }}
        >
          {isLoading ? 'Loading...' : 'Run Tests'}
        </button>
      </div>
      
      <div style={{ marginTop: '15px', fontSize: '12px', color: '#666' }}>
        <p>This component tests the Clipper2 WASM integration:</p>
        <ul>
          <li>Initialize: Loads and initializes the Clipper2 WASM module</li>
          <li>Run Tests: Performs basic union, intersection, and difference operations</li>
        </ul>
      </div>
    </div>
  );
};

export default Clipper2Test;
