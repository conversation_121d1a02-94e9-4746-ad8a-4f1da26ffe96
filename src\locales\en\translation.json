{"Solar PV Savings Calculator": "Solar PV Savings Calculator", "Toggle Light / Dark": "Toggle Light / Dark", "Toggle Language": "Toggle Language", "Calculate Solar Savings": "Calculate Solar Savings", "Results": "Results", "Financial Summary": "Financial Summary", "Carbon Savings": "Carbon Savings", "Calculating...": "Calculating...", "Home": "Home", "Financial Parameters": "Financial Parameters", "Payback Period": "Payback Period", "IRR": "IRR", "NPV": "NPV", "ROI": "ROI", "LCOE": "LCOE", "Financial Details": "Financial Details", "Carbon Emission Factors": "Carbon Emission Factors", "Total Carbon Savings": "Total Carbon Savings", "Equivalent Trees Planted": "Equivalent Trees Planted", "Fuel Saved": "Fuel Saved", "Carbon Savings Details": "Carbon Savings Details", "Year": "Year", "Yearly Carbon Savings (tCO₂)": "Yearly Carbon Savings (tCO₂)", "Cumulative Carbon Savings (tCO₂)": "Cumulative Carbon Savings (tCO₂)", "Cumulative Carbon Savings": "Cumulative Carbon Savings", "Panel Parameters": "Panel Parameters", "Panel Length (m)": "Panel Length (m)", "Panel Width (m)": "Panel Width (m)", "Panel Tilt (°)": "Panel Tilt (°)", "Orientation": "Orientation", "Portrait": "Portrait", "Landscape": "Landscape", "Azimuth Angle (°)": "<PERSON><PERSON><PERSON><PERSON> (°)", "Panels Spacing": "Panels Spacing", "Stack Number": "Stack Number", "Placement Mode": "Placement Mode", "Fill Available Area": "Fill Available Area", "Fixed Number": "Fixed Number", "Number of Panels": "Number of Panels", "Cancel": "Cancel", "Save & Auto-place Panels": "Save & Auto-place Panels", "Save Parameters": "Save Parameters", "Grid Emission Factor (kgCO₂/kWh)": "Grid Emission Factor (kgCO₂/kWh)", "LCE PV (kgCO₂/kWp)": "LCE PV (kgCO₂/kWp)", "LCE Inverter (kgCO₂/unit)": "LCE Inverter (kgCO₂/unit)", "LCE Mount (kgCO₂/kWp)": "LCE Mount (kgCO₂/kWp)", "System Parameters": "System Parameters", "System Peak Power (kWp)": "System Peak Power (kWp)", "System Losses (%)": "System Losses (%)", "Panel Azimuth (°)": "Panel Azimuth (°)", "Calculate optimal system size based on your bill": "Calculate optimal system size based on your bill", "Energy Consumption & Billing": "Energy Consumption & Billing", "Input Method": "Input Method", "Variable Monthly Bills": "Variable Monthly Bills", "Variable Monthly Consumption": "Variable Monthly Consumption", "Consumption Type": "Consumption Type", "Residential": "Residential", "Commercial": "Commercial", "Low Voltage Use": "Low Voltage Use", "Medium Voltage Use": "Medium Voltage Use", "Irrigation (Low Voltage)": "Irrigation (Low Voltage)", "Custom Tariff Rate (EGP per kWh)": "Custom Tariff Rate (EGP per kWh)", "Average Monthly Bill (EGP)": "Average Monthly Bill (EGP)", "Average Monthly Consumption (kWh)": "Average Monthly Consumption (kWh)", "Monthly Bills (EGP)": "Monthly Bills (EGP)", "Monthly Consumption (kWh)": "Monthly Consumption (kWh)", "Custom Tariff": "Custom Tariff", "Average Monthly Bill": "Average Monthly Bill", "Average Monthly Consumption": "Average Monthly Consumption", "January": "January", "February": "February", "March": "March", "April": "April", "May": "May", "June": "June", "July": "July", "August": "August", "September": "September", "October": "October", "November": "November", "December": "December", "Usable Roof Area": "Usable Roof Area", "Date": "Date", "Duration (hours)": "Duration (hours)", "Analyze Shadows": "Analyze Shadows", "Hide Shadows": "Hide Shadows", "Show Shadows": "Show Shadows", "Obstacles": "Obstacles", "Heights": "Heights", "set": "set", "Total Main Roof Area": "Total Main Roof Area", "Total Shaded Area on Roof": "Total Shaded Area on Roof", "Non-Shaded Roof Area": "Non-Shaded Roof Area", "Selected Location": "Selected Location", "GPS will auto-locate if allowed and show a marker.": "GPS will auto-locate if allowed and show a marker.", "Click map to select location or clear all.": "Click map to select location or clear all.", "Draw polygons to measure area or lines to measure distance. Edit or delete as needed.": "Draw polygons to measure area or lines to measure distance. Edit or delete as needed.", "Energy": "Energy", "Bills": "Bills", "Tariffs": "Tariffs", "Summary": "Summary", "Annual Bill without Solar": "Annual Bill without Solar", "Annual Bill with Solar": "Annual Bill with Solar", "Annual Savings": "Annual Savings", "Savings Percentage": "Savings Percentage", "Annual Energy Need without Solar": "Annual Energy Need without Solar", "Annual Energy Need with Solar": "Annual Energy Need with Solar", "Annual PV Production": "Annual PV Production", "Energy Savings": "Energy Savings", "Average Tariff Before": "Average Tariff Before", "Average Tariff After": "Average Tariff After", "Average Tariff Decrease": "Average Tariff Decrease", "Tariff Reduction": "Tariff Reduction", "Bill Without Solar": "<PERSON>", "Bill With Solar": "<PERSON>", "Energy Need": "Energy Need", "PV Production": "PV Production"}