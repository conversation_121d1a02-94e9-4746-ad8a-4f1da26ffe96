// PVGIS API utility
// Returns: { E_m: [monthly production], ... }
async function fetchWithRetry(url, options = {}, retries = 3) {
  let lastErr;
  for (let i = 0; i < retries; i++) {
    try {
      const res = await fetch(url, options);
      if (!res.ok) {
        // Retry on 408 or network errors
        if (res.status === 408) throw new Error('HTTP 408');
        return res;
      }
      return res;
    } catch (err) {
      lastErr = err;
      if (i < retries - 1) await new Promise(r => setTimeout(r, 1000 * (i + 1)));
    }
  }
  throw lastErr || new Error('Failed to fetch after retries');
}

export async function fetchPVGIS({ lat, lng, peakPower, losses, tilt, azimuth }) {
  // Use PVGIS-SARAH3 for v5_3 endpoint
  const raddatabase = 'PVGIS-SARAH3';
  console.log('Using raddatabase:', raddatabase); // Debug log
  const apiUrl = `https://re.jrc.ec.europa.eu/api/v5_3/PVcalc?lat=${lat}&lon=${lng}&peakpower=${peakPower}&loss=${losses}&angle=${tilt}&aspect=${azimuth}&outputformat=json&raddatabase=${raddatabase}`;
  // Use corsproxy.io as CORS proxy
  const url = `https://corsproxy.io/?${apiUrl}`;
  let res;
  try {
    res = await fetchWithRetry(url);
  } catch (err) {
    throw new Error('PVGIS API network error: ' + err.message);
  }
  if (!res.ok) {
    const text = await res.text();
    throw new Error('PVGIS API error: ' + text);
  }
  let data;
  try {
    data = await res.json();
  } catch (err) {
    throw new Error('PVGIS API invalid JSON: ' + err.message);
  }
  if (data.outputs && data.outputs.E_m) {
    return data.outputs;
  }
  if (data.outputs && data.outputs.monthly) {
    let monthly = data.outputs.monthly;
    if (Array.isArray(monthly)) {
      return { E_m: monthly.map(m => m.E_m) };
    } else if (monthly.fixed) {
      return { E_m: monthly.fixed.map(m => m.E_m) };
    }
  }
  throw new Error('Unexpected PVGIS API response: ' + JSON.stringify(data));
}
