// Main calculation logic for solar savings
import { fetchPVGIS } from './pvgis';
import { TARIFF_STRUCTURES, billToConsumption, calculateBill } from './tariffs';

export async function calculateSolarSavings({ location, params, billing }) {
  // 1. Prepare consumption array
  let yearlyConsumptions = [];
  const structure = billing.consumptionType === 'custom_tariff'
    ? { consumption_ranges_category: [1], consumption_ranges: [100000000], tariffs: [parseFloat(billing.customTariff) || 2.5] }
    : TARIFF_STRUCTURES[billing.consumptionType];

  if (billing.mode === 'average-consumption') {
    const avg = parseFloat(billing.fixedValue) || 0;
    yearlyConsumptions = Array(12).fill(avg);
  } else if (billing.mode === 'average-bill') {
    const avgBill = parseFloat(billing.fixedValue) || 0;
    const consumption = billToConsumption(avgBill, structure);
    yearlyConsumptions = Array(12).fill(consumption);
  } else if (billing.mode === 'variable-consumption' || billing.mode === 'variable-bill') {
    for (let i = 0; i < 12; i++) {
      let v = parseFloat(billing.monthlyValues[i]) || 0;
      if (billing.mode === 'variable-bill') {
        v = billToConsumption(v, structure);
      }
      yearlyConsumptions.push(v);
    }
  }

  // 2. Call PVGIS for monthly production
  const outputs = await fetchPVGIS({
    lat: location.lat,
    lng: location.lng,
    peakPower: params.peakPower,
    losses: params.losses,
    tilt: params.tilt,
    azimuth: params.azimuth,
    raddatabase: 'PVGIS-SARAH3', // Always use a valid value
  });
  const monthlyProduction = outputs.E_m || Array(12).fill(0);

  // 3. Calculate bills and savings
  const bills = [];
  const billTiers = [];
  const tariffRatesBefore = [];
  for (const consumption of yearlyConsumptions) {
    const [bill, tier] = calculateBill(structure, consumption);
    bills.push(bill);
    billTiers.push(tier);
    tariffRatesBefore.push(structure.tariffs[tier - 1] ?? null);
  }

  const newConsumptions = yearlyConsumptions.map((c, i) => Math.max(0, c - (monthlyProduction[i] || 0)));
  const newBills = [];
  const newBillTiers = [];
  const tariffRatesAfter = [];
  for (const consumption of newConsumptions) {
    const [bill, tier] = calculateBill(structure, consumption);
    newBills.push(bill);
    newBillTiers.push(tier);
    tariffRatesAfter.push(structure.tariffs[tier - 1] ?? null);
  }
  const savings = bills.map((b, i) => b - newBills[i]);
  const totalBill = bills.reduce((a, b) => a + b, 0);
  const totalNewBill = newBills.reduce((a, b) => a + b, 0);
  const savingsPercentage = totalBill > 0 ? ((totalBill - totalNewBill) / totalBill) * 100 : 0;

  return {
    yearlyConsumptions,
    monthlyProduction,
    newConsumptions,
    bills,
    newBills,
    savings,
    totalBill,
    totalNewBill,
    savingsPercentage,
    billTiers,
    newBillTiers,
    tariffRatesBefore,
    tariffRatesAfter,
  };
}
