import React, { memo } from 'react';
import { Polygon as RLPolygon } from 'react-leaflet';

/**
 * Optimized PV Table Panel Component
 * Renders individual PV panel rectangles with performance optimizations
 */
const PVTablePanel = memo(({
  rectangle,
  tableId,
  panelIndex,
  isSelected,
  isDragging,
  eventHandlers,
  style
}) => {
  // Generate stable key for React reconciliation
  const panelKey = `${tableId}-panel-${panelIndex}`;
  
  // Optimize polygon style based on state
  const polygonStyle = {
    color: style?.color || '#ff7800',
    weight: style?.weight || 2,
    opacity: style?.opacity || 1,
    fillColor: style?.fillColor || '#ff7800',
    fillOpacity: style?.fillOpacity || 0.2,
    // Performance optimization: disable animations during drag
    className: isDragging ? 'no-transition' : '',
    ...style
  };

  return (
    <RLPolygon
      key={panelKey}
      positions={rectangle}
      pane="obstaclePane"
      pathOptions={{
        ...polygonStyle,
        className: 'pv-table-panel' // Add CSS class for styling
      }}
      eventHandlers={{
        ...eventHandlers,
        mouseover: (e) => {
          // Change cursor to indicate draggable
          if (e.target && e.target._path) {
            e.target._path.style.cursor = 'grab';
          }
          eventHandlers.mouseenter?.(e);
        },
        mouseout: (e) => {
          // Reset cursor
          if (e.target && e.target._path) {
            e.target._path.style.cursor = 'default';
          }
          eventHandlers.mouseleave?.(e);
        },
        touchstart: (e) => {
          // Handle touch start for mobile devices
          if (e.originalEvent) {
            e.originalEvent.preventDefault();
            eventHandlers.mousedown?.(e);
          }
        }
      }}
    />
  );
}, (prevProps, nextProps) => {
  // Custom comparison function for optimal re-rendering
  // Only re-render if these specific props change

  // Quick checks first (most likely to change)
  if (
    prevProps.tableId !== nextProps.tableId ||
    prevProps.panelIndex !== nextProps.panelIndex ||
    prevProps.isSelected !== nextProps.isSelected ||
    prevProps.isDragging !== nextProps.isDragging
  ) {
    return false;
  }

  // Deep comparison for rectangle coordinates (important for rotation)
  const prevRectStr = JSON.stringify(prevProps.rectangle);
  const nextRectStr = JSON.stringify(nextProps.rectangle);
  if (prevRectStr !== nextRectStr) {
    return false;
  }

  // Style comparison
  const prevStyleStr = JSON.stringify(prevProps.style);
  const nextStyleStr = JSON.stringify(nextProps.style);
  if (prevStyleStr !== nextStyleStr) {
    return false;
  }

  // If all checks pass, don't re-render
  return true;
});

PVTablePanel.displayName = 'PVTablePanel';

export default PVTablePanel;
