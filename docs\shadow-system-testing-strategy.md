# Shadow System Testing Strategy

## Overview

This document outlines a comprehensive testing strategy for the shadow calculation system replacement. The testing approach covers unit tests, integration tests, performance benchmarks, and visual validation to ensure the new system meets reliability and performance requirements.

## Testing Framework Setup

### Dependencies

```bash
# Core testing framework
npm install --save-dev jest @testing-library/react @testing-library/jest-dom

# Geometry testing utilities
npm install --save-dev @turf/helpers @turf/boolean-equal

# Performance testing
npm install --save-dev benchmark

# Visual regression testing
npm install --save-dev puppeteer jest-image-snapshot
```

### Test Configuration

Create `jest.config.js`:

```javascript
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.js'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.{js,jsx}',
    '<rootDir>/src/**/*.{test,spec}.{js,jsx}'
  ],
  collectCoverageFrom: [
    'src/utils/geometryOperations.js',
    'src/components/OpenLayersMap.js',
    'src/hooks/useShadowWorker.js'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
};
```

## Unit Testing

### Geometry Operations Tests

Create `src/utils/__tests__/geometryOperations.test.js`:

```javascript
import {
  unionPolygons,
  intersectPolygons,
  differencePolygons,
  validatePolygon,
  calculatePolygonArea,
  GeometryOperationError
} from '../geometryOperations';

describe('Geometry Operations', () => {
  // Test data
  const validSquare = [[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]];
  const overlappingSquare = [[0.5, 0], [1.5, 0], [1.5, 1], [0.5, 1], [0.5, 0]];
  const separateSquare = [[2, 2], [3, 2], [3, 3], [2, 3], [2, 2]];
  const invalidPolygon = [[0, 0], [1, 0]]; // Less than 3 points

  describe('validatePolygon', () => {
    test('should validate correct polygon', () => {
      expect(validatePolygon(validSquare)).toBe(true);
    });

    test('should reject invalid polygon', () => {
      expect(validatePolygon(invalidPolygon)).toBe(false);
      expect(validatePolygon(null)).toBe(false);
      expect(validatePolygon([])).toBe(false);
    });

    test('should reject polygon with invalid coordinates', () => {
      const invalidCoords = [[0, 0], [1, NaN], [1, 1], [0, 1], [0, 0]];
      expect(validatePolygon(invalidCoords)).toBe(false);
    });
  });

  describe('unionPolygons', () => {
    test('should merge overlapping polygons', () => {
      const result = unionPolygons([validSquare, overlappingSquare]);
      
      expect(result).toHaveLength(1);
      expect(validatePolygon(result[0])).toBe(true);
      
      // Area should be 1.5 (1 + 1 - 0.5 overlap)
      const area = calculatePolygonArea(result[0]);
      expect(area).toBeCloseTo(1.5, 2);
    });

    test('should preserve separate polygons', () => {
      const result = unionPolygons([validSquare, separateSquare]);
      
      expect(result).toHaveLength(2);
      result.forEach(polygon => {
        expect(validatePolygon(polygon)).toBe(true);
      });
    });

    test('should handle single polygon', () => {
      const result = unionPolygons([validSquare]);
      
      expect(result).toHaveLength(1);
      expect(result[0]).toEqual(validSquare);
    });

    test('should throw error for invalid input', () => {
      expect(() => unionPolygons([invalidPolygon])).toThrow(GeometryOperationError);
    });
  });

  describe('intersectPolygons', () => {
    test('should find intersection of overlapping polygons', () => {
      const result = intersectPolygons(validSquare, overlappingSquare);
      
      expect(result).toHaveLength(1);
      expect(validatePolygon(result[0])).toBe(true);
      
      // Intersection area should be 0.5
      const area = calculatePolygonArea(result[0]);
      expect(area).toBeCloseTo(0.5, 2);
    });

    test('should return empty for non-overlapping polygons', () => {
      const result = intersectPolygons(validSquare, separateSquare);
      expect(result).toHaveLength(0);
    });
  });

  describe('calculatePolygonArea', () => {
    test('should calculate area correctly', () => {
      const area = calculatePolygonArea(validSquare);
      expect(area).toBeCloseTo(1.0, 2);
    });

    test('should throw error for invalid polygon', () => {
      expect(() => calculatePolygonArea(invalidPolygon)).toThrow(GeometryOperationError);
    });
  });
});
```

### Shadow Merging Tests

Create `src/components/__tests__/shadowMerging.test.js`:

```javascript
import { mergeShadowPolygons, smartMergeShadowPolygons } from '../OpenLayersMap';

describe('Shadow Merging', () => {
  const createTestShadow = (id, coordinates) => ({
    id,
    coordinates,
    type: 'individual'
  });

  const overlappingShadows = [
    createTestShadow('shadow1', [[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]),
    createTestShadow('shadow2', [[0.5, 0], [1.5, 0], [1.5, 1], [0.5, 1], [0.5, 0]])
  ];

  const separateShadows = [
    createTestShadow('shadow1', [[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]),
    createTestShadow('shadow2', [[2, 2], [3, 2], [3, 3], [2, 3], [2, 2]])
  ];

  describe('mergeShadowPolygons', () => {
    test('should merge overlapping shadows', () => {
      const result = mergeShadowPolygons(overlappingShadows);
      
      expect(result).toHaveLength(1);
      expect(result[0].type).toBe('aggregated');
      expect(result[0].id).toMatch(/merged_shadow_/);
    });

    test('should preserve separate shadows', () => {
      const result = mergeShadowPolygons(separateShadows);
      
      expect(result).toHaveLength(2);
      result.forEach(shadow => {
        expect(shadow.type).toBe('aggregated');
      });
    });

    test('should handle empty input', () => {
      expect(mergeShadowPolygons([])).toEqual([]);
      expect(mergeShadowPolygons(null)).toEqual([]);
    });

    test('should handle single shadow', () => {
      const singleShadow = [overlappingShadows[0]];
      const result = mergeShadowPolygons(singleShadow);
      
      expect(result).toEqual(singleShadow);
    });

    test('should fallback on error', () => {
      const invalidShadows = [
        createTestShadow('invalid', [[0, 0], [1, NaN]]) // Invalid coordinates
      ];
      
      const result = mergeShadowPolygons(invalidShadows);
      expect(result).toEqual([]); // Should filter out invalid shadows
    });
  });

  describe('smartMergeShadowPolygons', () => {
    test('should group nearby shadows', () => {
      const nearbyShadows = [
        createTestShadow('shadow1', [[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]),
        createTestShadow('shadow2', [[1.0001, 0], [2, 0], [2, 1], [1.0001, 1], [1.0001, 0]]),
        createTestShadow('shadow3', [[10, 10], [11, 10], [11, 11], [10, 11], [10, 10]])
      ];
      
      const result = smartMergeShadowPolygons(nearbyShadows, 0.001);
      
      // Should merge first two shadows, keep third separate
      expect(result).toHaveLength(2);
    });
  });
});
```

## Integration Testing

### Shadow Calculation Integration Tests

Create `src/components/__tests__/shadowCalculation.integration.test.js`:

```javascript
import { render, waitFor } from '@testing-library/react';
import OpenLayersMap from '../OpenLayersMap';
import { createMockPVTables, createMockObstacles } from '../../__mocks__/testData';

describe('Shadow Calculation Integration', () => {
  const mockProps = {
    location: { lat: 40.7128, lng: -74.0060 },
    analysisDate: '2024-06-21',
    analysisDurationHours: 8,
    obstacles: createMockObstacles(),
    pvTables: createMockPVTables(),
    showShadows: true
  };

  test('should calculate shadows for multiple PV tables', async () => {
    const { container } = render(<OpenLayersMap {...mockProps} />);
    
    // Wait for shadow calculation to complete
    await waitFor(() => {
      const shadowElements = container.querySelectorAll('[data-testid="shadow-polygon"]');
      expect(shadowElements.length).toBeGreaterThan(0);
    }, { timeout: 5000 });
  });

  test('should handle building orientation changes', async () => {
    const orientedProps = {
      ...mockProps,
      pvTables: mockProps.pvTables.map(table => ({
        ...table,
        properties: { ...table.properties, azimuth: 45 }
      }))
    };
    
    const { container } = render(<OpenLayersMap {...orientedProps} />);
    
    await waitFor(() => {
      const shadowElements = container.querySelectorAll('[data-testid="shadow-polygon"]');
      expect(shadowElements.length).toBeGreaterThan(0);
    });
  });

  test('should merge overlapping shadows correctly', async () => {
    const overlappingProps = {
      ...mockProps,
      pvTables: createOverlappingPVTables() // Helper function
    };
    
    const { container } = render(<OpenLayersMap {...overlappingProps} />);
    
    await waitFor(() => {
      const shadowElements = container.querySelectorAll('[data-testid="shadow-polygon"]');
      // Should have fewer shadows than PV tables due to merging
      expect(shadowElements.length).toBeLessThan(overlappingProps.pvTables.length);
    });
  });
});
```

## Performance Testing

### Benchmark Suite

Create `src/__tests__/performance/shadowPerformance.test.js`:

```javascript
import Benchmark from 'benchmark';
import { mergeShadowPolygons } from '../../components/OpenLayersMap';
import { generateTestShadows } from '../../__mocks__/testData';

describe('Shadow Performance Benchmarks', () => {
  const shadowCounts = [10, 50, 100, 500];
  
  shadowCounts.forEach(count => {
    test(`should merge ${count} shadows within performance target`, async () => {
      const shadows = generateTestShadows(count);
      
      const startTime = performance.now();
      const result = await mergeShadowPolygons(shadows);
      const endTime = performance.now();
      
      const duration = endTime - startTime;
      
      // Performance targets (in milliseconds)
      const targets = { 10: 50, 50: 200, 100: 500, 500: 2000 };
      
      expect(duration).toBeLessThan(targets[count]);
      expect(result.length).toBeLessThanOrEqual(count);
      
      console.log(`${count} shadows merged in ${duration.toFixed(2)}ms`);
    });
  });

  test('memory usage should remain stable', async () => {
    const initialMemory = getMemoryUsage();
    
    // Process multiple shadow sets
    for (let i = 0; i < 10; i++) {
      const shadows = generateTestShadows(100);
      await mergeShadowPolygons(shadows);
    }
    
    // Force garbage collection if available
    if (global.gc) global.gc();
    
    const finalMemory = getMemoryUsage();
    const memoryIncrease = finalMemory - initialMemory;
    
    // Memory increase should be less than 50MB
    expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
  });
});

// Utility function for memory monitoring
const getMemoryUsage = () => {
  if (typeof window !== 'undefined' && window.performance && window.performance.memory) {
    return window.performance.memory.usedJSHeapSize;
  }
  return 0;
};
```

### Load Testing

Create `src/__tests__/performance/loadTest.js`:

```javascript
import { calculateAndDisplayShadows } from '../../components/OpenLayersMap';
import { generateLargeDataset } from '../../__mocks__/testData';

describe('Shadow System Load Testing', () => {
  test('should handle large number of PV tables', async () => {
    const largeDataset = generateLargeDataset(1000); // 1000 PV tables
    
    const startTime = Date.now();
    
    try {
      await calculateAndDisplayShadows(largeDataset.obstacles);
      const endTime = Date.now();
      
      const duration = endTime - startTime;
      
      // Should complete within 10 seconds for 1000 PV tables
      expect(duration).toBeLessThan(10000);
      
    } catch (error) {
      fail(`Load test failed: ${error.message}`);
    }
  });

  test('should handle concurrent shadow calculations', async () => {
    const datasets = Array.from({ length: 5 }, () => generateLargeDataset(200));
    
    const promises = datasets.map(dataset => 
      calculateAndDisplayShadows(dataset.obstacles)
    );
    
    const startTime = Date.now();
    await Promise.all(promises);
    const endTime = Date.now();
    
    const duration = endTime - startTime;
    
    // Concurrent calculations should complete within reasonable time
    expect(duration).toBeLessThan(15000);
  });
});
```

## Visual Regression Testing

### Visual Test Setup

Create `src/__tests__/visual/shadowVisual.test.js`:

```javascript
import puppeteer from 'puppeteer';
import { toMatchImageSnapshot } from 'jest-image-snapshot';

expect.extend({ toMatchImageSnapshot });

describe('Shadow Visual Regression Tests', () => {
  let browser;
  let page;

  beforeAll(async () => {
    browser = await puppeteer.launch();
    page = await browser.newPage();
    await page.setViewport({ width: 1200, height: 800 });
  });

  afterAll(async () => {
    await browser.close();
  });

  test('should render shadows correctly for basic scenario', async () => {
    await page.goto('http://localhost:3000/test-scenarios/basic-shadows');
    
    // Wait for shadows to render
    await page.waitForSelector('[data-testid="shadow-polygon"]');
    
    const screenshot = await page.screenshot();
    
    expect(screenshot).toMatchImageSnapshot({
      threshold: 0.1,
      customSnapshotIdentifier: 'basic-shadows'
    });
  });

  test('should render merged shadows correctly', async () => {
    await page.goto('http://localhost:3000/test-scenarios/merged-shadows');
    
    await page.waitForSelector('[data-testid="shadow-polygon"]');
    
    const screenshot = await page.screenshot();
    
    expect(screenshot).toMatchImageSnapshot({
      threshold: 0.1,
      customSnapshotIdentifier: 'merged-shadows'
    });
  });

  test('should handle building orientation correctly', async () => {
    await page.goto('http://localhost:3000/test-scenarios/oriented-building');
    
    await page.waitForSelector('[data-testid="shadow-polygon"]');
    
    const screenshot = await page.screenshot();
    
    expect(screenshot).toMatchImageSnapshot({
      threshold: 0.1,
      customSnapshotIdentifier: 'oriented-building-shadows'
    });
  });
});
```

## Error Handling Tests

### Error Scenario Testing

Create `src/__tests__/errorHandling/shadowErrors.test.js`:

```javascript
import { 
  mergeShadowPolygons,
  calculateAndDisplayShadows 
} from '../../components/OpenLayersMap';
import { GeometryOperationError } from '../../utils/geometryOperations';

describe('Shadow Error Handling', () => {
  test('should handle invalid polygon data gracefully', async () => {
    const invalidShadows = [
      { id: 'invalid1', coordinates: null },
      { id: 'invalid2', coordinates: [[0, 0], [1, NaN]] },
      { id: 'valid', coordinates: [[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]] }
    ];
    
    const result = await mergeShadowPolygons(invalidShadows);
    
    // Should filter out invalid shadows and process valid ones
    expect(result).toHaveLength(1);
    expect(result[0].id).toMatch(/merged_shadow_/);
  });

  test('should handle geometry operation failures', async () => {
    // Mock geometry operation to throw error
    jest.mock('../../utils/geometryOperations', () => ({
      ...jest.requireActual('../../utils/geometryOperations'),
      unionPolygons: jest.fn().mockImplementation(() => {
        throw new GeometryOperationError('Mock geometry error');
      })
    }));
    
    const shadows = [
      { id: 'shadow1', coordinates: [[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]] }
    ];
    
    const result = await mergeShadowPolygons(shadows);
    
    // Should fallback to original shadows
    expect(result).toEqual(shadows);
  });

  test('should handle coordinate transformation errors', async () => {
    const invalidLocation = { lat: NaN, lng: -74.0060 };
    
    await expect(
      calculateAndDisplayShadows([], invalidLocation)
    ).rejects.toThrow('Invalid location coordinates');
  });
});
```

## Test Data Generation

### Mock Data Utilities

Create `src/__mocks__/testData.js`:

```javascript
/**
 * Test data generation utilities for shadow system testing
 */

export const generateTestShadows = (count) => {
  const shadows = [];
  
  for (let i = 0; i < count; i++) {
    const baseX = (i % 10) * 2;
    const baseY = Math.floor(i / 10) * 2;
    
    shadows.push({
      id: `test_shadow_${i}`,
      coordinates: [
        [baseX, baseY],
        [baseX + 1, baseY],
        [baseX + 1, baseY + 1],
        [baseX, baseY + 1],
        [baseX, baseY]
      ],
      type: 'individual'
    });
  }
  
  return shadows;
};

export const createMockPVTables = () => [
  {
    id: 'table1',
    properties: {
      azimuth: 0,
      tilt: 30,
      modulesX: 4,
      modulesY: 6,
      height: 2.5
    },
    coordinates: [[40.7128, -74.0060], [40.7129, -74.0060], [40.7129, -74.0059], [40.7128, -74.0059]]
  },
  {
    id: 'table2',
    properties: {
      azimuth: 45,
      tilt: 30,
      modulesX: 3,
      modulesY: 5,
      height: 2.5
    },
    coordinates: [[40.7130, -74.0058], [40.7131, -74.0058], [40.7131, -74.0057], [40.7130, -74.0057]]
  }
];

export const createMockObstacles = () => [
  {
    id: 'obstacle1',
    height: 5,
    layer: {
      toGeoJSON: () => ({
        geometry: {
          type: 'Polygon',
          coordinates: [[[40.7125, -74.0062], [40.7126, -74.0062], [40.7126, -74.0061], [40.7125, -74.0061]]]
        }
      })
    }
  }
];

export const generateLargeDataset = (pvTableCount) => ({
  pvTables: Array.from({ length: pvTableCount }, (_, i) => ({
    id: `large_table_${i}`,
    properties: {
      azimuth: (i * 15) % 360,
      tilt: 30,
      modulesX: 4,
      modulesY: 6,
      height: 2.5
    },
    coordinates: generateRandomCoordinates()
  })),
  obstacles: Array.from({ length: Math.floor(pvTableCount / 10) }, (_, i) => ({
    id: `large_obstacle_${i}`,
    height: 3 + Math.random() * 5,
    layer: {
      toGeoJSON: () => ({
        geometry: {
          type: 'Polygon',
          coordinates: [generateRandomCoordinates()]
        }
      })
    }
  }))
});

const generateRandomCoordinates = () => {
  const baseLat = 40.7128 + (Math.random() - 0.5) * 0.01;
  const baseLng = -74.0060 + (Math.random() - 0.5) * 0.01;
  
  return [
    [baseLat, baseLng],
    [baseLat + 0.0001, baseLng],
    [baseLat + 0.0001, baseLng + 0.0001],
    [baseLat, baseLng + 0.0001],
    [baseLat, baseLng]
  ];
};
```

## Continuous Integration

### CI Configuration

Create `.github/workflows/shadow-tests.yml`:

```yaml
name: Shadow System Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run unit tests
      run: npm run test:unit
    
    - name: Run integration tests
      run: npm run test:integration
    
    - name: Run performance tests
      run: npm run test:performance
    
    - name: Generate coverage report
      run: npm run test:coverage
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v1
```

## Test Execution Commands

Add to `package.json`:

```json
{
  "scripts": {
    "test": "jest",
    "test:unit": "jest --testPathPattern=__tests__/.*\\.test\\.js$",
    "test:integration": "jest --testPathPattern=integration",
    "test:performance": "jest --testPathPattern=performance",
    "test:visual": "jest --testPathPattern=visual",
    "test:coverage": "jest --coverage",
    "test:watch": "jest --watch"
  }
}
```

This comprehensive testing strategy ensures the shadow calculation system replacement is thoroughly validated across all aspects: functionality, performance, visual accuracy, and error handling.
