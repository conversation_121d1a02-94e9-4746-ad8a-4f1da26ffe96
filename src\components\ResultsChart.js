import React from 'react';
import { Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
  Legend,
  Tooltip,
} from 'chart.js';

ChartJS.register(CategoryScale, LinearScale, BarElement, PointElement, LineElement, Legend, Tooltip);

const MONTHS = [
  'January', 'February', 'March', 'April', 'May', 'June',
  'July', 'August', 'September', 'October', 'November', 'December'
];

export default function ResultsChart({ results }) {
  if (!results) return null;
  const data = {
    labels: MONTHS,
    datasets: [
      {
        label: 'Bill Without Solar (EGP)',
        data: results.bills,
        backgroundColor: 'rgba(255, 99, 132, 0.7)',
        borderColor: 'rgb(255, 99, 132)',
        borderWidth: 1,
        yAxisID: 'y',
      },
      {
        label: 'Bill With Solar (EGP)',
        data: results.newBills,
        backgroundColor: 'rgba(75, 192, 192, 0.7)',
        borderColor: 'rgb(75, 192, 192)',
        borderWidth: 1,
        yAxisID: 'y',
      },
      {
        label: 'Solar Production (kWh)',
        data: results.monthlyProduction,
        backgroundColor: 'rgba(255, 206, 86, 0.7)',
        borderColor: 'rgb(255, 206, 86)',
        borderWidth: 2,
        type: 'line',
        yAxisID: 'y1',
        pointRadius: 3,
        tension: 0.2,
      },
    ],
  };
  const options = {
    responsive: true,
    plugins: {
      legend: { position: 'top' },
      tooltip: { mode: 'index', intersect: false },
    },
    scales: {
      y: {
        beginAtZero: true,
        title: { display: true, text: 'Bill Amount (EGP)' },
      },
      y1: {
        beginAtZero: true,
        position: 'right',
        grid: { drawOnChartArea: false },
        title: { display: true, text: 'Energy (kWh)' },
      },
    },
  };
  return (
    <div className="chart-container" style={{ marginTop: 32, background: 'var(--color-surface)', borderRadius: 8, padding: 16 }}>
      <h3>Monthly Comparison Chart</h3>
      <Bar data={data} options={options} height={400} />
    </div>
  );
}
