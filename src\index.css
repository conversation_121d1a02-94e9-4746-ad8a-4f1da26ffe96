@tailwind base;
@tailwind components;
@tailwind utilities;

/* Dark theme color variables */
:root {
  /* dark (default) */
  /* Lighter dark palette (requested) */
  --color-bg: #232c3a;   /* dark slate */
  --color-surface: #2e3949; /* card/background */
  --color-text: #f8fafc;  /* near-white */
  --color-accent: #3b82f6;
  --color-surface-dark: #263141;
  --color-border: #475569;
}

/* Light theme overrides */
.light-theme {
  --color-bg: #f8fafc;
  --color-surface: #ffffff;
  --color-surface-dark: #f1f5f9;
  --color-text: #1e293b;
  --color-border: #cbd5e1;
}


body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: var(--color-bg);
  color: var(--color-text);
  font-size: 16px;
}

.container {
  max-width: 100%;
  margin: 0 auto;
  padding: 16px;
  padding-top: 72px;
  box-sizing: border-box;
}

h1 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 24px;
  padding-bottom: 10px;
  border-bottom: 2px solid var(--color-accent);
  font-size: 1.8rem;
}

/* Map container styles */
.leaflet-container {
  width: 100% !important;
  height: 450px !important;
  border-radius: 8px;
  overflow: hidden;
}

/* Responsive layout adjustments */
@media (max-width: 1024px) {
  .container > div > div:first-child,
  .container > div > div:nth-child(2) {
    max-width: 100% !important;
    flex: 1 1 100% !important;
  }
  
  h1 {
    font-size: 1.6rem;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 12px;
  }
  
  h1 {
    font-size: 1.4rem;
    margin-bottom: 16px;
  }
  
  .leaflet-container {
    height: 400px !important;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 8px;
  }
  
  h1 {
    font-size: 1.2rem;
    margin-bottom: 12px;
  }
  
  .leaflet-container {
    height: 350px !important;
  }
}

/* Ensure all elements use border-box for consistent sizing */
* {
  box-sizing: border-box;
}

/* Responsive form elements */
label {
  font-weight: 600;
  color: #93c5fd !important; /* light blue for dark theme */
}
.light-theme label {
  color: #4b5563 !important; /* gray for light theme */
}
input, select, textarea, button {
  font-size: 1rem;
  background-color: var(--color-surface-dark);
  color: var(--color-text);
  border: 1px solid var(--color-border);
  border-radius: 0.375rem; /* match other UI radius */
  padding: 0.5rem 0.75rem;
  transition: border-color 0.2s;
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--color-accent);
}

input::placeholder, textarea::placeholder {
  color: var(--color-border);
}

@media (max-width: 480px) {
  input, select, textarea, button {
    font-size: 0.9rem;
  }
}

/* Responsive flex containers */
.flex-row {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 16px;
}

.flex-column {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Responsive map controls */
.map-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
}

.map-control-button {
  padding: 8px 12px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
}

/* Fix z-index for all Leaflet controls to appear below header (z-index: 1000) */
.leaflet-top, .leaflet-bottom {
  z-index: 999 !important; /* Below header but above map content */
}

.leaflet-control {
  z-index: 999 !important; /* Below header but above map content */
}

/* Ensure all leaflet control containers respect the z-index hierarchy */
.leaflet-control-container {
  z-index: 999 !important; /* Below header but above map content */
}

.leaflet-control-container * {
  z-index: inherit !important; /* Inherit from parent container */
}

/* Leaflet zoom control dark theme */
.leaflet-control-zoom a {
  background-color: var(--color-surface-dark) !important;
  color: var(--color-text) !important;
  border: 1px solid var(--color-border) !important;
}

.leaflet-control-zoom a:hover {
  background-color: var(--color-surface) !important;
}

/* Leaflet.draw toolbar theming */
/* Default (dark theme) styling */
.leaflet-draw-toolbar a {
  /* Use light colors that will invert to dark */
  background-color: #d1d5db !important; /* This will invert to dark */
  border: 1px solid #9ca3af !important; /* This will invert to dark border */
  color: var(--color-text) !important;
  filter: invert(1) !important;
}

.leaflet-draw-toolbar a:hover {
  background-color: #e5e7eb !important; /* This will invert to darker */
  border-color: #6b7280 !important; /* This will invert to accent-like */
  filter: invert(1) brightness(1.1) !important;
}

/* Light theme styling */
.light-theme .leaflet-draw-toolbar a {
  background-color: var(--color-surface) !important;
  border: 1px solid var(--color-border) !important;
  color: var(--color-text) !important;
  filter: none !important; /* Remove invert filter for light theme */
}

.light-theme .leaflet-draw-toolbar a:hover {
  background-color: var(--color-surface-dark) !important;
  border-color: var(--color-accent) !important;
  filter: none !important;
}

/* Leaflet.draw actions toolbar theming */
.leaflet-draw-actions a {
  background-color: var(--color-surface-dark) !important;
  border: 1px solid var(--color-border) !important;
  color: var(--color-text) !important;
}

.leaflet-draw-actions a:hover {
  background-color: var(--color-surface) !important;
}

.light-theme .leaflet-draw-actions a {
  background-color: var(--color-surface) !important;
  border: 1px solid var(--color-border) !important;
  color: var(--color-text) !important;
}

.light-theme .leaflet-draw-actions a:hover {
  background-color: var(--color-surface-dark) !important;
}

/* Leaflet.draw tooltip theming */
.leaflet-draw-tooltip {
  background-color: var(--color-surface-dark) !important;
  border: 1px solid var(--color-border) !important;
  color: var(--color-text) !important;
}

.light-theme .leaflet-draw-tooltip {
  background-color: var(--color-surface) !important;
  border: 1px solid var(--color-border) !important;
  color: var(--color-text) !important;
}

/* Leaflet.draw guide lines and interactive elements */
.leaflet-draw-guide-dash {
  stroke: var(--color-text) !important;
  opacity: 0.6 !important;
}

.light-theme .leaflet-draw-guide-dash {
  stroke: var(--color-text) !important;
  opacity: 0.8 !important;
}

/* Drawing vertex markers */
.leaflet-editing-icon {
  background-color: var(--color-surface) !important;
  border: 2px solid var(--color-accent) !important;
}

.light-theme .leaflet-editing-icon {
  background-color: var(--color-surface) !important;
  border: 2px solid var(--color-accent) !important;
}

/* Drawing toolbar container */
.leaflet-draw-toolbar {
  background-color: transparent !important;
  z-index: 999 !important; /* Ensure drawing toolbar appears below header */
}

/* Ensure drawing controls are visible and properly themed */
.leaflet-control-container .leaflet-draw-toolbar {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  z-index: 999 !important; /* Ensure drawing controls appear below header */
}

.light-theme .leaflet-control-container .leaflet-draw-toolbar {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

/* Drawing toolbar active and disabled states */
.leaflet-draw-toolbar a.leaflet-disabled {
  background-color: var(--color-surface) !important;
  opacity: 0.5 !important;
  cursor: not-allowed !important;
}

.light-theme .leaflet-draw-toolbar a.leaflet-disabled {
  background-color: var(--color-surface-dark) !important;
  opacity: 0.5 !important;
  cursor: not-allowed !important;
}

.leaflet-draw-toolbar a.leaflet-draw-toolbar-button-enabled {
  /* Use colors that will invert to accent blue */
  background-color: #fbbf24 !important; /* This will invert to blue-ish */
  color: black !important; /* This will invert to white */
  border-color: #fbbf24 !important;
  filter: invert(1) !important;
}

.light-theme .leaflet-draw-toolbar a.leaflet-draw-toolbar-button-enabled {
  background-color: var(--color-accent) !important;
  color: white !important;
  border-color: var(--color-accent) !important;
  filter: none !important;
}

/* Additional styling for better contrast */
.leaflet-draw-toolbar a {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2) !important;
}

.light-theme .leaflet-draw-toolbar a {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

/* Responsive popup styles */
.leaflet-popup-content {
  max-width: 300px;
  overflow-wrap: break-word;
}

@media (max-width: 480px) {
  .leaflet-popup-content {
    max-width: 200px;
    font-size: 0.9rem;
  }
}
