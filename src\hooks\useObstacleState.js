import { useState, useCallback, useMemo, useRef } from 'react';
import { validateObject, sanitizeObject, createSafeUpdater } from '../utils/stateValidation';

/**
 * Obstacle State Management Hook
 * Centralized state management for obstacles with validation and optimization
 */
export const useObstacleState = (initialObstacles = []) => {
  const [obstacles, setObstacles] = useState(initialObstacles);
  const [selectedObstacleId, setSelectedObstacleId] = useState(null);
  
  // Performance optimization: track last update
  const lastUpdateRef = useRef(Date.now());
  
  // Create safe updater for obstacle validation
  const safeObstacleUpdater = useMemo(() => 
    createSafeUpdater('obstacle', (message, errors) => {
      console.warn('[useObstacleState]', message, errors);
    }), []
  );

  // Memoized selectors
  const selectedObstacle = useMemo(() => 
    obstacles.find(obstacle => obstacle.id === selectedObstacleId) || null,
    [obstacles, selectedObstacleId]
  );

  const obstacleCount = useMemo(() => obstacles.length, [obstacles]);

  const obstaclesByType = useMemo(() => {
    const grouped = {};
    obstacles.forEach(obstacle => {
      const type = obstacle.type || 'unknown';
      if (!grouped[type]) grouped[type] = [];
      grouped[type].push(obstacle);
    });
    return grouped;
  }, [obstacles]);

  const totalObstacleArea = useMemo(() => {
    return obstacles.reduce((total, obstacle) => {
      if (!obstacle.coordinates || obstacle.coordinates.length < 3) return total;
      
      // Simple polygon area calculation (Shoelace formula)
      let area = 0;
      const coords = obstacle.coordinates;
      for (let i = 0; i < coords.length; i++) {
        const j = (i + 1) % coords.length;
        area += coords[i][0] * coords[j][1];
        area -= coords[j][0] * coords[i][1];
      }
      return total + Math.abs(area) / 2;
    }, 0);
  }, [obstacles]);

  // Obstacle management actions
  const addObstacle = useCallback((obstacleData) => {
    const newObstacle = {
      id: obstacleData.id || `obstacle_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: Date.now(),
      lastModified: Date.now(),
      type: 'building',
      height: 3,
      ...obstacleData
    };

    // Validate new obstacle
    const validation = validateObject(newObstacle, 'obstacle');
    if (!validation.isValid) {
      console.error('[useObstacleState] Invalid obstacle data:', validation.errors);
      return false;
    }

    const sanitizedObstacle = sanitizeObject(newObstacle, 'obstacle');
    
    setObstacles(prev => [...prev, sanitizedObstacle]);
    lastUpdateRef.current = Date.now();
    return true;
  }, []);

  const updateObstacle = useCallback((obstacleId, updates) => {
    setObstacles(prev => prev.map(obstacle => {
      if (obstacle.id !== obstacleId) return obstacle;
      
      const updatedObstacle = safeObstacleUpdater(obstacle, {
        ...updates,
        lastModified: Date.now()
      });
      
      return updatedObstacle;
    }));
    lastUpdateRef.current = Date.now();
  }, [safeObstacleUpdater]);

  const removeObstacle = useCallback((obstacleId) => {
    setObstacles(prev => prev.filter(obstacle => obstacle.id !== obstacleId));
    
    // Clear selection if removed obstacle was selected
    if (selectedObstacleId === obstacleId) {
      setSelectedObstacleId(null);
    }
    
    lastUpdateRef.current = Date.now();
  }, [selectedObstacleId]);

  const selectObstacle = useCallback((obstacleId) => {
    setSelectedObstacleId(obstacleId);
  }, []);

  const deselectObstacle = useCallback(() => {
    setSelectedObstacleId(null);
  }, []);

  const updateObstacleHeight = useCallback((obstacleId, height) => {
    updateObstacle(obstacleId, { height: Math.max(0, height) });
  }, [updateObstacle]);

  const updateObstacleCoordinates = useCallback((obstacleId, coordinates) => {
    if (!Array.isArray(coordinates) || coordinates.length < 3) {
      console.warn('[useObstacleState] Invalid coordinates for obstacle:', obstacleId);
      return;
    }
    
    updateObstacle(obstacleId, { coordinates });
  }, [updateObstacle]);

  const updateObstacleType = useCallback((obstacleId, type) => {
    const validTypes = ['building', 'tree', 'fence-segment', 'manual-pv-panel', 'auto-pv-panel'];
    if (!validTypes.includes(type)) {
      console.warn('[useObstacleState] Invalid obstacle type:', type);
      return;
    }
    
    updateObstacle(obstacleId, { type });
  }, [updateObstacle]);

  // Bulk operations
  const addMultipleObstacles = useCallback((obstaclesData) => {
    const validObstacles = obstaclesData.filter(obstacle => {
      const validation = validateObject(obstacle, 'obstacle');
      if (!validation.isValid) {
        console.warn('[useObstacleState] Skipping invalid obstacle:', validation.errors);
        return false;
      }
      return true;
    });

    const sanitizedObstacles = validObstacles.map(obstacle => 
      sanitizeObject({
        id: obstacle.id || `obstacle_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        createdAt: Date.now(),
        lastModified: Date.now(),
        type: 'building',
        height: 3,
        ...obstacle
      }, 'obstacle')
    );

    setObstacles(prev => [...prev, ...sanitizedObstacles]);
    lastUpdateRef.current = Date.now();
    
    return sanitizedObstacles.length;
  }, []);

  const clearAllObstacles = useCallback(() => {
    setObstacles([]);
    setSelectedObstacleId(null);
    lastUpdateRef.current = Date.now();
  }, []);

  const removeObstaclesByType = useCallback((type) => {
    setObstacles(prev => prev.filter(obstacle => obstacle.type !== type));
    
    // Clear selection if selected obstacle was removed
    const selectedObstacle = obstacles.find(o => o.id === selectedObstacleId);
    if (selectedObstacle && selectedObstacle.type === type) {
      setSelectedObstacleId(null);
    }
    
    lastUpdateRef.current = Date.now();
  }, [obstacles, selectedObstacleId]);

  const importObstacles = useCallback((obstaclesData) => {
    const validObstacles = obstaclesData.filter(obstacle => {
      const validation = validateObject(obstacle, 'obstacle');
      if (!validation.isValid) {
        console.warn('[useObstacleState] Skipping invalid obstacle:', validation.errors);
        return false;
      }
      return true;
    });

    const sanitizedObstacles = validObstacles.map(obstacle => 
      sanitizeObject(obstacle, 'obstacle')
    );

    setObstacles(sanitizedObstacles);
    setSelectedObstacleId(null);
    lastUpdateRef.current = Date.now();
    
    return sanitizedObstacles.length;
  }, []);

  // Query functions
  const getObstacleById = useCallback((id) => {
    return obstacles.find(obstacle => obstacle.id === id) || null;
  }, [obstacles]);

  const getObstaclesByType = useCallback((type) => {
    return obstacles.filter(obstacle => obstacle.type === type);
  }, [obstacles]);

  const getObstaclesInBounds = useCallback((bounds) => {
    return obstacles.filter(obstacle => {
      if (!obstacle.coordinates || obstacle.coordinates.length === 0) return false;
      
      // Check if any coordinate is within bounds
      return obstacle.coordinates.some(coord => {
        const [lat, lng] = coord;
        return lat >= bounds.south && lat <= bounds.north && 
               lng >= bounds.west && lng <= bounds.east;
      });
    });
  }, [obstacles]);

  // Performance metrics
  const getPerformanceMetrics = useCallback(() => ({
    obstacleCount,
    totalObstacleArea,
    obstaclesByType: Object.keys(obstaclesByType).reduce((acc, type) => {
      acc[type] = obstaclesByType[type].length;
      return acc;
    }, {}),
    lastUpdate: lastUpdateRef.current,
    selectedObstacleId
  }), [obstacleCount, totalObstacleArea, obstaclesByType, selectedObstacleId]);

  return {
    // State
    obstacles,
    selectedObstacleId,
    selectedObstacle,
    
    // Computed values
    obstacleCount,
    obstaclesByType,
    totalObstacleArea,
    
    // Actions
    addObstacle,
    updateObstacle,
    removeObstacle,
    selectObstacle,
    deselectObstacle,
    updateObstacleHeight,
    updateObstacleCoordinates,
    updateObstacleType,
    
    // Bulk operations
    addMultipleObstacles,
    clearAllObstacles,
    removeObstaclesByType,
    importObstacles,
    
    // Query functions
    getObstacleById,
    getObstaclesByType,
    getObstaclesInBounds,
    
    // Utilities
    getPerformanceMetrics,
    
    // Direct state setters (for advanced use cases)
    setObstacles,
    setSelectedObstacleId
  };
};

export default useObstacleState;
