import { useState, useEffect, useCallback, useMemo } from 'react';

/**
 * Viewport-based rendering optimization hook
 * Only renders elements that are visible or near the viewport
 */
export const useViewportOptimization = (mapRef, items = [], options = {}) => {
  const {
    bufferDistance = 0.001, // Buffer around viewport in degrees
    enableVirtualization = true,
    maxItemsToRender = 1000 // Maximum items to render at once
  } = options;

  const [mapBounds, setMapBounds] = useState(null);
  const [visibleItems, setVisibleItems] = useState(items);

  // Update map bounds when map moves
  const updateMapBounds = useCallback(() => {
    if (!mapRef?.current || !enableVirtualization) {
      setVisibleItems(items);
      return;
    }

    try {
      const map = mapRef.current;
      const bounds = map.getBounds();
      
      if (bounds) {
        const extendedBounds = {
          north: bounds.getNorth() + bufferDistance,
          south: bounds.getSouth() - bufferDistance,
          east: bounds.getEast() + bufferDistance,
          west: bounds.getWest() - bufferDistance
        };
        
        setMapBounds(extendedBounds);
      }
    } catch (error) {
      console.warn('[useViewportOptimization] Error updating map bounds:', error);
      setVisibleItems(items);
    }
  }, [mapRef, bufferDistance, enableVirtualization, items]);

  // Filter items based on viewport bounds
  const filteredItems = useMemo(() => {
    if (!enableVirtualization || !mapBounds || !items.length) {
      return items.slice(0, maxItemsToRender);
    }

    const visible = items.filter(item => {
      // Check if item has position or rectangles to determine visibility
      if (item.position) {
        const { lat, lng } = item.position;
        return (
          lat >= mapBounds.south &&
          lat <= mapBounds.north &&
          lng >= mapBounds.west &&
          lng <= mapBounds.east
        );
      }

      if (item.rectangles && item.rectangles.length > 0) {
        // Check if any rectangle is within bounds
        return item.rectangles.some(rectangle => {
          return rectangle.some(point => {
            const lat = point[0];
            const lng = point[1];
            return (
              lat >= mapBounds.south &&
              lat <= mapBounds.north &&
              lng >= mapBounds.west &&
              lng <= mapBounds.east
            );
          });
        });
      }

      // If no position info, include by default
      return true;
    });

    // Limit the number of items to render for performance
    return visible.slice(0, maxItemsToRender);
  }, [items, mapBounds, enableVirtualization, maxItemsToRender]);

  // Set up map event listeners
  useEffect(() => {
    if (!mapRef?.current || !enableVirtualization) {
      setVisibleItems(items);
      return;
    }

    const map = mapRef.current;
    
    // Initial bounds update
    updateMapBounds();

    // Listen for map events
    const handleMapMove = () => updateMapBounds();
    const handleMapZoom = () => updateMapBounds();

    map.on('moveend', handleMapMove);
    map.on('zoomend', handleMapZoom);
    map.on('resize', handleMapMove);

    return () => {
      if (map) {
        map.off('moveend', handleMapMove);
        map.off('zoomend', handleMapZoom);
        map.off('resize', handleMapMove);
      }
    };
  }, [mapRef, updateMapBounds, enableVirtualization, items]);

  // Update visible items when filtered items change
  useEffect(() => {
    setVisibleItems(filteredItems);
  }, [filteredItems]);

  return {
    visibleItems,
    totalItems: items.length,
    isVirtualized: enableVirtualization && mapBounds !== null,
    mapBounds
  };
};

/**
 * Hook for optimizing rendering of large collections
 * Provides batching and throttling capabilities
 */
export const useRenderOptimization = (items = [], options = {}) => {
  const {
    batchSize = 50,
    renderDelay = 16, // ~60fps
    enableBatching = true
  } = options;

  const [renderedItems, setRenderedItems] = useState([]);
  const [isRendering, setIsRendering] = useState(false);

  useEffect(() => {
    if (!enableBatching || items.length <= batchSize) {
      setRenderedItems(items);
      return;
    }

    setIsRendering(true);
    let currentIndex = 0;
    const rendered = [];

    const renderBatch = () => {
      const endIndex = Math.min(currentIndex + batchSize, items.length);
      
      for (let i = currentIndex; i < endIndex; i++) {
        rendered.push(items[i]);
      }
      
      setRenderedItems([...rendered]);
      currentIndex = endIndex;

      if (currentIndex < items.length) {
        setTimeout(renderBatch, renderDelay);
      } else {
        setIsRendering(false);
      }
    };

    renderBatch();
  }, [items, batchSize, renderDelay, enableBatching]);

  return {
    renderedItems,
    isRendering,
    progress: items.length > 0 ? (renderedItems.length / items.length) * 100 : 100
  };
};

export default useViewportOptimization;
