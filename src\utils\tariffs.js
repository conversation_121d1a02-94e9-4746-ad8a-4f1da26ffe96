// Tariff structures and bill/consumption conversion utilities

export const TARIFF_STRUCTURES = {
  residential: {
    consumption_ranges_category: [1, 1, 2, 2, 2, 3, 4],
    consumption_ranges: [50, 100, 200, 350, 650, 1000, 1000000],
    tariffs: [0.68, 0.78, 0.95, 1.55, 1.95, 2.10, 2.23]
  },
  commercial: {
    consumption_ranges_category: [1, 2, 3, 3, 4],
    consumption_ranges: [100, 250, 600, 1000, 1000000],
    tariffs: [0.85, 1.68, 2.2, 2.27, 2.33]
  },
  low_voltage: {
    consumption_ranges_category: [1],
    consumption_ranges: [100000000],
    tariffs: [2.34]
  },
  medium_voltage: {
    consumption_ranges_category: [1],
    consumption_ranges: [100000000],
    tariffs: [1.94]
  },
  irrigation_low_voltage: {
    consumption_ranges_category: [1],
    consumption_ranges: [100000000],
    tariffs: [2]
  }
};

// Bill to consumption
export function billToConsumption(bill, structure) {
  const { consumption_ranges_category, consumption_ranges, tariffs } = structure;
  for (let category = consumption_ranges_category[0]; category <= consumption_ranges_category[consumption_ranges_category.length - 1]; category++) {
    const current_consumption_ranges = [0];
    const current_tariffs = [0];
    for (let i = 0; i < consumption_ranges_category.length; i++) {
      if (consumption_ranges_category[i] === category) {
        current_consumption_ranges.push(consumption_ranges[i]);
        current_tariffs.push(tariffs[i]);
      }
    }
    const current_bills = [0];
    for (let i = 0; i < current_consumption_ranges.length - 1; i++) {
      current_bills.push(
        current_bills[i] + (current_consumption_ranges[i+1] - current_consumption_ranges[i]) * current_tariffs[i+1]
      );
    }
    if (bill <= current_bills[current_bills.length - 1]) {
      for (let i = 0; i < current_bills.length; i++) {
        if (bill <= current_bills[i]) {
          const consumption = current_consumption_ranges[i] - (current_bills[i] - bill) / current_tariffs[i];
          return consumption;
        }
      }
      break;
    }
  }
  // If bill is higher than all categories
  const highestTariff = tariffs[tariffs.length - 1];
  const highestRange = consumption_ranges[consumption_ranges.length - 1];
  const billAtHighestRange = highestRange * highestTariff;
  const additionalConsumption = (bill - billAtHighestRange) / highestTariff;
  return highestRange + additionalConsumption;
}

// Calculate bill for a given consumption
export function calculateBill(structure, consumption) {
  const { consumption_ranges_category, consumption_ranges, tariffs } = structure;
  if (consumption <= 0) return [0, 0];
  let categoryIdx = consumption_ranges.length - 1;
  for (let i = 0; i < consumption_ranges.length; i++) {
    if (consumption <= consumption_ranges[i]) {
      categoryIdx = i;
      break;
    }
  }
  const targetCategory = consumption_ranges_category[categoryIdx];
  let totalBill = 0;
  let remaining = consumption;
  let prevLimit = 0;
  let billTier = 0;
  for (let i = 0; i < consumption_ranges.length; i++) {
    if (consumption_ranges_category[i] !== targetCategory) continue;
    const currentLimit = consumption_ranges[i];
    const amountInTier = Math.min(remaining, currentLimit - prevLimit);
    if (amountInTier > 0) {
      totalBill += amountInTier * tariffs[i];
      remaining -= amountInTier;
      billTier = i + 1;
    }
    prevLimit = currentLimit;
    if (remaining <= 0) break;
  }
  return [totalBill, billTier];
}
