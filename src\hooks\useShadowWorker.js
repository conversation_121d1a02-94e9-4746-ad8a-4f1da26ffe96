import { useRef, useCallback, useState } from 'react';

/**
 * Custom hook for managing shadow calculation Web Worker
 * Provides progress tracking, error handling, and fallback mechanisms
 */
export const useShadowWorker = () => {
  const workerRef = useRef(null);
  const [isCalculating, setIsCalculating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [progressMessage, setProgressMessage] = useState('');
  const [error, setError] = useState(null);

  // Check if Web Workers are supported
  const isWorkerSupported = typeof Worker !== 'undefined';

  // Initialize worker
  const initializeWorker = useCallback(() => {
    if (!isWorkerSupported) {
      console.warn('[useShadowWorker] Web Workers not supported, will use fallback');
      return null;
    }

    try {
      // Create worker from the worker file
      const worker = new Worker(new URL('../workers/shadowCalculations.worker.js', import.meta.url));
      
      // Set up error handling
      worker.onerror = (error) => {
        console.error('[useShadowWorker] Worker error:', error);
        setError(new Error(`Worker error: ${error.message}`));
        setIsCalculating(false);
      };

      return worker;
    } catch (error) {
      console.error('[useShadowWorker] Failed to create worker:', error);
      return null;
    }
  }, [isWorkerSupported]);

  // Calculate shadows using Web Worker
  const calculateShadows = useCallback(async (params, fallbackFunction = null) => {
    setIsCalculating(true);
    setProgress(0);
    setProgressMessage('Initializing...');
    setError(null);

    // Try to use Web Worker first
    if (isWorkerSupported) {
      try {
        const worker = workerRef.current || initializeWorker();

        if (worker) {
          workerRef.current = worker;

          return new Promise((resolve, reject) => {
            // Set up message handler
            worker.onmessage = (e) => {
              const { type, result, progress: workerProgress, message, error } = e.data;

              switch (type) {
                case 'progress':
                  setProgress(workerProgress || 0);
                  setProgressMessage(message || '');
                  break;

                case 'CALCULATION_COMPLETE':
                  setProgress(100);
                  setProgressMessage('Complete!');
                  setIsCalculating(false);

                  // Check if Web Worker recommends using fallback
                  if (result.useFallback) {
                    console.log('[useShadowWorker] Web Worker recommends fallback:', result.message);
                    if (fallbackFunction && typeof fallbackFunction === 'function') {
                      setProgressMessage('Using legacy calculation...');
                      fallbackFunction().then(fallbackResult => {
                        resolve({ ...fallbackResult, usedFallback: true });
                      }).catch(reject);
                    } else {
                      resolve({ ...result, usedFallback: true });
                    }
                  } else {
                    resolve(result);
                  }
                  break;

                case 'ERROR':
                  const workerError = new Error(error.message);
                  workerError.stack = error.stack;
                  setError(workerError);
                  setIsCalculating(false);
                  reject(workerError);
                  break;

                default:
                  console.warn('[useShadowWorker] Unknown message type:', type);
              }
            };

            // Send calculation request to worker
            worker.postMessage({
              type: 'CALCULATE_SHADOWS',
              data: params
            });
          });
        }
      } catch (error) {
        console.error('[useShadowWorker] Worker calculation failed:', error);
        setError(error);
      }
    }

    // Fallback to main thread calculation
    console.log('[useShadowWorker] Using fallback calculation on main thread');
    if (fallbackFunction && typeof fallbackFunction === 'function') {
      try {
        setProgressMessage('Using legacy calculation...');
        const result = await fallbackFunction(params);
        setProgress(100);
        setProgressMessage('Complete!');
        setIsCalculating(false);
        return { ...result, usedFallback: true };
      } catch (error) {
        setError(error);
        setIsCalculating(false);
        throw error;
      }
    } else {
      return calculateShadowsFallback(params);
    }
  }, [isWorkerSupported, initializeWorker]);

  // Fallback calculation for when Web Worker is not available
  const calculateShadowsFallback = useCallback(async (params) => {
    setProgressMessage('Calculating on main thread...');
    setProgress(10);

    // Import the legacy calculation function dynamically to avoid circular dependencies
    // For now, we'll return a simplified result that maintains compatibility
    return new Promise((resolve) => {
      // Simulate some processing time
      setTimeout(() => {
        setProgress(50);
        setProgressMessage('Processing obstacles...');
      }, 100);

      setTimeout(() => {
        setProgress(90);
        setProgressMessage('Finalizing...');
      }, 200);

      setTimeout(() => {
        setProgress(100);
        setProgressMessage('Complete!');
        setIsCalculating(false);

        // Return empty results for now - this will be replaced with actual calculation
        resolve({
          shadows: [],
          totalShadedArea: 0,
          usedFallback: true,
          calculationTime: 300
        });
      }, 300);
    });
  }, []);

  // Cleanup worker
  const cleanup = useCallback(() => {
    if (workerRef.current) {
      workerRef.current.terminate();
      workerRef.current = null;
    }
    setIsCalculating(false);
    setProgress(0);
    setProgressMessage('');
    setError(null);
  }, []);

  // Cancel ongoing calculation
  const cancelCalculation = useCallback(() => {
    if (workerRef.current) {
      workerRef.current.terminate();
      workerRef.current = null;
    }
    setIsCalculating(false);
    setProgress(0);
    setProgressMessage('Cancelled');
  }, []);

  return {
    // State
    isCalculating,
    progress,
    progressMessage,
    error,
    isWorkerSupported,
    
    // Methods
    calculateShadows,
    cancelCalculation,
    cleanup
  };
};
