import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Bar } from 'react-chartjs-2';
import { FaMoneyBillWave } from 'react-icons/fa';
import FinancialParametersPopup from './FinancialParametersPopup';

export default function FinancialSummary({ summary, financialInputs, setFinancialInputs, recalculateFinancials }) {
  const [showFinancialPopup, setShowFinancialPopup] = useState(false);
  const { t } = useTranslation();
  if (!summary) return null;

  // Create financial details table data
  const tableData = [];
  const startYear = summary.startYear || new Date().getFullYear();
  
  if (summary.years && summary.netProfit && summary.cumulativeProfit) {
    for (let i = 0; i < Math.min(20, summary.years.length); i++) {
      tableData.push({
        year: startYear + i, // Use startYear parameter
        production: summary.production && summary.production[i] ? summary.production[i] : 0,
        tariff: summary.tariffs && summary.tariffs[i] ? summary.tariffs[i] : 0,
        maintenance: summary.maintenance && summary.maintenance[i] ? summary.maintenance[i] : 0,
        netProfit: summary.netProfit[i] || 0,
        cumulativeProfit: summary.cumulativeProfit[i] || 0
      });
    }
  }

  return (
    <div className="results-section" style={{ marginTop: 32, background: 'var(--color-surface)', borderRadius: 8, padding: 16, width: '100%', maxWidth: '1200px', margin: '32px auto' }}>
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '10px', marginBottom: '10px' }}>
        <h2 style={{ textAlign: 'center', margin: 0 }}>{t('Financial Summary')}</h2>
        {financialInputs && setFinancialInputs && (
          <button
            onClick={() => setShowFinancialPopup(true)}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: '36px',
              height: '36px',
              padding: '0',
              background: '#2563eb',
              color: 'var(--color-text)',
              border: 'none',
              borderRadius: '50%',
              cursor: 'pointer',
              boxShadow: '0 2px 4px rgba(37, 99, 235, 0.2)'
            }}
            title={t('Financial Parameters')}
          >
            <FaMoneyBillWave size={16} />
          </button>
        )}
      </div>
      <div style={{ 
        display: 'flex', 
        flexWrap: 'wrap', 
        gap: '24px', 
        marginBottom: '32px',
        justifyContent: 'center',
        padding: '20px',
        background: 'var(--color-surface-dark)',
        borderRadius: '12px',
        boxShadow: '0 2px 8px rgba(37, 99, 235, 0.1)',
        maxWidth: '900px',
        margin: '0 auto 32px auto'
      }}>
        <div style={{ 
          flex: '1 1 160px', 
          minWidth: '140px', 
          textAlign: 'center',
          padding: '16px',
          background: 'var(--color-surface)',
          borderRadius: '8px',
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)'
        }}>
          <div style={{ fontWeight: 'bold', color: '#2563eb', marginBottom: '8px', fontSize: '16px' }}>{t('Payback Period')}</div>
          <div style={{ fontSize: '22px', fontWeight: '700' }}>
            {summary.paybackPeriod === Infinity ? 'Never' : summary.paybackPeriod.toFixed(2)}
            <span style={{ fontSize: '14px' }}> years</span>
          </div>
        </div>
        
        <div style={{ 
          flex: '1 1 160px', 
          minWidth: '140px', 
          textAlign: 'center',
          padding: '16px',
          background: 'var(--color-surface)',
          borderRadius: '8px',
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)'
        }}>
          <div style={{ fontWeight: 'bold', color: '#2563eb', marginBottom: '8px', fontSize: '16px' }}>{t('IRR')}</div>
          <div style={{ fontSize: '22px', fontWeight: '700' }}>
            {summary.irr.toFixed(2)}<span style={{ fontSize: '14px' }}>%</span>
          </div>
        </div>
        
        <div style={{ 
          flex: '1 1 160px', 
          minWidth: '140px', 
          textAlign: 'center',
          padding: '16px',
          background: 'var(--color-surface)',
          borderRadius: '8px',
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)'
        }}>
          <div style={{ fontWeight: 'bold', color: '#2563eb', marginBottom: '8px', fontSize: '16px' }}>{t('NPV')}</div>
          <div style={{ fontSize: '22px', fontWeight: '700' }}>
            {summary.npv.toLocaleString(undefined, { maximumFractionDigits: 2 })}
            <span style={{ fontSize: '14px' }}> EGP</span>
          </div>
        </div>
        
        <div style={{ 
          flex: '1 1 160px', 
          minWidth: '140px', 
          textAlign: 'center',
          padding: '16px',
          background: 'var(--color-surface)',
          borderRadius: '8px',
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)'
        }}>
          <div style={{ fontWeight: 'bold', color: '#2563eb', marginBottom: '8px', fontSize: '16px' }}>{t('ROI')}</div>
          <div style={{ fontSize: '22px', fontWeight: '700' }}>
            {summary.roi.toFixed(2)}<span style={{ fontSize: '14px' }}>%</span>
          </div>
        </div>
        
        <div style={{ 
          flex: '1 1 160px', 
          minWidth: '140px', 
          textAlign: 'center',
          padding: '16px',
          background: 'var(--color-surface)',
          borderRadius: '8px',
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)'
        }}>
          <div style={{ fontWeight: 'bold', color: '#2563eb', marginBottom: '8px', fontSize: '16px' }}>{t('LCOE')}</div>
          <div style={{ fontSize: '22px', fontWeight: '700' }}>
            {summary.lcoe.toFixed(3)}
            <span style={{ fontSize: '14px' }}> EGP/kWh</span>
          </div>
        </div>
      </div>

      {tableData.length > 0 && (
        <>
          <h3 style={{ textAlign: 'center' }}>{t('Financial Details')}</h3>
          <div style={{ display: 'flex', gap: 16, alignItems: 'flex-end', flexWrap: 'wrap', justifyContent: 'center' }}>
            {/* Table on the left */}
            <div style={{ flex: 1, minWidth: 260, maxWidth: 600, margin: '0 auto' }}>
              <div className="table-responsive" style={{ overflowX: 'auto', maxHeight: '500px', overflowY: 'auto' }}>
                <table style={{ width: '100%', borderCollapse: 'collapse', fontSize: '14px', tableLayout: 'fixed' }}>
                  <colgroup>
                    <col style={{ width: '8%' }} />
                    <col style={{ width: '17%' }} />
                    <col style={{ width: '15%' }} />
                    <col style={{ width: '15%' }} />
                    <col style={{ width: '20%' }} />
                    <col style={{ width: '25%' }} />
                  </colgroup>
                  <thead>
                    <tr style={{ background: 'var(--color-surface-dark)', color: 'var(--color-text)', position: 'sticky', top: 0, zIndex: 10 }}>
                      <th style={{ padding: '8px 12px', textAlign: 'left' }}>Year</th>
                      <th style={{ padding: '8px 12px', textAlign: 'right' }}>Production (kWh)</th>
                      <th style={{ padding: '8px 12px', textAlign: 'right' }}>Tariff (EGP/kWh)</th>
                      <th style={{ padding: '8px 12px', textAlign: 'right' }}>Maintenance</th>
                      <th style={{ padding: '8px 12px', textAlign: 'right' }}>Net Profit</th>
                      <th style={{ padding: '8px 12px', textAlign: 'right' }}>Cumulative Profit</th>
                    </tr>
                  </thead>
                  <tbody>
                    {tableData.map((row, index) => (
                      <tr key={row.year} style={{ background: index % 2 === 0 ? 'var(--color-surface-dark)' : 'var(--color-surface)', borderBottom: '1px solid var(--color-border)' }}>
                        <td style={{ padding: '8px 12px' }}>{row.year}</td>
                        <td style={{ padding: '8px 12px', textAlign: 'right', color: '#0369a1' }}>
                          {row.production.toFixed(2)}
                        </td>
                        <td style={{ padding: '8px 12px', textAlign: 'right' }}>
                          {parseFloat(row.tariff).toFixed(2)}
                        </td>
                        <td style={{ padding: '8px 12px', textAlign: 'right' }}>
                          {parseFloat(row.maintenance).toLocaleString(undefined, { maximumFractionDigits: 2, minimumFractionDigits: 2 })}
                        </td>
                        <td style={{ padding: '8px 12px', textAlign: 'right', color: '#16a34a', fontWeight: 'bold' }}>
                          {parseFloat(row.netProfit).toLocaleString(undefined, { maximumFractionDigits: 2, minimumFractionDigits: 2 })}
                        </td>
                        <td style={{ padding: '8px 12px', textAlign: 'right', 
                            color: parseFloat(row.cumulativeProfit) < 0 ? '#ef4444' : '#16a34a', 
                            fontWeight: 'bold' }}>
                          {parseFloat(row.cumulativeProfit) < 0 ? '-' : ''}{Math.abs(parseFloat(row.cumulativeProfit)).toLocaleString(undefined, { maximumFractionDigits: 2, minimumFractionDigits: 2 })}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
            
            {/* Chart on the right */}
            <div style={{ flex: 1, minWidth: 260, maxWidth: 600, alignSelf: 'flex-end', display: 'flex', flexDirection: 'column', justifyContent: 'flex-end', margin: '0 auto' }}>
              <div className="chart-container" style={{ background: 'var(--color-surface)', borderRadius: 8, padding: 8, boxShadow: '0 2px 8px rgba(0,0,0,0.04)', width: '100%', marginTop: 16, height: 500, minHeight: 500, maxHeight: 500, overflow: 'hidden', display: 'flex', flexDirection: 'column', justifyContent: 'flex-end' }}>
                <div style={{ fontWeight: 700, fontSize: 16, textAlign: 'center', marginBottom: 4 }}>Net and Cumulative Profit</div>
                <div style={{ flex: 1, display: 'flex', alignItems: 'flex-end' }}>
                  <Bar
                    data={{
                      labels: summary.years,
                      datasets: [
                        {
                          label: 'Net Profit (EGP)',
                          data: summary.netProfit,
                          backgroundColor: 'rgba(134, 239, 172, 0.8)',
                          borderColor: 'rgb(74, 222, 128)',
                          borderWidth: 1,
                          borderRadius: 4,
                          barPercentage: 0.9,
                          categoryPercentage: 0.95,
                          order: 2
                        },
                        {
                          label: 'Cumulative Profit (EGP)',
                          data: summary.cumulativeProfit,
                          backgroundColor: function(context) {
                            const value = context.dataset.data[context.dataIndex];
                            return value < 0 ? 'rgba(239, 68, 68, 0.8)' : 'rgba(16, 185, 129, 0.8)';
                          },
                          borderColor: function(context) {
                            const value = context.dataset.data[context.dataIndex];
                            return value < 0 ? 'rgb(239, 68, 68)' : 'rgb(16, 185, 129)';
                          },
                          borderWidth: 1,
                          borderRadius: 4,
                          barPercentage: 0.9,
                          categoryPercentage: 0.95,
                          order: 1
                        },
                      ],
                    }}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: { 
                        legend: { position: 'top' },
                        tooltip: {
                          callbacks: {
                            label: function(context) {
                              return `${context.dataset.label}: ${context.raw.toLocaleString()} EGP`;
                            }
                          }
                        }
                      },
                      scales: {
                        y: { 
                          title: { display: false },
                          grid: { color: 'rgba(0, 0, 0, 0.05)' }
                        },
                        x: { 
                          title: { display: false },
                          grid: { display: false }
                        },
                      },
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        </>
      )}
      
      {/* Financial Parameters Popup */}
      <FinancialParametersPopup
        values={financialInputs}
        setValues={setFinancialInputs}
        onClose={() => {
          setShowFinancialPopup(false);
          if (recalculateFinancials) {
            recalculateFinancials(financialInputs);
          }
        }}
        autoUpdate={true}
        isOpen={showFinancialPopup}
      />
    </div>
  );
}
