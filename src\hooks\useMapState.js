import { useState, useCallback, useMemo, useRef } from 'react';
import { validateObject, sanitizeObject } from '../utils/stateValidation';
import { usePVTableState } from './usePVTableState';
import { useObstacleState } from './useObstacleState';

/**
 * Main Map State Management Hook
 * Centralized state management for the entire map component
 */
export const useMapState = (initialConfig = {}) => {
  // Location and map state
  const [location, setLocation] = useState(
    initialConfig.location || { lat: 40.7128, lng: -74.0060 }
  );
  const [selectedCoord, setSelectedCoord] = useState(null);
  const [mapRef, setMapRef] = useState(null);
  
  // Drawing and interaction state
  const [mode, setMode] = useState(initialConfig.mode || 'VIEW');
  const [drawingArea, setDrawingArea] = useState(false);
  const [drawTableMode, setDrawTableMode] = useState(false);
  const [tableDrawingPoints, setTableDrawingPoints] = useState([]);
  
  // Main roof state
  const [mainRoofLayer, setMainRoofLayer] = useState(null);
  const [mainRoofArea, setMainRoofArea] = useState(0);
  
  // Shadow analysis state
  const [shadowLayers, setShadowLayers] = useState([]);
  const [totalShadedAreaOnRoof, setTotalShadedAreaOnRoof] = useState(0);
  const [shadowsVisible, setShadowsVisible] = useState(true);
  const [isCalculatingShadows, setIsCalculatingShadows] = useState(false);
  const [analysisDate, setAnalysisDate] = useState(
    initialConfig.analysisDate || new Date().toISOString().split('T')[0]
  );
  const [analysisDurationHours, setAnalysisDurationHours] = useState(
    initialConfig.analysisDurationHours || 6
  );
  
  // Auto-placed PV features
  const [autoPlacedPvFeatures, setAutoPlacedPvFeatures] = useState(null);
  
  // Drag state
  const [isDragging, setIsDragging] = useState(false);
  const [isDragStarted, setIsDragStarted] = useState(false);
  const [dragStartPosition, setDragStartPosition] = useState(null);
  
  // Modal and UI state
  const [pendingObstacle, setPendingObstacle] = useState(null);
  const [showHeightModalForObstacleId, setShowHeightModalForObstacleId] = useState(null);
  const [currentObstacleLayer, setCurrentObstacleLayer] = useState(null);
  
  // Performance tracking
  const lastStateUpdateRef = useRef(Date.now());
  
  // Initialize sub-state hooks
  const pvTableState = usePVTableState(initialConfig.pvTables);
  const obstacleState = useObstacleState(initialConfig.obstacles);
  
  // Validate location changes
  const updateLocation = useCallback((newLocation) => {
    const validation = validateObject(newLocation, 'location');
    if (!validation.isValid) {
      console.error('[useMapState] Invalid location:', validation.errors);
      return false;
    }
    
    const sanitizedLocation = sanitizeObject(newLocation, 'location');
    setLocation(sanitizedLocation);
    lastStateUpdateRef.current = Date.now();
    return true;
  }, []);
  
  // Mode management
  const MODES = useMemo(() => ({
    VIEW: 'VIEW',
    MEASURE: 'MEASURE',
    DRAW: 'DRAW'
  }), []);
  
  const setMapMode = useCallback((newMode) => {
    if (!Object.values(MODES).includes(newMode)) {
      console.warn('[useMapState] Invalid mode:', newMode);
      return;
    }
    setMode(newMode);
  }, [MODES]);
  
  // Drawing state management
  const startDrawingArea = useCallback(() => {
    setDrawingArea(true);
    setCurrentObstacleLayer(null);
    setMainRoofArea(0);
  }, []);
  
  const stopDrawingArea = useCallback(() => {
    setDrawingArea(false);
  }, []);
  
  const startTableDrawing = useCallback(() => {
    setDrawTableMode(true);
    setTableDrawingPoints([]);
  }, []);
  
  const stopTableDrawing = useCallback(() => {
    setDrawTableMode(false);
    setTableDrawingPoints([]);
  }, []);
  
  const addTableDrawingPoint = useCallback((point) => {
    setTableDrawingPoints(prev => [...prev, point]);
  }, []);
  
  // Shadow analysis management
  const toggleShadowVisibility = useCallback(() => {
    setShadowsVisible(prev => !prev);
  }, []);
  
  const updateShadowAnalysisParams = useCallback((params) => {
    if (params.analysisDate) setAnalysisDate(params.analysisDate);
    if (params.analysisDurationHours) setAnalysisDurationHours(params.analysisDurationHours);
  }, []);
  
  const clearShadows = useCallback(() => {
    setShadowLayers([]);
    setTotalShadedAreaOnRoof(0);
  }, []);
  
  // Drag state management
  const startDrag = useCallback((position) => {
    setIsDragging(true);
    setIsDragStarted(true);
    setDragStartPosition(position);
  }, []);
  
  const endDrag = useCallback(() => {
    setIsDragging(false);
    setIsDragStarted(false);
    setDragStartPosition(null);
  }, []);
  
  // Modal management
  const showObstacleHeightModal = useCallback((obstacleId) => {
    setShowHeightModalForObstacleId(obstacleId);
  }, []);
  
  const hideObstacleHeightModal = useCallback(() => {
    setShowHeightModalForObstacleId(null);
  }, []);
  
  const setPendingObstacleData = useCallback((obstacleData) => {
    setPendingObstacle(obstacleData);
  }, []);
  
  const clearPendingObstacle = useCallback(() => {
    setPendingObstacle(null);
  }, []);
  
  // Computed values
  const totalRoofArea = useMemo(() => mainRoofArea, [mainRoofArea]);
  const usableRoofArea = useMemo(() => 
    Math.max(0, totalRoofArea - totalShadedAreaOnRoof), 
    [totalRoofArea, totalShadedAreaOnRoof]
  );
  
  const shadowCoveragePercentage = useMemo(() => 
    totalRoofArea > 0 ? (totalShadedAreaOnRoof / totalRoofArea) * 100 : 0,
    [totalRoofArea, totalShadedAreaOnRoof]
  );
  
  // Performance metrics
  const getPerformanceMetrics = useCallback(() => ({
    lastStateUpdate: lastStateUpdateRef.current,
    pvTableMetrics: pvTableState.getPerformanceMetrics(),
    obstacleMetrics: obstacleState.getPerformanceMetrics(),
    shadowLayerCount: shadowLayers.length,
    totalRoofArea,
    usableRoofArea,
    shadowCoveragePercentage
  }), [
    pvTableState, 
    obstacleState, 
    shadowLayers.length, 
    totalRoofArea, 
    usableRoofArea, 
    shadowCoveragePercentage
  ]);
  
  // State persistence (optional)
  const exportState = useCallback(() => ({
    location,
    mode,
    analysisDate,
    analysisDurationHours,
    pvTables: pvTableState.pvTables,
    obstacles: obstacleState.obstacles,
    mainRoofArea,
    shadowsVisible,
    timestamp: Date.now()
  }), [
    location, 
    mode, 
    analysisDate, 
    analysisDurationHours, 
    pvTableState.pvTables, 
    obstacleState.obstacles, 
    mainRoofArea, 
    shadowsVisible
  ]);

  return {
    // Location and map
    location,
    selectedCoord,
    mapRef,
    updateLocation,
    setSelectedCoord,
    setMapRef,
    
    // Mode and interaction
    mode,
    MODES,
    setMapMode,
    drawingArea,
    drawTableMode,
    tableDrawingPoints,
    startDrawingArea,
    stopDrawingArea,
    startTableDrawing,
    stopTableDrawing,
    addTableDrawingPoint,
    
    // Main roof
    mainRoofLayer,
    mainRoofArea,
    totalRoofArea,
    usableRoofArea,
    setMainRoofLayer,
    setMainRoofArea,
    
    // Shadow analysis
    shadowLayers,
    totalShadedAreaOnRoof,
    shadowsVisible,
    isCalculatingShadows,
    analysisDate,
    analysisDurationHours,
    shadowCoveragePercentage,
    setShadowLayers,
    setTotalShadedAreaOnRoof,
    setIsCalculatingShadows,
    toggleShadowVisibility,
    updateShadowAnalysisParams,
    clearShadows,
    
    // Auto-placed PV
    autoPlacedPvFeatures,
    setAutoPlacedPvFeatures,
    
    // Drag state
    isDragging,
    isDragStarted,
    dragStartPosition,
    startDrag,
    endDrag,
    
    // Modal and UI
    pendingObstacle,
    showHeightModalForObstacleId,
    currentObstacleLayer,
    showObstacleHeightModal,
    hideObstacleHeightModal,
    setPendingObstacleData,
    clearPendingObstacle,
    setCurrentObstacleLayer,
    
    // Sub-state hooks
    pvTableState,
    obstacleState,
    
    // Utilities
    getPerformanceMetrics,
    exportState
  };
};

export default useMapState;
