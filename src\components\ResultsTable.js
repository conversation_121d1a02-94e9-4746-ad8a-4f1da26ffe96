import React from 'react';

const MONTHS = [
  'January', 'February', 'March', 'April', 'May', 'June',
  'July', 'August', 'September', 'October', 'November', 'December'
];

export default function ResultsTable({ results }) {
  if (!results) return null;
  return (
    <div className="tabular-results" style={{ marginTop: 24 }}>
      <h3>Monthly Breakdown</h3>
      <div style={{ overflowX: 'auto' }}>
        <table style={{ minWidth: 900 }}>
          <thead>
            <tr>
              <th>Month</th>
              <th>Consumption (kWh)</th>
              <th>Solar Production (kWh)</th>
              <th>Net Consumption (kWh)</th>
              <th>Bill Without Solar</th>
              <th>Bill With Solar</th>
              <th>Savings</th>
            </tr>
          </thead>
          <tbody>
            {MONTHS.map((month, i) => (
              <tr key={month}>
                <td>{month}</td>
                <td>{results.yearlyConsumptions[i]?.toFixed(2)}</td>
                <td>{results.monthlyProduction[i]?.toFixed(2)}</td>
                <td>{results.newConsumptions[i]?.toFixed(2)}</td>
                <td>{results.bills[i]?.toFixed(2)}</td>
                <td>{results.newBills[i]?.toFixed(2)}</td>
                <td style={{ color: results.savings[i] > 0 ? '#27ae60' : '#e74c3c', fontWeight: 600 }}>{results.savings[i]?.toFixed(2)}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
