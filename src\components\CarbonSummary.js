import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Line } from 'react-chartjs-2';
import { FaLeaf } from 'react-icons/fa';
import CarbonFactorsModal from './CarbonFactorsModal';

export default function CarbonSummary({ summary, financialInputs, setFinancialInputs, recalculateFinancials }) {
  const { t } = useTranslation();
  const [showCarbonModal, setShowCarbonModal] = useState(false);
  if (!summary) return null;
  
  // Create carbon savings details table data
  const tableData = [];
  const startYear = summary.startYear || new Date().getFullYear();
  
  if (summary.years && summary.carbonSavings && summary.cumulativeCarbonSavings) {
    for (let i = 0; i < Math.min(20, summary.years.length); i++) {
      tableData.push({
        year: startYear + i,
        yearlySavings: (summary.carbonSavings[i] || 0) / 1000,
        cumulativeSavings: (summary.cumulativeCarbonSavings[i] || 0) / 1000
      });
    }
  }
  
  return (
    <div className="results-section" style={{ marginTop: 24, background: 'var(--color-surface)', borderRadius: 8, padding: 16, width: '100%', maxWidth: '1200px', margin: '24px auto' }}>
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '10px', marginBottom: '10px' }}>
        <h2 style={{ textAlign: 'center', margin: 0 }}>{t('Carbon Savings')}</h2>
        {financialInputs && setFinancialInputs && (
          <button
            onClick={() => setShowCarbonModal(true)}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: '36px',
              height: '36px',
              padding: '0',
              background: '#16a34a',
              color: 'var(--color-text)',
              border: 'none',
              borderRadius: '50%',
              cursor: 'pointer',
              boxShadow: '0 2px 4px rgba(16, 185, 129, 0.2)'
            }}
            title={t('Carbon Emission Factors')}
          >
            <FaLeaf size={16} />
          </button>
        )}
      </div>
      <div style={{ 
        display: 'flex', 
        flexWrap: 'wrap', 
        gap: '24px', 
        marginBottom: '32px',
        justifyContent: 'center',
        padding: '20px',
        background: 'var(--color-surface-dark)',
        borderRadius: '12px',
        boxShadow: '0 2px 8px rgba(16, 185, 129, 0.1)',
        maxWidth: '800px',
        margin: '0 auto 32px auto'
      }}>
        <div style={{ 
          flex: '1 1 200px', 
          minWidth: '160px', 
          textAlign: 'center',
          padding: '16px',
          background: 'var(--color-surface)',
          borderRadius: '8px',
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)'
        }}>
          <div style={{ fontWeight: 'bold', color: 'var(--color-text)', marginBottom: '8px', fontSize: '16px' }}>{t('Total Carbon Savings')}</div>
          <div style={{ fontSize: '24px', fontWeight: '700' }}>{summary.totalCarbonSavings.toLocaleString()} <span style={{ fontSize: '16px' }}>tCO₂</span></div>
        </div>
        <div style={{ 
          flex: '1 1 200px', 
          minWidth: '160px', 
          textAlign: 'center',
          padding: '16px',
          background: 'var(--color-surface)',
          borderRadius: '8px',
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)'
        }}>
          <div style={{ fontWeight: 'bold', color: 'var(--color-text)', marginBottom: '8px', fontSize: '16px' }}>{t('Equivalent Trees Planted')}</div>
          <div style={{ fontSize: '24px', fontWeight: '700' }}>{summary.equivalentTrees} <span style={{ fontSize: '16px' }}>trees</span></div>
        </div>
        <div style={{ 
          flex: '1 1 200px', 
          minWidth: '160px', 
          textAlign: 'center',
          padding: '16px',
          background: 'var(--color-surface)',
          borderRadius: '8px',
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)'
        }}>
          <div style={{ fontWeight: 'bold', color: 'var(--color-text)', marginBottom: '8px', fontSize: '16px' }}>{t('Fuel Saved')}</div>
          <div style={{ fontSize: '24px', fontWeight: '700' }}>{summary.fuelSaved} <span style={{ fontSize: '16px' }}>liters</span></div>
        </div>
      </div>
      
      {/* Carbon Savings Details and Chart */}
      {tableData.length > 0 && (
        <>
          <h3>{t('Carbon Savings Details')}</h3>
          <div style={{ display: 'flex', gap: 0, alignItems: 'flex-end', flexWrap: 'wrap' }}>
            {/* Table on the left */}
            <div style={{ flex: 2, minWidth: 260, maxWidth: 600 }}>
              <div className="table-responsive" style={{ overflowX: 'auto', maxHeight: '500px', overflowY: 'auto' }}>
                <table style={{ width: '100%', borderCollapse: 'collapse', fontSize: '14px' }}>
                  <thead>
                    <tr style={{ background: 'var(--color-surface-dark)', color: 'var(--color-text)', position: 'sticky', top: 0, zIndex: 10 }}>
                      <th style={{ padding: '8px 12px', textAlign: 'left' }}>{t('Year')}</th>
                      <th style={{ padding: '8px 12px', textAlign: 'right' }}>{t('Yearly Carbon Savings (tCO₂)')}</th>
                      <th style={{ padding: '8px 12px', textAlign: 'right' }}>{t('Cumulative Carbon Savings (tCO₂)')}</th>
                    </tr>
                  </thead>
                  <tbody>
                    {tableData.map((row, index) => (
                      <tr key={row.year} style={{ background: index % 2 === 0 ? 'var(--color-surface-dark)' : 'var(--color-surface)', borderBottom: '1px solid var(--color-border)' }}>
                        <td style={{ padding: '8px 12px' }}>{row.year}</td>
                        <td style={{ padding: '8px 12px', textAlign: 'right', color: 'var(--color-text)' }}>
                          {row.yearlySavings.toFixed(2)}
                        </td>
                        <td style={{ padding: '8px 12px', textAlign: 'right', 
                            color: parseFloat(row.cumulativeSavings) < 0 ? '#ef4444' : '#16a34a', 
                            fontWeight: 'bold' }}>
                          {parseFloat(row.cumulativeSavings) < 0 ? '-' : ''}{Math.abs(parseFloat(row.cumulativeSavings)).toFixed(2)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
            
            {/* Chart on the right */}
            <div style={{ flex: 2, minWidth: 260, maxWidth: 600, alignSelf: 'flex-end', display: 'flex', flexDirection: 'column', justifyContent: 'flex-end' }}>
              <div className="chart-container" style={{ background: 'var(--color-surface)', borderRadius: 8, padding: 8, boxShadow: '0 2px 8px rgba(0,0,0,0.04)', width: '100%', marginTop: 16, height: 500, minHeight: 500, maxHeight: 500, overflow: 'hidden', display: 'flex', flexDirection: 'column', justifyContent: 'flex-end' }}>
                <div style={{ fontWeight: 700, fontSize: 16, textAlign: 'center', marginBottom: 4 }}>{t('Cumulative Carbon Savings')}</div>
                <div style={{ flex: 1, display: 'flex', alignItems: 'flex-end' }}>
                  <Line
                    data={{
                      labels: summary.years,
                      datasets: [
                        {
                          label: 'Cumulative Carbon Savings (tCO₂)',
                          data: summary.cumulativeCarbonSavings.map(v => v / 1000),
                          borderColor: '#2563eb',
                          backgroundColor: 'rgba(37,99,235,0.15)',
                          fill: true,
                          tension: 0.2,
                        },
                      ],
                    }}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: { 
                        legend: { position: 'top' } 
                      },
                      scales: {
                        y: {
                          title: { display: true, text: 'tCO₂' },
                          beginAtZero: true,
                          grid: {
                            color: (ctx) => ctx.tick.value === 0 ? '#94a3b8' : 'rgba(255,255,255,0.1)',
                          }
                        },
                        x: { title: { display: true, text: 'Year' } },
                      },
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        </>
      )}
      
      {/* Carbon Factors Modal */}
      <CarbonFactorsModal
        open={showCarbonModal}
        onClose={() => {
          setShowCarbonModal(false);
          if (recalculateFinancials) {
            recalculateFinancials(financialInputs);
          }
        }}
        values={financialInputs}
        setValues={setFinancialInputs}
      />
    </div>
  );
}
