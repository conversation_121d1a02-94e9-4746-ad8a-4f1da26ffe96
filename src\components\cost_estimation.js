/*
  Cost estimation utilities
  Implements linear forecast and cost estimation based on a lookup table.
*/

// Cost per kWp lookup (EGP)
const ESTIMATED_COST_KWP = {
  5: 25000,
  10: 23000,
  15: 22000,
  30: 19000,
  50: 18600,
  75: 18000,
  100: 17800,
  150: 17500,
  200: 17200,
  250: 17000,
  300: 16800,
  400: 16700,
  500: 16600,
  750: 16500,
  1000: 16300,
  1500: 16000,
  2000: 15800,
  3000: 15500,
  5000: 15400,
  10000: 15200,
  100000: 15000,
};

/**
 * Perform a simple linear regression forecast for y at point x.
 * Equivalent to Excel's FORECAST.LINEAR
 * @param {number} x - point to forecast y for
 * @param {number[]} knownXs - known x values
 * @param {number[]} knownYs - corresponding known y values
 * @returns {number}
 */
export function forecastLinear(x, knownXs, knownYs) {
  const n = knownXs.length;
  if (n < 2) {
    throw new Error('Need at least two data points to perform linear forecasting.');
  }
  const meanX = knownXs.reduce((a, b) => a + b, 0) / n;
  const meanY = knownYs.reduce((a, b) => a + b, 0) / n;

  // covariance and variance
  let covXY = 0;
  let varX = 0;
  for (let i = 0; i < n; i++) {
    covXY += (knownXs[i] - meanX) * (knownYs[i] - meanY);
    varX += (knownXs[i] - meanX) ** 2;
  }
  if (varX === 0) {
    throw new Error('Zero variance in input X values; cannot compute slope.');
  }
  const m = covXY / varX; // slope
  const b = meanY - m * meanX; // intercept
  return m * x + b;
}

/**
 * Estimate total system cost (EGP) for a given system peak power (kWp).
 * @param {number} systemPeakPower
 * @param {Object} [costTable=ESTIMATED_COST_KWP] - optional override of lookup table {kWp: costPerkWp}
 * @returns {number} estimated total cost (EGP)
 */
export function estimateCost(systemPeakPower, costTable = ESTIMATED_COST_KWP) {
  const parsedPower = Number(systemPeakPower);
  if (isNaN(parsedPower) || parsedPower <= 0) {
    throw new Error('Invalid system peak power supplied to estimateCost');
  }

  if (costTable[parsedPower] !== undefined) {
    return costTable[parsedPower] * parsedPower;
  }

  const xs = Object.keys(costTable).map(Number);
  const ys = xs.map(k => costTable[k]);

  const costPerKwp = forecastLinear(parsedPower, xs, ys);
  return costPerKwp * parsedPower;
}

// Default export convenience wrapper
export default estimateCost;
