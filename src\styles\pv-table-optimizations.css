/* PV Table Performance Optimizations */

/* Disable transitions during drag operations for better performance */
.no-transition {
  transition: none !important;
  animation: none !important;
}

/* Optimize PV table panel rendering */
.pv-table-panel {
  /* Use GPU acceleration for better performance */
  transform: translateZ(0);
  will-change: transform, opacity;
  
  /* Optimize rendering */
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Optimize cursor changes */
.pv-table-panel:hover {
  cursor: grab;
}

.pv-table-panel:active {
  cursor: grabbing;
}

/* Optimize selected state */
.pv-table-panel.selected {
  /* Use outline instead of border for better performance */
  outline: 2px dashed #ff0000;
  outline-offset: -2px;
}

/* Optimize dragging state */
.pv-table-panel.dragging {
  /* Reduce opacity and add slight blur for visual feedback */
  opacity: 0.7;
  filter: blur(0.5px);
  
  /* Disable pointer events during drag */
  pointer-events: none;
}

/* Performance optimizations for large numbers of PV tables */
@media (min-width: 1024px) {
  .pv-table-panel {
    /* Enable hardware acceleration on larger screens */
    transform: translate3d(0, 0, 0);
  }
}

/* Reduce visual complexity on mobile devices */
@media (max-width: 768px) {
  .pv-table-panel {
    /* Simplify rendering on mobile */
    filter: none;
    box-shadow: none;
  }
}

/* Optimize for high DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .pv-table-panel {
    /* Ensure crisp rendering on high DPI displays */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}
