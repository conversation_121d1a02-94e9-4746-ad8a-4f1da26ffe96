import React, { memo, useMemo } from 'react';
import PVTablePanel from './PVTablePanel';
import { useViewportOptimization } from '../../../hooks/useViewportOptimization';

/**
 * Optimized PV Table Renderer Component
 * Handles efficient rendering of PV tables with viewport optimization
 */
const PVTableRenderer = memo(({
  pvTables = [],
  selectedTableId,
  draggedTableId,
  mapRef,
  onTableClick,
  onTableMouseDown,
  onTableMouseEnter,
  onTableMouseLeave,
  enableViewportOptimization = true,
  maxTablesPerFrame = 100
}) => {
  // Viewport optimization for large numbers of tables
  const { visibleItems: visibleTables } = useViewportOptimization(
    mapRef,
    pvTables,
    {
      enableVirtualization: enableViewportOptimization && pvTables.length > 50,
      maxItemsToRender: maxTablesPerFrame,
      bufferDistance: 0.002 // Slightly larger buffer for PV tables
    }
  );

  // Memoize table styles to prevent unnecessary recalculations
  const getTableStyle = useMemo(() => {
    const styleCache = new Map();
    
    return (tableId, isSelected, isDragging) => {
      const cacheKey = `${tableId}-${isSelected}-${isDragging}`;
      
      if (styleCache.has(cacheKey)) {
        return styleCache.get(cacheKey);
      }
      
      const style = {
        color: isDragging ? '#fbbf24' : 'black',
        weight: isDragging ? 3 : 2,
        fillColor: isSelected ? '#ff0000' : (isDragging ? '#fbbf24' : '#007bff'),
        fillOpacity: isDragging ? 0.7 : 1,
        dashArray: isDragging ? '5,5' : null,
        opacity: 1
      };
      
      styleCache.set(cacheKey, style);
      return style;
    };
  }, []);

  // Memoize event handlers to prevent unnecessary re-renders
  const createEventHandlers = useMemo(() => {
    const handlerCache = new Map();
    
    return (tableId) => {
      if (handlerCache.has(tableId)) {
        return handlerCache.get(tableId);
      }
      
      const handlers = {
        click: (e) => {
          e.originalEvent.stopPropagation();
          onTableClick?.(tableId, e);
        },
        mousedown: (e) => {
          e.originalEvent.stopPropagation();
          onTableMouseDown?.(tableId, e);
        },
        mouseenter: (e) => {
          onTableMouseEnter?.(tableId, e);
        },
        mouseleave: (e) => {
          onTableMouseLeave?.(tableId, e);
        }
      };
      
      handlerCache.set(tableId, handlers);
      return handlers;
    };
  }, [onTableClick, onTableMouseDown, onTableMouseEnter, onTableMouseLeave]);

  // Render optimized PV tables
  const renderedTables = useMemo(() => {
    return visibleTables.map(table => {
      if (!table.rectangles || table.rectangles.length === 0) {
        return null;
      }

      const isSelected = table.id === selectedTableId;
      const isDragging = table.id === draggedTableId;
      const tableStyle = getTableStyle(table.id, isSelected, isDragging);
      const eventHandlers = createEventHandlers(table.id);

      // Render all panels for this table
      return table.rectangles.map((rectangle, panelIndex) => (
        <PVTablePanel
          key={`${table.id}-panel-${panelIndex}`}
          rectangle={rectangle}
          tableId={table.id}
          panelIndex={panelIndex}
          isSelected={isSelected}
          isDragging={isDragging}
          eventHandlers={eventHandlers}
          style={tableStyle}
        />
      ));
    }).filter(Boolean).flat();
  }, [
    visibleTables,
    selectedTableId,
    draggedTableId,
    getTableStyle,
    createEventHandlers
  ]);

  // Performance monitoring (development only)
  if (process.env.NODE_ENV === 'development') {
    console.log(`[PVTableRenderer] Rendering ${renderedTables.length} panels from ${visibleTables.length}/${pvTables.length} tables`);

    // Debug table properties for rotation issues
    visibleTables.forEach(table => {
      if (table.id === selectedTableId) {
        console.log(`[PVTableRenderer] Selected table ${table.id} - azimuth: ${table.properties?.azimuth}, lastModified: ${table.lastModified}`);
      }
    });
  }

  return <>{renderedTables}</>;
}, (prevProps, nextProps) => {
  // Custom comparison for optimal re-rendering
  const propsToCompare = [
    'selectedTableId',
    'draggedTableId',
    'enableViewportOptimization',
    'maxTablesPerFrame'
  ];
  
  // Check if simple props changed
  for (const prop of propsToCompare) {
    if (prevProps[prop] !== nextProps[prop]) {
      return false;
    }
  }
  
  // Deep comparison for pvTables array
  if (prevProps.pvTables.length !== nextProps.pvTables.length) {
    return false;
  }
  
  // Check if any table has changed (comprehensive comparison)
  for (let i = 0; i < prevProps.pvTables.length; i++) {
    const prevTable = prevProps.pvTables[i];
    const nextTable = nextProps.pvTables[i];

    if (
      prevTable.id !== nextTable.id ||
      prevTable.selected !== nextTable.selected ||
      prevTable.rectangles?.length !== nextTable.rectangles?.length ||
      JSON.stringify(prevTable.position) !== JSON.stringify(nextTable.position) ||
      JSON.stringify(prevTable.properties) !== JSON.stringify(nextTable.properties) ||
      prevTable.lastModified !== nextTable.lastModified
    ) {
      return false;
    }

    // Deep comparison of rectangles if they exist (for rotation changes)
    if (prevTable.rectangles && nextTable.rectangles) {
      for (let j = 0; j < prevTable.rectangles.length; j++) {
        if (JSON.stringify(prevTable.rectangles[j]) !== JSON.stringify(nextTable.rectangles[j])) {
          return false;
        }
      }
    }
  }
  
  return true;
});

PVTableRenderer.displayName = 'PVTableRenderer';

export default PVTableRenderer;
