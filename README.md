# Solar PV React App

This is a React-based rewrite of the Solar PV Savings Calculator web app.

## Features
- Location selection with map (Leaflet)
- System parameters input
- Consumption and billing input
- Results with charts and tables
- Financial and carbon calculations

## Getting Started

1. Install dependencies:
   ```bash
   npm install
   ```
2. Start the development server:
   ```bash
   npm start
   ```

## Dependencies
- React
- React DOM
- React Scripts
- React Leaflet
- Leaflet
- React Chart.js 2
- Chart.js
