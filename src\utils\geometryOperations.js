/**
 * Geometry Operations Utility
 * 
 * Provides robust geometric operations for shadow calculations using polygon-clipping library.
 * Replaces martinez-polygon-clipping with more reliable implementation.
 */

import polygonClipping from 'polygon-clipping';
import { polygon as turfPolygon, point as turfPoint } from '@turf/helpers';
import turfArea from '@turf/area';
import turfBbox from '@turf/bbox';
import turfBooleanContains from '@turf/boolean-contains';
import turfConvex from '@turf/convex';

/**
 * Custom error class for geometry operations
 */
export class GeometryOperationError extends Error {
  constructor(message, originalError = null) {
    super(message);
    this.name = 'GeometryOperationError';
    this.originalError = originalError;
  }
}

/**
 * Convert from Leaflet [lat, lng] to polygon-clipping [lng, lat] format
 * @param {Array} leafletCoords - Array of [lat, lng] coordinates
 * @returns {Array} Polygon in polygon-clipping format
 */
export const convertToPolygonClippingFormat = (leafletCoords) => {
  if (!validatePolygon(leafletCoords)) {
    throw new GeometryOperationError('Invalid polygon coordinates for conversion');
  }
  
  // Convert from Leaflet [lat, lng] to polygon-clipping [lng, lat]
  return [leafletCoords.map(coord => [coord[1], coord[0]])];
};

/**
 * Convert from polygon-clipping format back to Leaflet format
 * @param {Array} polygonClippingResult - Result from polygon-clipping operation
 * @returns {Array} Array of polygons in Leaflet format
 */
export const convertFromPolygonClippingFormat = (polygonClippingResult) => {
  if (!polygonClippingResult || !Array.isArray(polygonClippingResult)) {
    return [];
  }
  
  try {
    // Handle MultiPolygon result
    if (Array.isArray(polygonClippingResult[0][0][0])) {
      return polygonClippingResult.map(polygon => 
        polygon[0].map(coord => [coord[1], coord[0]])
      );
    }
    
    // Handle single Polygon result
    return [polygonClippingResult[0].map(coord => [coord[1], coord[0]])];
  } catch (error) {
    console.error('Error converting from polygon-clipping format:', error);
    throw new GeometryOperationError('Format conversion failed', error);
  }
};

/**
 * Perform union operation on multiple polygons
 * @param {Array} polygons - Array of polygons in Leaflet format
 * @returns {Array} Array of merged polygons in Leaflet format
 */
export const unionPolygons = (polygons) => {
  if (!polygons || !Array.isArray(polygons) || polygons.length === 0) {
    return [];
  }
  
  if (polygons.length === 1) {
    return [polygons[0]];
  }
  
  try {
    // Validate and convert all polygons
    const validPolygons = polygons.filter(validatePolygon);
    
    if (validPolygons.length === 0) {
      throw new GeometryOperationError('No valid polygons for union operation');
    }
    
    // Convert to polygon-clipping format
    const formatted = validPolygons.map(convertToPolygonClippingFormat);
    
    // Perform union operation
    const result = polygonClipping.union(...formatted);
    
    // Convert back to Leaflet format
    return convertFromPolygonClippingFormat(result);
  } catch (error) {
    console.error('Union operation failed:', error);
    throw new GeometryOperationError('Union operation failed', error);
  }
};

/**
 * Perform intersection operation between two polygons
 * @param {Array} polygon1 - First polygon in Leaflet format
 * @param {Array} polygon2 - Second polygon in Leaflet format
 * @returns {Array} Array of intersection polygons in Leaflet format
 */
export const intersectPolygons = (polygon1, polygon2) => {
  if (!validatePolygon(polygon1) || !validatePolygon(polygon2)) {
    throw new GeometryOperationError('Invalid polygons for intersection operation');
  }
  
  try {
    const p1 = convertToPolygonClippingFormat(polygon1);
    const p2 = convertToPolygonClippingFormat(polygon2);
    
    const result = polygonClipping.intersection(p1, p2);
    
    return convertFromPolygonClippingFormat(result);
  } catch (error) {
    console.error('Intersection operation failed:', error);
    throw new GeometryOperationError('Intersection operation failed', error);
  }
};

/**
 * Perform difference operation between two polygons
 * @param {Array} polygon1 - First polygon in Leaflet format
 * @param {Array} polygon2 - Second polygon in Leaflet format
 * @returns {Array} Array of difference polygons in Leaflet format
 */
export const differencePolygons = (polygon1, polygon2) => {
  if (!validatePolygon(polygon1) || !validatePolygon(polygon2)) {
    throw new GeometryOperationError('Invalid polygons for difference operation');
  }
  
  try {
    const p1 = convertToPolygonClippingFormat(polygon1);
    const p2 = convertToPolygonClippingFormat(polygon2);
    
    const result = polygonClipping.difference(p1, p2);
    
    return convertFromPolygonClippingFormat(result);
  } catch (error) {
    console.error('Difference operation failed:', error);
    throw new GeometryOperationError('Difference operation failed', error);
  }
};

/**
 * Validate polygon coordinates
 * @param {Array} coordinates - Array of [lat, lng] coordinates
 * @returns {boolean} True if valid polygon
 */
export const validatePolygon = (coordinates) => {
  if (!Array.isArray(coordinates) || coordinates.length < 3) {
    return false;
  }
  
  // Check for valid coordinate pairs
  return coordinates.every(coord => 
    Array.isArray(coord) && 
    coord.length === 2 && 
    typeof coord[0] === 'number' && 
    typeof coord[1] === 'number' &&
    !isNaN(coord[0]) && 
    !isNaN(coord[1])
  );
};

/**
 * Calculate polygon area in square meters
 * @param {Array} coordinates - Array of [lat, lng] coordinates
 * @returns {number} Area in square meters
 */
export const calculatePolygonArea = (coordinates) => {
  if (!validatePolygon(coordinates)) {
    throw new GeometryOperationError('Invalid polygon for area calculation');
  }
  
  try {
    // Convert to GeoJSON format for Turf.js
    const turfPoly = turfPolygon([coordinates.map(coord => [coord[1], coord[0]])]);
    return turfArea(turfPoly);
  } catch (error) {
    console.error('Area calculation failed:', error);
    throw new GeometryOperationError('Area calculation failed', error);
  }
};

/**
 * Calculate bounding box for polygon
 * @param {Array} coordinates - Array of [lat, lng] coordinates
 * @returns {Object} Bounding box {minLat, minLng, maxLat, maxLng}
 */
export const calculateBoundingBox = (coordinates) => {
  if (!validatePolygon(coordinates)) {
    throw new GeometryOperationError('Invalid polygon for bounding box calculation');
  }
  
  try {
    // Convert to GeoJSON format for Turf.js
    const turfPoly = turfPolygon([coordinates.map(coord => [coord[1], coord[0]])]);
    const bbox = turfBbox(turfPoly);
    
    return {
      minLng: bbox[0],
      minLat: bbox[1],
      maxLng: bbox[2],
      maxLat: bbox[3]
    };
  } catch (error) {
    console.error('Bounding box calculation failed:', error);
    throw new GeometryOperationError('Bounding box calculation failed', error);
  }
};

/**
 * Check if a point is inside a polygon
 * @param {Array} point - [lat, lng] coordinates
 * @param {Array} polygon - Array of [lat, lng] coordinates
 * @returns {boolean} True if point is inside polygon
 */
export const isPointInPolygon = (point, polygon) => {
  if (!Array.isArray(point) || point.length !== 2 || !validatePolygon(polygon)) {
    return false;
  }
  
  try {
    const turfP = turfPoint([point[1], point[0]]);
    const turfPoly = turfPolygon([polygon.map(coord => [coord[1], coord[0]])]);
    
    return turfBooleanContains(turfPoly, turfP);
  } catch (error) {
    console.error('Point in polygon check failed:', error);
    return false;
  }
};

/**
 * Create convex hull from array of points
 * @param {Array} points - Array of [lat, lng] points
 * @returns {Array} Convex hull polygon in Leaflet format
 */
export const createConvexHull = (points) => {
  if (!Array.isArray(points) || points.length < 3) {
    throw new GeometryOperationError('Insufficient points for convex hull');
  }
  
  try {
    // Convert to GeoJSON format for Turf.js
    const features = points.map(point => turfPoint([point[1], point[0]]));
    const featureCollection = { type: 'FeatureCollection', features };
    
    const hull = turfConvex(featureCollection);
    
    if (!hull || !hull.geometry || !hull.geometry.coordinates || !hull.geometry.coordinates[0]) {
      throw new GeometryOperationError('Failed to generate convex hull');
    }
    
    // Convert back to Leaflet format
    return hull.geometry.coordinates[0].map(coord => [coord[1], coord[0]]);
  } catch (error) {
    console.error('Convex hull creation failed:', error);
    throw new GeometryOperationError('Convex hull creation failed', error);
  }
};

export default {
  unionPolygons,
  intersectPolygons,
  differencePolygons,
  validatePolygon,
  calculatePolygonArea,
  calculateBoundingBox,
  isPointInPolygon,
  createConvexHull,
  convertToPolygonClippingFormat,
  convertFromPolygonClippingFormat
};
