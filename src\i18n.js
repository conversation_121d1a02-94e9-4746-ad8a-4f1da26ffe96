import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// Import translation resources
import en from './locales/en/translation.json';
import ar from './locales/ar/translation.json';

const resources = {
  en: { translation: en },
  ar: { translation: ar },
};

// Retrieve previously-chosen language (default to English)
const storedLang = localStorage.getItem('lang') || 'en';

// Initialise i18next
i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: storedLang,
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false, // React already escapes
    },
  });

// Handle document direction (LTR / RTL)
const setDir = (lng) => {
  document.documentElement.dir = lng === 'ar' ? 'rtl' : 'ltr';
};
setDir(storedLang);

i18n.on('languageChanged', (lng) => {
  localStorage.setItem('lang', lng);
  setDir(lng);
});

export default i18n;
