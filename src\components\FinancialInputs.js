import React, { useState } from 'react';
import { FaLeaf } from 'react-icons/fa';
import CarbonFactorsModal from './CarbonFactorsModal';

export default function FinancialInputs({ values, setValues }) {
  const [carbonModalOpen, setCarbonModalOpen] = useState(false);
  return (
    <>
    <div className="input-section" style={{ marginTop: 24, background: 'var(--color-surface)', borderRadius: 8, padding: 16 }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h2>Financial Parameters</h2>
        <button 
          onClick={() => setCarbonModalOpen(true)}
          data-carbon-button="true"
          style={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: '8px', 
            padding: '8px 16px', 
            background: '#10b981', 
            color: 'var(--color-text)', 
            border: 'none', 
            borderRadius: '6px',
            fontSize: '14px',
            fontWeight: '600',
            cursor: 'pointer',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            transition: 'all 0.2s ease'
          }}
        >
          <FaLeaf /> Carbon Emission Factors
        </button>
      </div>
      <div style={{ display: 'flex', flexWrap: 'wrap', gap: 24 }}>
        <div>
          <label>Total Cost (EGP):</label><br />
          <input type="number" min="0" value={values.totalCost} onChange={e => setValues(v => ({ ...v, totalCost: e.target.value }))} />
        </div>
        <div>
          <label>First Year Maintenance (EGP):</label><br />
          <input type="number" min="0" value={values.firstYearMaintenance} onChange={e => setValues(v => ({ ...v, firstYearMaintenance: e.target.value }))} />
        </div>
        <div>
          <label>Maintenance Annual Increase (%):</label><br />
          <input type="number" min="0" value={values.maintenanceAnnualIncrease} onChange={e => setValues(v => ({ ...v, maintenanceAnnualIncrease: e.target.value }))} />
        </div>
        <div>
          <label>Yearly Tariff Increase Rate (%):</label><br />
          <input type="number" min="0" value={values.yearlyTariffIncreaseRate} onChange={e => setValues(v => ({ ...v, yearlyTariffIncreaseRate: e.target.value }))} />
        </div>
        <div>
          <label>Yearly Production Degradation Rate (%):</label><br />
          <input 
            type="number" 
            min="0" 
            max="100" 
            step="0.01" 
            value={values.yearlyDegradationRate || 0.5} 
            onChange={e => setValues(v => ({ ...v, yearlyDegradationRate: e.target.value }))} 
          />
        </div>
        <div>
          <label>Start Year:</label><br />
          <input 
            type="number" 
            min="2024" 
            max="2100" 
            value={values.startYear || new Date().getFullYear()} 
            onChange={e => setValues(v => ({ ...v, startYear: e.target.value }))} 
          />
        </div>
        <div>
          <label>Project Years:</label><br />
          <input type="number" min="1" value={values.projectYears} onChange={e => setValues(v => ({ ...v, projectYears: e.target.value }))} />
        </div>

      </div>
    </div>
    <CarbonFactorsModal 
      open={carbonModalOpen} 
      onClose={() => setCarbonModalOpen(false)} 
      values={values} 
      setValues={setValues} 
    />
  </>
  );
}
