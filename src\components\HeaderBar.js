import React, { useEffect, useState } from 'react';
import { Fa<PERSON>un, <PERSON>a<PERSON><PERSON>, FaHome } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';

/**
 * HeaderBar component renders the top application bar containing the title and a
 * light/dark theme toggle switch. The component toggles a `light-theme` CSS
 * class on the document root (<html>) element which swaps CSS variables defined
 * in index.css. The current theme preference is persisted in localStorage so it
 * is remembered across page reloads.
 */
export default function HeaderBar() {
  const { t, i18n } = useTranslation();
  // True means the user selected light mode (light-theme class applied)
  const [isLight, setIsLight] = useState(false);

  // Initialise theme on first render from localStorage
  useEffect(() => {
    const stored = localStorage.getItem('theme');
    if (stored === 'light') {
      document.documentElement.classList.add('light-theme');
      setIsLight(true);
    }
  }, []);

  // Handler to toggle theme
  const handleToggle = () => {
    setIsLight(prev => {
      const next = !prev;
      if (next) {
        document.documentElement.classList.add('light-theme');
        localStorage.setItem('theme', 'light');
      } else {
        document.documentElement.classList.remove('light-theme');
        localStorage.setItem('theme', 'dark');
      }
      return next;
    });
  };

  return (
    <header
      className="app-header"
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        background: 'var(--color-surface-dark)',
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        zIndex: 1000,
        padding: '12px 24px',
        borderBottom: '1px solid var(--color-border)',
        // Force LTR layout for header regardless of document direction
        direction: 'ltr'
      }}
    >
      <div style={{display:'flex', alignItems:'center', gap:12}}>
        <button onClick={() => window.scrollTo({top:0, behavior:'smooth'})}
          style={{background:'none', border:'none', cursor:'pointer', color:'var(--color-text)'}}
          title="Home"
        >
          <FaHome size={22}/>
        </button>
        <h1 style={{
          margin: 0,
          fontSize: '1.5rem',
          color: 'var(--color-text)',
          // Allow text content to follow document direction for proper RTL text rendering
          direction: 'inherit'
        }}>
          {t('Solar PV Savings Calculator')}
        </h1>
      </div>

      <div style={{display:'flex', gap:12}}>
        <button
          onClick={() => i18n.changeLanguage(i18n.language === 'en' ? 'ar' : 'en')}
          style={{
            background: 'none',
            border: 'none',
            cursor: 'pointer',
            color: 'var(--color-text)',
            // Allow button text to follow document direction for proper RTL text rendering
            direction: 'inherit'
          }}
          title={t('Toggle Language')}
        >
          {i18n.language === 'ar' ? 'En' : 'ع'}
        </button>
        <button
          onClick={handleToggle}
          style={{ background: 'none', border: 'none', cursor: 'pointer', color: 'var(--color-text)' }}
          title={t('Toggle Light / Dark')}
        >
          {isLight ? <FaSun size={22}/> : <FaMoon size={22}/>}
        </button>
      </div>
    </header>
  );
}
